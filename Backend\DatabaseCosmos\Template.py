import logging
import random
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from utilities.CreateGuid import generate_guide
from Config.cosmosdb import database
from DatabaseCosmos.UpdatingCheckboxAndCheckboxWithGrouping import upsertUserSelectedData
# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="Template",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)

""" - Parameters
chat_id = 123(guid),
modules_list = []
user_id:123(guid)
"""
def insert_Template(template, user_id: str):
    logging.info("Inserting row in Template")
    print("Inserting row in Template")
    try:
        guid=generate_guide()
        item = {
                'id': guid,
                'user_id': user_id,
                'template':template
                
            }
        container.create_item(body=item)
        print(f'Created new item with Id {guid}')
        return item
        
    except Exception as e:
         print("Error" ,e)
        
         print("Read Failed!")
         return None       

def read_Template(user_id):
    print("reading Template")
    try:
        logging.info('Applying query.')
        query = f"SELECT * FROM c WHERE c.user_id = '{user_id}'"
        
        item= container.query_items(query = query, enable_cross_partition_query = True)
        
        print(f"Retrieved (query) .",item)

        return list(item)[0]
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
    
        return None

def updateTemplate(user_id,template):
    
    try:
        print("checking row exist or not!")
        query = f"SELECT * FROM c WHERE c.user_id = '{user_id}'"
        
        items = list(container.query_items(query=query, enable_cross_partition_query=True))
        items['template'] = template
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         

        