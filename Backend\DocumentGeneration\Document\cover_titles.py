from docx import Document
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
import datetime
import json
from typing import Dict, List, Optional
import requests
from io import BytesIO

class ContentControlMapper:
    KEY_MAPPINGS = {
        'Subject': 'Subtitle',
        'Publish Date': 'Date'
    }
    
    @staticmethod
    def map_key(key: str) -> str:
        return ContentControlMapper.KEY_MAPPINGS.get(key, key)

class WordContentController:
    def __init__(self, doc_path: str):
        self.doc_path = doc_path
        # self.doc = Document(doc_path)
        # to read data from url. 
        response = requests.get(doc_path)
        response.raise_for_status()  # Raise an error if the request fails
        self.doc = Document(BytesIO(response.content))

    def _find_all_sdts(self, element, found_sdts=None) -> List:
        if found_sdts is None:
            found_sdts = []
        
        if hasattr(element, 'tag') and element.tag.endswith('}sdt'):
            found_sdts.append(element)
        
        if hasattr(element, 'find'):
            txbx_content = element.find('.//' + qn('w:txbxContent'))
            if txbx_content is not None:
                for sdt in txbx_content.findall('.//' + qn('w:sdt')):
                    found_sdts.append(sdt)
        
        for child in element:
            self._find_all_sdts(child, found_sdts)
        
        return found_sdts

    def _create_run_with_properties(self, text: str, original_run) -> OxmlElement:
        new_run = OxmlElement('w:r')
        
        if original_run is not None:
            original_rPr = original_run.find(qn('w:rPr'))
            if original_rPr is not None:
                new_rPr = original_rPr
                new_run.append(new_rPr)
        
        new_t = OxmlElement('w:t')
        new_t.set(qn('xml:space'), 'preserve')
        new_t.text = text
        new_run.append(new_t)
        
        return new_run

    def _update_sdt_content(self, sdt, new_text: str) -> bool:
        try:
            # Remove data binding
            sdt_pr = sdt.find(qn('w:sdtPr'))
            if sdt_pr is not None:
                data_binding = sdt_pr.find(qn('w:dataBinding'))
                if data_binding is not None:
                    sdt_pr.remove(data_binding)
            
            sdt_content = sdt.find(qn('w:sdtContent'))
            if sdt_content is None:
                return False
            
            p = sdt_content.find(qn('w:p'))
            if p is None:
                p = OxmlElement('w:p')
                sdt_content.append(p)
            
            p_pr = p.find(qn('w:pPr'))
            
            original_run = None
            for run in p.findall(qn('w:r')):
                if run.find(qn('w:rPr')) is not None:
                    original_run = run
                    break
            
            # Clear content
            for child in list(sdt_content):
                sdt_content.remove(child)
            
            new_p = OxmlElement('w:p')
            if p_pr is not None:
                new_p.append(p_pr)
            
            new_run = self._create_run_with_properties(new_text, original_run)
            new_p.append(new_run)
            sdt_content.append(new_p)
            
            return True
        except Exception as e:
            print(f"Error updating content: {str(e)}")
            return False

    def get_current_values(self) -> Dict[str, str]:
        body = self.doc._element.body
        all_sdts = self._find_all_sdts(body)
        current_values = {}
        
        for sdt in all_sdts:
            sdt_pr = sdt.find(qn('w:sdtPr'))
            if sdt_pr is not None:
                alias = sdt_pr.find(qn('w:alias'))
                tag = sdt_pr.find(qn('w:tag'))
                
                alias_val = alias.get(qn('w:val')) if alias is not None else None
                tag_val = tag.get(qn('w:val')) if tag is not None else None
                
                identifier = alias_val or tag_val
                if identifier:
                    text_parts = []
                    sdt_content = sdt.find(qn('w:sdtContent'))
                    if sdt_content is not None:
                        for t in sdt_content.iter(qn('w:t')):
                            if t.text:
                                text_parts.append(t.text)
                    text = ' '.join(text_parts)
                    mapped_key = ContentControlMapper.map_key(identifier)
                    current_values[mapped_key] = text
        
        return current_values
    
    def update_content_controls(self, new_values: Dict[str, str]) -> List[str]:

        new_values = process_date_fields(new_values)
        body = self.doc._element.body
        all_sdts = self._find_all_sdts(body)
        modified_controls = []
        
        for sdt in all_sdts:
            try:
                sdt_pr = sdt.find(qn('w:sdtPr'))
                if sdt_pr is None:
                    continue
                
                alias = sdt_pr.find(qn('w:alias'))
                tag = sdt_pr.find(qn('w:tag'))
                
                alias_val = alias.get(qn('w:val')) if alias is not None else None
                tag_val = tag.get(qn('w:val')) if tag is not None else None
                
                identifier = alias_val or tag_val
                if identifier:
                    mapped_key = ContentControlMapper.map_key(identifier)
                    
                    if mapped_key in new_values:
                        success = self._update_sdt_content(sdt, new_values[mapped_key])
                        if success:
                            modified_controls.append(mapped_key)
            except Exception as e:
                print(f"Error processing control: {str(e)}")
                continue
        
        
        return self.doc


class WordContentController2:
    def __init__(self, doc_path: str):
        self.doc = Document(doc_path)
        

    def _find_all_sdts(self, element, found_sdts=None) -> List:
        if found_sdts is None:
            found_sdts = []
        
        if hasattr(element, 'tag') and element.tag.endswith('}sdt'):
            found_sdts.append(element)
        
        if hasattr(element, 'find'):
            txbx_content = element.find('.//' + qn('w:txbxContent'))
            if txbx_content is not None:
                for sdt in txbx_content.findall('.//' + qn('w:sdt')):
                    found_sdts.append(sdt)
        
        for child in element:
            self._find_all_sdts(child, found_sdts)
        
        return found_sdts

    def _create_run_with_properties(self, text: str, original_run) -> OxmlElement:
        new_run = OxmlElement('w:r')
        
        if original_run is not None:
            original_rPr = original_run.find(qn('w:rPr'))
            if original_rPr is not None:
                new_rPr = original_rPr
                new_run.append(new_rPr)
        
        new_t = OxmlElement('w:t')
        new_t.set(qn('xml:space'), 'preserve')
        new_t.text = text
        new_run.append(new_t)
        
        return new_run

    def _update_sdt_content(self, sdt, new_text: str) -> bool:
        try:
            # Remove data binding
            sdt_pr = sdt.find(qn('w:sdtPr'))
            if sdt_pr is not None:
                data_binding = sdt_pr.find(qn('w:dataBinding'))
                if data_binding is not None:
                    sdt_pr.remove(data_binding)
            
            sdt_content = sdt.find(qn('w:sdtContent'))
            if sdt_content is None:
                return False
            
            p = sdt_content.find(qn('w:p'))
            if p is None:
                p = OxmlElement('w:p')
                sdt_content.append(p)
            
            p_pr = p.find(qn('w:pPr'))
            
            original_run = None
            for run in p.findall(qn('w:r')):
                if run.find(qn('w:rPr')) is not None:
                    original_run = run
                    break
            
            # Clear content
            for child in list(sdt_content):
                sdt_content.remove(child)
            
            new_p = OxmlElement('w:p')
            if p_pr is not None:
                new_p.append(p_pr)
            
            new_run = self._create_run_with_properties(new_text, original_run)
            new_p.append(new_run)
            sdt_content.append(new_p)
            
            return True
        except Exception as e:
            print(f"Error updating content: {str(e)}")
            return False

    def get_current_values(self) -> Dict[str, str]:
        body = self.doc._element.body
        all_sdts = self._find_all_sdts(body)
        current_values = {}
        
        for sdt in all_sdts:
            sdt_pr = sdt.find(qn('w:sdtPr'))
            if sdt_pr is not None:
                alias = sdt_pr.find(qn('w:alias'))
                tag = sdt_pr.find(qn('w:tag'))
                
                alias_val = alias.get(qn('w:val')) if alias is not None else None
                tag_val = tag.get(qn('w:val')) if tag is not None else None
                
                identifier = alias_val or tag_val
                if identifier:
                    text_parts = []
                    sdt_content = sdt.find(qn('w:sdtContent'))
                    if sdt_content is not None:
                        for t in sdt_content.iter(qn('w:t')):
                            if t.text:
                                text_parts.append(t.text)
                    text = ' '.join(text_parts)
                    mapped_key = ContentControlMapper.map_key(identifier)
                    current_values[mapped_key] = text
        
        return current_values
    
    def update_content_controls(self, new_values: Dict[str, str]) -> List[str]:

        new_values = process_date_fields(new_values)
        body = self.doc._element.body
        all_sdts = self._find_all_sdts(body)
        modified_controls = []
        
        for sdt in all_sdts:
            try:
                sdt_pr = sdt.find(qn('w:sdtPr'))
                if sdt_pr is None:
                    continue
                
                alias = sdt_pr.find(qn('w:alias'))
                tag = sdt_pr.find(qn('w:tag'))
                
                alias_val = alias.get(qn('w:val')) if alias is not None else None
                tag_val = tag.get(qn('w:val')) if tag is not None else None
                
                identifier = alias_val or tag_val
                if identifier:
                    mapped_key = ContentControlMapper.map_key(identifier)
                    
                    if mapped_key in new_values:
                        success = self._update_sdt_content(sdt, new_values[mapped_key])
                        if success:
                            modified_controls.append(mapped_key)
            except Exception as e:
                print(f"Error processing control: {str(e)}")
                continue
        
        
        return self.doc


def process_date_fields(data: Dict[str, str]) -> Dict[str, str]:
    for key in data:
        if key.lower().endswith('date'):
            data[key] = datetime.datetime.now().strftime("%d-%b-%y")
    return data