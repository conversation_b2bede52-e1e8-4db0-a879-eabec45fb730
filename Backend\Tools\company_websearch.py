import os

from langchain_core.tools import Tool
from langchain_google_community import GoogleSearchAP<PERSON>Wrapper

from langchain_core.tools import tool
from dotenv import load_dotenv

load_dotenv() 

os.environ["GOOGLE_CSE_ID"] = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_SEARCH_API_KEY")


@tool(return_direct=True)
def companySearchWebTool(query:str)-> tuple[str, str]:
    """Search company on web. Give details like What is the company type, its major clients, product type, email id, phone number, Number of employees, headquarters address, company revenue, main products, buisness module, headquarters address, subsidiaries, industry name and type, SIC_Code, its linkedin account link, website link and any other information avaiable."""
    # search = GoogleSearchAPIWrapper()

    # google_search_tool = Tool(
    #     name="google_search",
    #     description="Search Google for company name and information, headquarters address, number of employees, subsidiaries, industry name and type, SIC_Code, phone numbers",
    #     func=search.results,
    # )
    
    search = GoogleSearchAPIWrapper()
    # Perform the search to get raw data for artifact
    raw_results = search.results(query, num_results=5)

    # Summarize content as a simple readable format for the LLM
    content = "\n".join(
        f"{result['title']}: {result['snippet']}" for result in raw_results
    )

    # Return both content and artifact (raw data)
    artifact = raw_results  # Raw JSON or structured response for deeper access
    
    return content, artifact