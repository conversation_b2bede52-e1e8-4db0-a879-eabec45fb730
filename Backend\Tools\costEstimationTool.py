from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
from DatabaseCosmos.ProjectEstimation import read_ProjectEstimation_State, insert_ProjectEstimation_State, update_ProjectEstimation
from DatabaseCosmos.Company_info_State import read_CompanyInfo
from Tools.suggestPills import getSuggestedPills
from Config.blobService import blob_service_client
import pandas as pd
import os
import io

@tool(return_direct=True)
def createCostEstimationTool(user_query:str, user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool generates Project Cost Estimation and total budget costs, ideal for queries on estimation or budgeting."""

    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "costEstimation",
            "errorkey": "companyNameNotFound"
        })
    
    container_name = os.getenv("Blob_output_container_name")
    container_client = blob_service_client.get_container_client(container_name)
    folder_path = f"Resource_costing/"
    blob_list = container_client.list_blobs(name_starts_with=folder_path)
    blobs = list(blob_list)
    if not blobs:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "costEstimation",
            "errorkey": "ResourceCostingNotFound"
        })
    
    file_blob = blobs[0]
    blob_client = container_client.get_blob_client(file_blob.name)
    download_stream = blob_client.download_blob()
    file_content = download_stream.readall()
    resource_costing_df = pd.read_csv(io.BytesIO(file_content))

    item = read_ProjectEstimation_State(id)
    if item:
        resource_costing_df['rate'] = pd.to_numeric(resource_costing_df['Rate'], errors='coerce')

        # Calculate the average of the 'rate' column
        average_rate = resource_costing_df['rate'].mean()
        phases_total_rate = {phase: hours * average_rate for phase, hours in item["Phases"].items()}
        budget = sum(phases_total_rate.values())
        item["Budget"] = f"${budget:,}"
        item["PhaseRate"] = {phase: f"${value:,.2f}" for phase, value in phases_total_rate.items()}
        item["AverageRate"] = f"${average_rate:,.2f}"
        
        update_ProjectEstimation(item)
        
        suggested_pills= getSuggestedPills(user_id)


        return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "CostEstimation",
                        "tool_name": "costEstimation",
                        "Data": item,
                        "pills": suggested_pills
                    }
                )
    else:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "costEstimation",
            "errorkey": "TimelineNotFound"
        })


  