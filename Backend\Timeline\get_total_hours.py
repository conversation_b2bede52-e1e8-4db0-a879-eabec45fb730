def get_total_hours(estimates):
        # Print results
    print("\nEstimated Implementation hours:")
    print(f"Data Migration: {estimates['data_migration']} hours")
    print(f"Module Implementation: {estimates['module_implementation']} hours")

    # Handle ERP integration details
    erp_estimate = estimates['erp_integration']
    if erp_estimate and isinstance(erp_estimate, dict):
        if erp_estimate['integration_points']:
            print("\nERP Integration Breakdown:")
            for integration_point, hours in erp_estimate['integration_points'].items():
                print(f"- {integration_point}: {hours} hours")
        print(f"Total ERP Integration: {erp_estimate['total_hours']} hours")
        
        # Calculate grand total
        total_hours = (
            (estimates['data_migration'] or 0) +
            (estimates['module_implementation'] or 0) +
            erp_estimate['total_hours']
        )
        print(f"\nTotal Estimated hours: {total_hours}")
        return total_hours
        
    else:
        # Calculate total without ERP if it's not available
        total_hours = sum(v for v in [estimates['data_migration'], 
                                    estimates['module_implementation']] 
                        if v is not None)
        print(f"\nTotal Estimated Hours: {total_hours}")
        return total_hours