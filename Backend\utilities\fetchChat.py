from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.Buisness_process_State import read_BPState
from DatabaseCosmos.App_module_State import read_AppModule_State
from DatabaseCosmos.ERP_State import read_ERPState
from DatabaseCosmos.Integration_State import read_INTState
from DatabaseCosmos.Data_Migration_State import read_DMState
from DatabaseCosmos.UserCurrentChat import read_userConversation
from DatabaseCosmos.Timeline import GetTimeline

def fetchChatSummary(id:str, getCompany=True, getBP=True, getModules=True, getERP=True, getIntegration=True, getDM=True, getChatHistory=True, chatHistoryWithTool=False, getTimeline=False, returnRaw=False):
    msg_summary = ""
    isThereAnyTrueValue = False
    raw_data = {}

    if getCompany:
        company_info = read_CompanyInfo(id)
        if company_info:
            company_input = f"""
                            Compnay Information:

                            Company Name: {company_info['companyname']}
                            Company Information: {company_info['companyinformation']}
                            Number of Employees: {company_info['employees']}
                            Headquarters Location: {company_info['headquarters_address']}
                            Industry: {company_info['industry']}"""
            msg_summary += company_input

            if returnRaw:
                raw_data['company'] = company_info
    
    if getBP:
        state = read_BPState(id)
        if state:
            isThereAnyTrueValue = False
            state_info = f"""

                            Business Processes the company choosed:
                            """
            state_object = []
            for each_object in state[0]['items']:
                if each_object['isChecked'] == True:
                    isThereAnyTrueValue = True
                    obj_str = f"""
                    - Category: {each_object['label']}
                    - Description: {each_object['description']}
                    """
                    state_info += obj_str

                    state_object.append({
                        "Process": each_object['label'],
                        "Process Description": each_object['description']
                    })

            if isThereAnyTrueValue:
                msg_summary += state_info
                isThereAnyTrueValue = False

                if returnRaw:
                    raw_data['businessprocesses'] = state_object
    
    if getModules:
        state = read_AppModule_State(id)
        if state:
            isThereAnyTrueValue = False
            state_info = f"""

                            App modules the company choosed:
                            """
            state_object = []
            for each_object in state[0]['items']:
                if each_object['isChecked'] == True:
                    isThereAnyTrueValue = True
                    obj_str = f"""
                    - Category: {each_object['label']}
                    - Description: {each_object['description']}
                    """
                    state_info += obj_str

                    state_object.append({
                        "Category": each_object['label'],
                        "Training Description": each_object['description']
                    })

            if isThereAnyTrueValue:
                msg_summary += state_info
                isThereAnyTrueValue = False

                if returnRaw:
                    raw_data['modules'] = state_object

    if getERP:
        state = read_ERPState(id)
        if state:
            isThereAnyTrueValue = False
            state_info = f"""

                            ERP modules the company choosed:
                            """
            state_object = []
            for each_object in state[0]['items']:
                if each_object['isChecked'] == True:
                    isThereAnyTrueValue = True
                    obj_str = f"""
                    - Category: {each_object['label']}
                    - Description: {each_object['description']}
                    """
                    state_info += obj_str

                    state_object.append({
                        "ERP": each_object['label'],
                        "ERP Description": each_object['description']
                    })

            if isThereAnyTrueValue:
                msg_summary += state_info
                isThereAnyTrueValue = False

                if returnRaw:
                    raw_data['erp'] = state_object

    if getDM:
        state = read_DMState(id)
        if state:
            isThereAnyTrueValue = False
            state_info = f"""

                            Data Migration modules the company choosed:
                            """
            state_object = []
            for each_object in state[0]['items']:
                if each_object['isChecked'] == True:
                    isThereAnyTrueValue = True
                    obj_str = f"""
                    - Category: {each_object.get('label' , " ")}
                    - Description: {each_object.get('description' , " ")}
                    - Value: {each_object.get('value' , " ") if each_object.get('value' , " ") else " "}"""
                    state_info += obj_str

                    state_object.append({
                        "Category": each_object.get('label' , " "),
                        "Description": each_object.get('description' , " "),
                        "Value": each_object.get('value' , " ")
                    })

            if isThereAnyTrueValue:
                msg_summary += state_info
                isThereAnyTrueValue = False

                if returnRaw:
                    raw_data['datamigration'] = state_object

    if getIntegration:
        state = read_INTState(id)
        if state:
            isThereAnyTrueValue = False
            state_info = f"""

                            Integration modules the company choosed:
                            """
            state_object = []
            for each_object in state[0]['items']:
                if each_object['isChecked'] == True:
                    isThereAnyTrueValue = True
                    obj_str = f"""
                    - Category: {each_object.get('label' , " ")}
                    - Description: {each_object.get('description' , " ")}
                    - Value: {each_object.get('value' , " ")}"""
                    state_info += obj_str

                    state_object.append({
                        "Integration Point": each_object.get('label' , " "),
                        "Integration  Description": each_object.get('description' , " "),
                        "Value": each_object.get('value' , " ")
                    })

            if isThereAnyTrueValue:
                msg_summary += state_info
                isThereAnyTrueValue = False

                if returnRaw:
                    raw_data['integration'] = state_object
    
    if getTimeline:
        timelinedata = GetTimeline(id=id)
        if timelinedata:
            isThereAnyTrueValue = False
            timeline_info = f"""

                            Timeline:
                            """
            for each_object in timelinedata["timeline"]:
                isThereAnyTrueValue = True
                obj_str = f"""

                - Week: {each_object['week']}
                - Phase: {each_object['phase']}
                - Task: {each_object['task']}
                - Resources: {each_object['resources']}
                - Allocated Hours: {each_object['allocatedhours']}"""
                timeline_info += obj_str

            if isThereAnyTrueValue:
                msg_summary += timeline_info
                isThereAnyTrueValue = False

                if returnRaw:
                    raw_data['timeline'] = timelinedata

    if getChatHistory:
        UserChatHistory = read_userConversation(id)
        if UserChatHistory:
            isThereAnyTrueValue = False
            chat_history = f"""

                            Here is the Chat History below:
                            """
            for each_chat in UserChatHistory['History']:
                isThereAnyTrueValue = True

                if each_chat['role'] == "tool": #check if msg is tool call
                    if chatHistoryWithTool: #check if we need to show tool call
                        obj_str = f"""
                            {each_chat['role']} : {each_chat['content']}"""
                        chat_history += obj_str

                else: #if msg is not tool call
                    obj_str = f"""
                                {each_chat['role']} : {each_chat['content']}"""
                    chat_history += obj_str
                    
            if isThereAnyTrueValue:
                msg_summary += chat_history
                isThereAnyTrueValue = False

                if returnRaw:
                    #To remove response key from the current chat history
                    UserChatHistory['History'] = [{k: v for k, v in item.items() if k != "response"} for item in UserChatHistory['History']]
                    raw_data['chathistory'] = UserChatHistory

    if returnRaw: 
        return raw_data 
    else: 
        return msg_summary.replace("    ", "")