import re,requests
from bs4 import BeautifulSoup

def scrapping():
    bc_Costing = webScrapping("https://www.microsoft.com/en-us/dynamics-365/products/business-central/pricing","Pricing")
    finance_Costing=webScrapping("https://www.microsoft.com/en-us/dynamics-365/products/finance/pricing","pricing")
    supplychain_costing=webScrapping("https://www.microsoft.com/en-us/dynamics-365/products/supply-chain-management/pricing","Pricing")
    bc_costing_cleaned = parse_costing(bc_Costing, "Dynamics 365 Business Central")
    finance_costing_cleaned = parse_costing(finance_Costing, "Dynamics 365 Finance")
    supplychain_costing_cleaned = parse_costing(supplychain_costing, "Dynamics 365 Supply Chain Management")

    licensing_cost = (
                f"{bc_costing_cleaned}\n\n"
                f"{finance_costing_cleaned}\n\n"
                f"{supplychain_costing_cleaned}"
            )
    return licensing_cost

def webScrapping(url,id):
    headers = {
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:115.0) Gecko/20100101 Firefox/115.0"
    }
    
    res = requests.get(url, headers=headers)
    soup = BeautifulSoup(res.text, 'html.parser')
    
    button_tags = soup.find_all('a')
    for button in button_tags:
        button.decompose()
    
    #print(soup.find("div", {"id": id}).get_text(strip=True, separator='\n'))
    return soup.find("div", {"id": id}).get_text(strip=True, separator='\n')
 
# Parsing function remains the same
def parse_costing(raw_text, category):
    lines = raw_text.strip().split("\n")
    plans = {}
    current_plan = None

    for line in lines:
        if "free trial" in line.lower():
            current_plan = "Free Trial"
            plans[current_plan] = "Free for 30 days"
        elif "essentials" in line.lower():
            current_plan = "Essentials"
        elif "premium" in line.lower() and "team members" not in line.lower():
            current_plan = "Premium"
        elif "team members" in line.lower():
            current_plan = "Team Members"
        elif "$" in line:
            # Extract the price
            if current_plan:
                plans[current_plan] = line.strip()
                current_plan = None

    # Format into string
    result = f"{category}:\n"
    for plan, cost in plans.items():
        result += f"  {plan}: {cost}\n"
    return result.strip()

   