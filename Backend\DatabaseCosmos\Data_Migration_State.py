import logging
import random
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from utilities.CreateGuid import generate_guide
from Config.cosmosdb import database
from DatabaseCosmos.UpdateCheckboxwithText import upsertUserSelectedData

# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="DM_State",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)

""" - Parameters
chat_id = 123(guid),
list = []
user_id:123(guid)
"""
# def insert_DM_State(chat_id : str,dm_list, dm_object:dict , user_id: str):
def insert_DM_State(chat_id : str,dm_list, description:str , user_id: str):
    logging.info("Inserting row in DM_State Container")
    print("Inserting row in DM_State Container")
    try:
        guid=generate_guide()
        item = {
                'id': guid,
                'user_id': user_id,
                'createdAt': datetime.utcnow().isoformat(),
                'chat_id':chat_id, 
                'items':dm_list,
                # "object":dm_object,
                'description':description,
            }
        container.create_item(body=item)
        print(f'Created new item with Id {guid}')
        return item
        
    except Exception as e:
        print("Error" ,e)
        
        print("Read Failed!")
        return None       

def read_DMState(chat_id):
    print("reading DM State")
    try:
        logging.info('Applying query.')
        query = f"SELECT * FROM c WHERE c.chat_id = '{chat_id}'"
        results = []
        for item in container.query_items(query = query, enable_cross_partition_query = True):
                    results.append({
                        'id': item['id'],
                        'user_id': item['user_id'],
                        'createdAt':item.get('createdAt'),
                        'items':item.get('items'),
                        # 'object':item.get('object'),
                        'description':item.get('description'),
                    })
        logging.info(f"Retrieved (query) .")
        print(f"Retrieved (query) .",results)

        return results
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
    
        return None

def callUpdateDm(chat_id,selected_list):
    print("------------------",selected_list)
    response=upsertUserSelectedData(chat_id,selected_list,container) 

    if response:
         print(response)
         return True
    else:
         return False








