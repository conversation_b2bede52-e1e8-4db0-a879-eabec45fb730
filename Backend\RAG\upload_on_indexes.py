from RAG.data_extraction import metadata_extraction, extract_text_and_tables
from RAG.chunk_index import raw_content_index
from RAG.summary_index import master_summary_index
from Config.azureIndexes import search_client
from Config.azureIndexes import search_client2

def upload_documents_in_raw_and_summary_indexes(document , blob_url):
        """
        Extract data from a document content, split it into chunks, and upload it to a search client.

        Args:
            doc_content (bytes): The content of the document file.
        """
        # Extract text and metadata from the document
        metadata = metadata_extraction(document , blob_url)
        print("METADATA EXTRACTED FROM DOCUMENT")
        text = extract_text_and_tables(document)
        print("CONTENT EXTRACTED FROM DOCUMENT")
        
        # Process and upload the content in chunks
        raw_chunks = raw_content_index(text, metadata)
        search_client.upload_documents(documents=raw_chunks)
        print("CHUNK UPLOADED FOR RAW INDEX")
        
        #upload the master summary in summary index
        master_summary = master_summary_index(text, metadata)
        search_client2.upload_documents(documents=master_summary)

        print("MASTER SUMMARY UPLOADED IN SUMMARY INDEX")
