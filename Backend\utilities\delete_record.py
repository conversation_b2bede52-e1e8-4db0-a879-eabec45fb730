from Config.cosmosdb import database

def delete_record(id,user_id,container_name):
    # Delete a record from the database
    container = database.get_container_client(container_name)
    query = f"SELECT c.id FROM c WHERE c.chat_id = @chat_id"
    parameters = [{"name": "@chat_id", "value": id}]
    items = container.query_items(query=query, parameters=parameters, enable_cross_partition_query=True)
    for item in items:
        container.delete_item(item=item['id'], partition_key=user_id)
    return {"status": "Record deleted successfully"}