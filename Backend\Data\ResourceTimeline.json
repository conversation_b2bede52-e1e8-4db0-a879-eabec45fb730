{"schema": {"fields": [{"name": "index", "type": "string"}, {"name": "Phase", "type": "string"}, {"name": "Week 1", "type": "integer"}, {"name": "Week 2", "type": "integer"}, {"name": "Week 3", "type": "integer"}, {"name": "Week 4", "type": "integer"}, {"name": "Week 5", "type": "integer"}, {"name": "Week 6", "type": "integer"}, {"name": "Week 7", "type": "integer"}, {"name": "Week 8", "type": "integer"}, {"name": "Week 9", "type": "integer"}, {"name": "Week 10", "type": "integer"}, {"name": "Week 11", "type": "integer"}, {"name": "Week 12", "type": "integer"}, {"name": "Week 13", "type": "integer"}, {"name": "Week 14", "type": "integer"}, {"name": "Week 15", "type": "integer"}, {"name": "Total Hours", "type": "integer"}], "primaryKey": ["index"], "pandas_version": "1.4.0"}, "data": [{"index": "Project Manager", "Phase": "Build", "Week 1": 20, "Week 2": 20, "Week 3": 20, "Week 4": 20, "Week 5": 20, "Week 6": 20, "Week 7": 20, "Week 8": 20, "Week 9": 20, "Week 10": 20, "Week 11": 20, "Week 12": 20, "Week 13": 20, "Week 14": 20, "Week 15": 20, "Total Hours": 300}, {"index": "Solution Architect", "Phase": "Build", "Week 1": 20, "Week 2": 20, "Week 3": 20, "Week 4": 20, "Week 5": 20, "Week 6": 20, "Week 7": 20, "Week 8": 20, "Week 9": 20, "Week 10": 20, "Week 11": 20, "Week 12": 20, "Week 13": 20, "Week 14": 20, "Week 15": 20, "Total Hours": 300}, {"index": "Developer", "Phase": "Build", "Week 1": 40, "Week 2": 40, "Week 3": 40, "Week 4": 40, "Week 5": 40, "Week 6": 40, "Week 7": 40, "Week 8": 40, "Week 9": 40, "Week 10": 40, "Week 11": 40, "Week 12": 40, "Week 13": 40, "Week 14": 40, "Week 15": 40, "Total Hours": 600}]}