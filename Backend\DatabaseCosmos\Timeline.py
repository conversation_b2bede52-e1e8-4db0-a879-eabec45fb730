import logging
import random
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from utilities.CreateGuid import generate_guide
from Config.cosmosdb import database
from DatabaseCosmos.UpdatingCheckboxAndCheckboxWithGrouping import upsertUserSelectedData
# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="Timeline",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)

""" - Parameters
chat_id = 123(guid),
modules_list = []
user_id:123(guid)
"""
def insert_Timeline(timeline, user_id: str,id, build_hours):
    logging.info("Inserting row in Timeline")
    print("Inserting row in Timeline")
    try:
        guid=generate_guide()
        item = {
                'id': guid,
                'user_id': user_id,
                'chat_id':id,
                'timeline':timeline,
                'build_hours':build_hours,
            }
        container.create_item(body=item)
        print(f'Created new item with Id {guid}')
        return item
        
    except Exception as e:
        print("Error" ,e)
        
        print("Read Failed!")
        return None       

def GetTimeline(id):
    print("reading Timeline")
    try:
        logging.info('Applying query.')
        query = f"SELECT * FROM c WHERE c.chat_id = '{id}'"
        
        item= container.query_items(query = query, enable_cross_partition_query = True)
        
        print(f"Retrieved (query) .",item)

        return list(item)[0]
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
    
        return None
    
def updateTimeline(id,Timeline,build_hours):
    
    try:
        print("checking row exist or not!")
        query = f"SELECT * FROM c WHERE c.chat_id = '{id}'"
        
        items = list(container.query_items(query=query, enable_cross_partition_query=True))
        item = items[0]
        item['timeline'] = Timeline
        item['build_hours'] = build_hours
        response = container.upsert_item(body=item)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         

        