import math
def get_phase_template(role, is_first_developer=False):
    """
    Get phase template based on role
    
    Args:
        role: Role of the resource
        is_first_developer: <PERSON><PERSON><PERSON> indicating if this is the first developer
    """
    phase_templates = {
        "Project Manager": [
            {"name": phase, "hours_allocation": 0.5, "weeks_allocation": 1, 
             "order": 1, "total_hours": 0, "total_cost": 0, "total_weeks": 0}
            for phase in ["Initialization", "Solution Modeling", "Build", 
                         "Solution Testing", "Deployment", "Support"]
        ],
        "Solution Architect": [
            {
                "name": phase,
                "hours_allocation": 1.0 if phase in ["Initialization", "Solution Modeling"] else 0.5,
                "weeks_allocation": 1,
                "order": 1,
                "total_hours": 0,
                "total_cost": 0,
                "total_weeks": 0
            }
            for phase in ["Initialization", "Solution Modeling", "Build", 
                         "Solution Testing", "Deployment", "Support"]
        ],
        "Functional Consultant": [
            {"name": phase, "hours_allocation": 1.0, "weeks_allocation": 1,
             "order": 1, "total_hours": 0, "total_cost": 0, "total_weeks": 0}
            for phase in ["Initialization", "Solution Modeling", "Build", 
                         "Solution Testing", "Deployment", "Support"]
        ],
        "Infrastructure Consultant": [
            {"name": phase, "hours_allocation": 1.0, "weeks_allocation": 1,
             "order": 1, "total_hours": 0, "total_cost": 0, "total_weeks": 0}
            for phase in ["Initialization", "Solution Modeling", "Build", 
                         "Solution Testing", "Deployment", "Support"]
        ]
    }
    
    if "Developer" in role:
        phases = ["Initialization", "Solution Modeling", "Build", 
                 "Solution Testing", "Deployment", "Support"]
        if is_first_developer:
            return [
                {
                    "name": phase,
                    "hours_allocation": 1.0 if phase in ["Initialization", "Solution Modeling", "Build", "Deployment"]
                                      else 0.5 if phase in ["Solution Testing", "Support"]
                                      else 0.0,
                    "weeks_allocation": 1,
                    "order": 1,
                    "total_hours": 0,
                    "total_cost": 0,
                    "total_weeks": 0
                }
                for phase in phases
            ]
        else:
            return [
                {
                    "name": phase,
                    "hours_allocation": 1.0 if phase == "Build"
                                      else 0.5 if phase in ["Solution Testing", "Support"]
                                      else 0.0,
                    "weeks_allocation": 1,
                    "order": 1,
                    "total_hours": 0,
                    "total_cost": 0,
                    "total_weeks": 0
                }
                for phase in phases
            ]
    
    return phase_templates[role]





def calculate_required_developers(module_info, integration_info, dm_info , total_build_hours):
    """
    Calculate number of required developers based on project scope
    
    Returns:
    - base_resources: List of base resources (PM, SA, FC, IC)
    - num_developers: Number of developers needed
    """
    # Initialize with mandatory roles
    MAX_BUILD_WEEKS = 20
    HOURS_PER_WEEK = 40
    base_resources = [
        {
            "id": "1001",
            "name": "Resource 1",
            "role": "Project Manager",
            "build_allocation": "false",
            "rate": 100,
            "phases": get_phase_template("Project Manager")
        },
        {
            "id": "1002",
            "name": "Resource 2",
            "role": "Solution Architect",
            "build_allocation": "false",
            "rate": 100,
            "phases": get_phase_template("Solution Architect")
        },
        {
            "id": "1003",
            "name": "Resource 3",
            "role": "Functional Consultant",
            "build_allocation": "true",
            "rate": 80,
            "phases": get_phase_template("Functional Consultant")
        },
        {
            "id": "1004",
            "name": "Resource 4",
            "role": "Infrastructure Consultant",
            "build_allocation": "false",
            "rate": 90,
            "phases": get_phase_template("Infrastructure Consultant")
        }
    ]
    
    # Calculate number of developers needed
    num_developers = 0
    
    # 1 Developer if modules are selected
    if module_info:
        num_developers += 1
    
    # Additional developer for integration
    if integration_info:
        num_developers += 1
    
    # Additional developer for data migration
    if dm_info:
        num_developers += 1

    #This one more developer is of functional consultant
    num_developers += 1
    # Check if build phase exceeds maximum weeks limit
    build_weeks_with_current_team = total_build_hours / (num_developers * HOURS_PER_WEEK)
    print("Build Weeks with Current Team:", build_weeks_with_current_team)
    if build_weeks_with_current_team > MAX_BUILD_WEEKS:
        # Calculate required resources using the formula
        
        total_resources_needed = math.ceil(total_build_hours / (MAX_BUILD_WEEKS * HOURS_PER_WEEK))
        print("Total Resources Needed:", total_resources_needed)
        # Subtract 1 to get number of developers (excluding the first resource)
        num_developers = total_resources_needed - 1    
    else:
        num_developers -= 1 
        
    return base_resources, num_developers




def generate_resource_schema(base_resources , num_developers):
    """
    Generate complete resource schema based on project scope
    """

    # Add required number of developers
    last_id = len(base_resources) + 1
    for i in range(num_developers):
        developer = {
            "id": f"{1000 + last_id}",
            "name": f"Resource {last_id}",
            "role": f"Developer {i + 1}",
            "build_allocation": "true",
            "rate": 60,
            "phases": get_phase_template(f"Developer {i + 1}", is_first_developer=(i == 0))
        }
        base_resources.append(developer)
        last_id += 1
    
    
    return base_resources


def count_of_resources(base_resources):
    role_counts = {}
    role_rates = {}
    
    for resource in base_resources:
        role = 'Developer' if 'Developer' in resource['role'] else resource['role']
        role_counts[role] = role_counts.get(role, 0) + 1
        
        # Store the rate for each role (assuming consistent rates per role)
        if role not in role_rates:
            role_rates[role] = resource['rate']
    
    # Convert to desired list format
    result = []
    for role, count in role_counts.items():
        result.append({
            "type": role,
            "count": count,
            "rate": role_rates.get(role, 0)
        })
    
    return result