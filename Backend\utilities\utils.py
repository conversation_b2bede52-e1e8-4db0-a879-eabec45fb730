# from langchain_core.messages import HumanMessage, AIMessage, ToolMessage, SystemMessage

# def convert_to_langchain_format(openai_chat_history):
#     """
#     Converts OpenAI chat history to LangChain message format.
    
#     :param openai_chat_history: List of dicts representing the OpenAI chat history.
#     :return: List of LangChain message objects.
#     """
#     langchain_messages = []
    
#     for message in openai_chat_history:
#         role = message['role']
#         content = message['content']
#         response = message.get('response', None)
        
#         # Map OpenAI roles to LangChain message types
#         if role == 'system':
#             langchain_messages.append(SystemMessage(content=content))
#         elif role == 'user':
#             langchain_messages.append(HumanMessage(content=content))
#         elif role == 'assistant':
#             langchain_messages.append(AIMessage(content=content))
#         elif role == 'Tool':
#             # Handle tool messages - use content as the tool_call_id
#             langchain_messages.append(ToolMessage(content=response, tool_call_id=content))
#         # else:
#         #     raise ValueError(f"Unknown role: {role}")
    
#     return langchain_messages




from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

def convert_to_langchain_format(openai_chat_history):
    """
    Converts OpenAI chat history to LangChain message format.
    
    :param openai_chat_history: List of dicts representing the OpenAI chat history.
    :return: List of LangChain message objects.
    """
    langchain_messages = []
    
    for message in openai_chat_history:
        role = message['role']
        content = message['content']
        response = message.get('response', None)
        
        
        # Map OpenAI roles to LangChain message types
        if role == 'system':
            langchain_messages.append(SystemMessage(content=content))
        elif role == 'user':
            langchain_messages.append(HumanMessage(content=content))
        elif role == 'assistant':
            langchain_messages.append(AIMessage(content=content))
        elif role == 'Tool':
            # Convert Tool messages to assistant messages
            tool_content = f"[Tool used: {content}]"
            if response and isinstance(response, dict):
                # Add basic tool response info
                if 'viewType' in response:
                    tool_content += f" Created a {response.get('viewType')} view"
                    if 'title' in response:
                        tool_content += f" for {response.get('title')}"
            
            langchain_messages.append(AIMessage(content=tool_content))
    
    return langchain_messages