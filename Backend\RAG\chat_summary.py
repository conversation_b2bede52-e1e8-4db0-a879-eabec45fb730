from pydantic import BaseModel, <PERSON>
from typing import List
from langchain_openai import AzureChatOpenAI
from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain.output_parsers import PydanticOutputParser
import json
import os
from Config.azureAI import llm  # Importing the language model configuration

# Define a Pydantic model for a document section, including its title, summary, and key terms
class Section(BaseModel):
    section: str = Field(description="Section title or heading")
    summarized_text: str = Field(description="Detailed summarized version of the section text")
    highlighted_terms: List[str] = Field(description="Key terms or concepts from the section")

# Define a Pydantic model for the document structure, containing multiple sections
class DocumentStructure(BaseModel):
    sections: List[Section] = Field(description="List of sections with their summaries and highlighted terms")

def create_chat_sections_summary(doc_sections):
    """
    Analyze sections of a document to generate detailed summaries and key terms.

    Args:
        doc_sections (list): List of document sections to analyze.

    Returns:
        list: List of Section objects with summaries and highlighted terms.
    """
    parser = PydanticOutputParser(pydantic_object=DocumentStructure)
    format_instructions = parser.get_format_instructions()  # Get formatting instructions for the output

    # System template guiding the AI to analyze the document sections
    system_template = """
    You are an advanced AI assistant specializing in document analysis. Your task is to analyze sections of a document and provide in-depth summaries and key terms.
    For each section:
    1. Maintain the original heading as a reference.
    2. Generate a comprehensive and detailed summary that captures the main points, context, and nuances of the section's content.
    3. Identify and list the most important terms, phrases, or concepts that are essential for understanding the section's content.
    """
    
    # Human template instructing the AI on how to present the analysis
    human_template = """
    Please analyze the following document sections and provide detailed, insightful summaries along with key terms for each section.

    Document sections:
    {doc_sections}

    For each section, provide:
    1. The section heading.
    2. A detailed summary that includes all relevant information, context, and insights.
    3. A list of key terms or concepts that are crucial for understanding the section.

    Ensure that the summaries are thorough and provide a deep understanding of the content. The key terms should reflect the core concepts and significant points discussed in each section.

    {format_instructions}
    """
    
    # Create a prompt using the system and human templates
    chat_prompt = ChatPromptTemplate(
        messages=[
            SystemMessagePromptTemplate.from_template(system_template),
            HumanMessagePromptTemplate.from_template(human_template)
        ]
    )
    
    # Format messages with the document sections and format instructions
    messages = chat_prompt.format_messages(
        format_instructions=format_instructions,
        doc_sections=json.dumps(doc_sections, indent=2)
    )
    
    # Invoke the language model to generate the response
    response = llm.invoke(messages)
    
    try:
        # Parse the response to extract structured information
        parsed_response = parser.parse(response.content)
        # Optionally save the parsed response
        # save_json_output(parsed_response.sections)
        return parsed_response.sections
    except Exception as e:
        raise ValueError(f"Failed to parse LLM response: {str(e)}")

def generate_chat_master_summary(sections):
    """
    Generate a master summary from the detailed section summaries and key terms.

    Args:
        sections (list): List of Section objects.

    Returns:
        str: The master summary text.
    """
    sections_text = "\n".join([
        f"Section: {sec.section}\nSummary: {sec.summarized_text}\nKey Terms: {', '.join(sec.highlighted_terms)}" 
        for sec in sections
    ])

    # System template guiding the AI to create a cohesive master summary
    system_template = """
    You are an AI tasked with creating a detailed and cohesive master summary for a document based on individual section summaries and key terms. Your summary should:
    1. Integrate and synthesize the key points, insights, and concepts from all sections.
    2. Provide a structured and logical flow that reflects the document's main themes and objectives.
    3. Highlight significant details and insights while ensuring the summary remains comprehensive and informative.
    4. Identify overarching themes, relationships between sections, and any key insights or conclusions that emerge from the document as a whole.
    """
    
    # Human template to guide the AI in consolidating and summarizing the sections
    human_template = """
    Based on the following section summaries and key terms, create a master summary that encompasses the main themes, key insights, and important concepts.

    Sections:
    {sections_text}

    Use the above information to draft a master summary that captures the document's essence in a detailed yet succinct manner.
    """
    
    # Create a prompt using the system and human templates
    chat_prompt = ChatPromptTemplate(
        messages=[
            SystemMessagePromptTemplate.from_template(system_template),
            HumanMessagePromptTemplate.from_template(human_template)
        ]
    )
    
    # Format messages with the sections text
    messages = chat_prompt.format_messages(sections_text=sections_text)
    
    # Invoke the language model to generate the master summary
    response = llm.invoke(messages)
    # Optionally save the master summary
    # save_master_summary(response.content)
    return response.content

def save_json_output(data, output_dir="chat_summary_output"):
    """
    Save the structured document summary as a JSON file.

    Args:
        data (list): List of Section objects.
        output_dir (str): Directory to save the JSON file.
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)  # Create the output directory if it doesn't exist
    output_path = os.path.join(output_dir, "chat_summary.json")
    
    # Convert Pydantic models to dictionaries
    data_dict = [section.dict() for section in data]
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data_dict, f, indent=4)
    print(f"Output saved to {output_path}")

def save_master_summary(summary, output_dir="chat_summary_output"):
    """
    Save the master summary as a text file.

    Args:
        summary (str): The master summary text.
        output_dir (str): Directory to save the text file.
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)  # Create the output directory if it doesn't exist
    output_path = os.path.join(output_dir, "summary.txt")
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(summary)
    print(f"Master summary saved to {output_path}")
