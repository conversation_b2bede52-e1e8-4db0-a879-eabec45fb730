from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated

import pandas as pd
from DatabaseCosmos.StatesChecklist import updatetimelineState
from DatabaseCosmos.Timeline import updateTimeline , insert_Timeline, GetTimeline
from DatabaseCosmos.ProjectEstimation import read_ProjectEstimation_State, insert_ProjectEstimation_State, update_ProjectEstimation
from DatabaseCosmos.UserCurrentChat import read_userConversation,upsert_userConversation
from utilities.fetchChat import fetchChatSummary
from Tools.suggestPills import getSuggestedPills

from LLMChains.Base_LLMChain_ModelClass import BaseLL<PERSON>hainClass
from StructuredSchemas.timeline_schema import TimelineStructureModel
from DatabaseCosmos.Company_info_State import read_CompanyInfo

import json
from collections import defaultdict

from openai import AzureOpenAI

timeline_extraction_prompt = """
You are a Project Timeline Creation assistant for Microsoft Dynamics 365 ERP Implementation:
timeline duration is mentioned below: 

'''
{timeline_duration}
''' 

Remember to keep the name of phases same as above mentioned timeline duration.
Only use the phases name from timeline duration mentioned above.
If phases are not mentioned in the timeline duration, then you can suggest the phases.

Remember to Keep Total Project timeline Weeks with relative to above given total timeline duration. don't suggest Project timeline Weeks far more than the given timeline duration.
weeks must be in range of between 2 weeks like this '1-2' or '4-8'.
keep the weeks in order, same phases may repeat in different weeks range and phases should be in order.
Remember not to keep different phases in same week range.

Phases: include Phases from given timeline
Define tasks for each Phase (phase defined above) following Sure Step methodology, covering modules from (e.g Business processes, Erp, App modules
etc) from the given input (by user) with their respective prefixes, add names in tasks (e.g., Business process-Module name).

Tasks and Roles:
Allocate roles (Engagement Manager, Project Manager, Solution Architect, Functional Consultant, Technical Team) to each task based on responsibilities.
If a task involves multiple roles, list all relevant roles name by name.
No other role other than the provided list should be included
You must not use generic terms like "All resources, stakeholders, team members, end users etc"

Divide hours among resources and weeks accordingly.
Provide the timeline based on the provided structure.
user query: {user_query}
"""

#Create a langchain chain to get pydantic structure.

Timeline_structure_chain = BaseLLMChainClass(
                                            timeline_extraction_prompt,
                                            TimelineStructureModel
                                            )


@tool(return_direct=True)
def createTimeLineTool(user_query:str, user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is use to generate Project Timeline and will return a proper structured output with divided tasks"""

    client=AzureOpenAI(
        api_key='********************************',
        azure_endpoint='https://secureopenai.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview',
        api_version= '2024-08-01-preview'
    )

    def chunk_retriever():
        user_query = "get me the timeline details and tell the total weeks"
        retrieval_completion = client.chat.completions.create(
            model='gpt-4o',
            messages=[
                {
                    "role": "system",
                    "content": """You are an AI assistant that reads documents and answers questions based on the information in the documents""" ,
                },
                {"role": "user", "content": user_query}
            ],
            max_tokens=800,
            temperature=0.3,
            top_p=0.7,
            frequency_penalty=0,
            presence_penalty=0,
            stream=False,
            extra_body={
                "data_sources": [{
                    "type": "azure_search",
                    "parameters": {
                        "endpoint": 'https://timelinepoc.search.windows.net',
                        "index_name": 'rawindex',
                        "query_type": "vector_simple_hybrid",
                        "fields_mapping": {
                            "content_fields_separator": "\n",
                            "content_fields": ["content"],
                            "title_field": "title",
                            "vector_fields": ["contentVector"],
                            "url_field": "file_id",
                            "filepath_field": "file_id"  
                        },
                        "in_scope": True,
                        "role_information": "You are an AI assistant that reads documents and answers questions based on the information in the documents.",
                        "strictness": 3,
                        "top_n_documents": 3,
                        "authentication": {
                            "type": "api_key",
                            "key": 'U6dJdMu6BwGrWyxIHNympw81y6xWI8GEQ049srZrxFAzSeDZkdw2'
                        },
                        "embedding_dependency": {
                        "type": "deployment_name",
                        "deployment_name": "text-embedding-3-small"
                        }
                    }
                }]
            }
        )

        retrieved_info = retrieval_completion.choices[0].message.content
        print("Answer from RAG: ", retrieved_info)

    reteived_timeline = chunk_retriever()
    print("Retrieved Timeline: ", reteived_timeline)

    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "timeline",
            "errorkey": "companyNameNotFound"
        })
    msg_summary= fetchChatSummary(id)

    response = Timeline_structure_chain.structure({
                    "user_query": user_query,
                    "input": msg_summary,
                    "timeline_duration": reteived_timeline,
                    })
    response['tasks'] = [task.dict() for task in response['tasks']]

    validateHours(response['tasks'])

    createEstimationObject(response['tasks'],user_id,id)

    items = GetTimeline(id)

    if items:
        updateTimeline(id, response['tasks'])
    else:  
        insert_Timeline(response['tasks'], user_id,id)


    #db work
    #Intitializing Company Search State in DB True
    print("updating BP state to true in db -----------------------------------")
    # updatetimelineState(user_id=user_id,value="True")
    suggested_pills= getSuggestedPills(user_id)

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "timeline",
                        "tool_name": "timeline",
                        "timeline": response['tasks'],
                        "pills":suggested_pills
                    }
                )

def createEstimationObject(data,user_id,id):
    df = pd.DataFrame(data)
    df = df.assign(resources=df['resources'].str.split(', ')).explode('resources')
    

    resource_allocation = df.groupby(['resources', 'week'])['allocatedhours'].sum().unstack(fill_value=0).T
    sorted_weeks = sorted(resource_allocation.index, key=lambda x: (int(x.split('-')[0]), int(x.split('-')[-1])))
    resource_allocation = resource_allocation.loc[sorted_weeks]
    
    total_hours_per_resource = resource_allocation.sum()
    total_hours_all_resources = total_hours_per_resource.sum()

    if '-' in resource_allocation.index[-1]:
        total_weeks = int(resource_allocation.index[-1].split('-')[-1])
    else:
        total_weeks = int(resource_allocation.index[-1])

    months = round(total_weeks / 4.33) 

    resources = {key: float(value) for key, value in total_hours_per_resource.to_dict().items()}
    total_hours = float(total_hours_all_resources.T)
    months = float(months)

    phase_data = df.drop_duplicates(subset=['week', 'task', 'phase', 'allocatedhours'])
    phase_hours = phase_data.groupby('phase')['allocatedhours'].sum()
    total_phase_hours = float(phase_hours.sum().T)
    phases = {key: float(value) for key, value in phase_hours.to_dict().items()}

    item = read_ProjectEstimation_State(id)

    if item:
        update_ProjectEstimation({
            "id": item['id'],
            "chat_id": item['chat_id'],
            "user_id": user_id,
            "Resources": resources,
            "Phases": phases,  
            "Estimated Hours": total_phase_hours,
            "months":months,
            "PhaseRate":"",
            "Budget":"",
            "AverageRate":""
        })
    else:
        insert_ProjectEstimation_State(resources,phases,total_phase_hours,months,"","","", user_id,id)

def validateHours(data):
    week_resource_hours = defaultdict(lambda: defaultdict(int))
    resource_tasks = defaultdict(lambda: defaultdict(list))

    # Step 1: Calculate total hours for each resource per week and track tasks
    for task in data:
        week = task['week']
        resources = parse_resources(task['resources'])
        for resource in resources:
            week_resource_hours[week][resource] += task['allocatedhours']
            resource_tasks[week][resource].append(task)

    # Step 2: Distribute tasks across weeks, ensuring no week exceeds 40 hours, but the total hours over a week range can exceed 40
    for week, resources in week_resource_hours.items():
        for resource,total_hours in resources.items():
            tasks_to_adjust = resource_tasks[week][resource]
            total_task_hours = sum(task['allocatedhours'] for task in tasks_to_adjust)

            if total_task_hours > 40:
                week_range = get_week_range(week)
                total_range_weeks = week_range[1] - week_range[0] + 1

                max_allowed_hours = 40 * total_range_weeks

                if total_task_hours > max_allowed_hours:
                    excess_hours = total_task_hours - max_allowed_hours
                    reduction_factor = max_allowed_hours / total_task_hours
                    for task in tasks_to_adjust:
                        task['allocatedhours'] = int(task['allocatedhours'] * reduction_factor)

def get_week_range(week):
    """
    Helper function to parse week ranges (e.g., '1-2') into a tuple.
    If the week is a single week (e.g., '1'), it returns the same week range.
    """
    if '-' in week:
        start_week, end_week = week.split('-')
        return int(start_week), int(end_week)
    else:
        return int(week), int(week)

def parse_resources(resource_str):
    return [r.strip() for r in resource_str.split(",")]

