from azure.cosmos import Cosmos<PERSON>lient, Partition<PERSON>ey, exceptions
from dotenv import load_dotenv
import os

load_dotenv()

# endpoint = os.getenv("COSMOS_DB_ENDPOINT")
# key = os.getenv("COSMOS_DB_KEY")
#folderpath = os.getenv("File_User_db_Folder_path", "https://securegptstorageacc.blob.core.windows.net/userdoc")
# database_name = os.getenv("COSMOS_DB_NAME")

# Initialize Cosmos DB client
# client = CosmosClient(endpoint, key)
# database = client.create_database_if_not_exists(id=database_name)

client = CosmosClient(
            os.getenv("COSMOS_DB_ENDPOINT"), 
            os.getenv("COSMOS_DB_KEY"),
        )

database = client.create_database_if_not_exists(
                        id=os.getenv("COSMOS_DB_NAME"),
                    )

print("CosmosDB Initilized...")