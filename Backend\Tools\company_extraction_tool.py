import json

from DatabaseCosmos.Company_info_State import read_CompanyInfo, insert_Companyinfo_State,updateCompanyInfo
from langchain_core.tools import tool

from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
from LLMChains.Base_LLMChain_ModelClass import Base<PERSON><PERSON>hainClass
# from SchemaModels.companySearchSchema import CompanyInfo
from StructuredSchemas.company_extraction_schema import CompanyStructureModel

from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from DatabaseCosmos.StatesChecklist import updateCompanyInfoState,validation,States
from Tools.suggestPills import getSuggestedPills



@tool(return_direct=True)
def comapny_info_extration(user_query:str, user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    
    """Trigger this tool when a query includes a company name in the context of starting work with Microsoft Dynamics 365. Examples include:
- A company name + intent to start a **proposal**, **Statement of Work (SOW)**, **SOW** (e.g., “Create SOW for Samsung”, “Need proposal for Contoso Ltd.”)
- Keywords like **proposal**, **SOW**, **scope**, **plan**, **Dynamics 365**, **search <company>** etc., used with a company name"""


    try:
        
        company_extraction_system_prompt = f"""
                    You will be provided user query : '{user_query}' , this contain company name. You need to provide details about the company.
                    You are also provided the company info object.
                    you have to change the data that user asked to change in the company info object
                    
                    Read the given query and company info object precisely.
                    Transform the given output into the structured output provided.
                    
                    Remember: you need to provide data and company details according to the structured output provided  
                    """
        company_extraction_chain = BaseLLMChainClass(
                                        company_extraction_system_prompt, 
                                        CompanyStructureModel
                                        )

        company_data = company_extraction_chain.structure({"input": "create company data for the company ","user_query":user_query })

        
        response= DataNotExisted(id=id,user_id=user_id,company_data=company_data)
        
        return response
    except Exception as e:
        return(f"Error while extracting company information.{e}")
    





def DataNotExisted(id,company_data,user_id):
    
    
    # checking if any company search data existed for the given user
    existing_data=read_CompanyInfo(chat_id=id)
    old_dictionary  = existing_data
    
    
    dictionary = company_data
    
    dictionary["industry"] = {
        "name": company_data["industry_name"],
        "SIC_Code": company_data["SIC_Code"],
        "type_of_manufacturing": company_data["type_of_manufacturing"]
    }

    dictionary["corporate_structure"] = {
        "subsidiary_of": company_data["corporate_structure_subsidiary_of"],
        "ownership": company_data["corporate_structure_ownership"]
    }

    dictionary["employees"] = str(dictionary["employees"])

    del dictionary["industry_name"]
    del dictionary["SIC_Code"]
    del dictionary["type_of_manufacturing"]
    del dictionary["corporate_structure_subsidiary_of"]
    del dictionary["corporate_structure_ownership"]

    print("----------------------------------------- dictionary", dictionary)

    company_name = dictionary["company_name"]
    company_information = dictionary["company_information"]
    industry = dictionary["industry"]["name"]
    headquarters_address = dictionary["headquarters_address"]
    employees = dictionary["employees"]
    corporate_structure = dictionary["corporate_structure"]
    sic_code=dictionary["industry"]["SIC_Code"]
    type_of_manufac=dictionary["industry"]["type_of_manufacturing"]
    subsidiaries_and_brands = dictionary["subsidiaries_and_brands"]


    
    # if there is no previous company data found in DB then insert the data into DB
    if not old_dictionary :
        print("Inserting data into company state DB ----------------------------------------------------------------")
        db_response = insert_Companyinfo_State(
            chat_id=id,
            user_id=user_id,
            company_name=company_name,
            company_information=company_information,
            industry=industry,
            SIC_code=sic_code,
            type_of_manufacturing=type_of_manufac,
            headquarters_address=headquarters_address,
            employees=employees,
            corporate_structure=corporate_structure,
            subsidiaries_and_brands=subsidiaries_and_brands
        )
        print("Db write completed", db_response)

    # if company data found in the DB against the provided chat_id then update the company data into the DB    
    else:
        print("--------------- in data existed , updating the data : ", company_data)
        db_response = updateCompanyInfo(chat_id=id,
            user_id=user_id,
            company_name=company_name,
            company_information=company_information,
            industry=industry,
            SIC_code=sic_code,
            type_of_manufacturing=type_of_manufac,
            headquarters_address=headquarters_address,
            employees=employees,
            corporate_structure=corporate_structure,
            subsidiaries_and_brands=subsidiaries_and_brands)
        
        print("Db company data update completed", db_response)

    #^^^^^^^^^^^^^^^^^^^^^^^^ Database Work Here ^^^^^^^^^^^^^^^^^^^^^^^^^
    #Intitializing Company Search State in DB True
    print("updating Company info state to true in db -----------------------------------")
    updateCompanyInfoState(user_id=user_id,value="True")

    #^^^^^^^^^^^^^^^^^^^^^^^^ Database Work End Here ^^^^^^^^^^^^^^^^^^^^^^^^^
    
    # get suggested pills 
    suggested_pills= getSuggestedPills(user_id)
    
    response= json.dumps({
            
                        "id": id,
                        "user_id": user_id,
                        "viewType": "companyinfo",
                        "tool_name":"company_info",
                        "title": company_name,
                        "content": company_information,
                        "industry": dictionary["industry"]["name"],
                        "SIC_Code":dictionary["industry"]["SIC_Code"],
                        "type_of_manufacturing":dictionary["industry"]["type_of_manufacturing"],
                        "headquarters_address":headquarters_address,
                        "employees":employees,
                        "corporate_structure":dictionary["corporate_structure"]["ownership"],
                        "subsidiaries_and_brands": subsidiaries_and_brands,
                        
                        "pills": suggested_pills
                    })
    return response









"""
        response["industry"] = {
            "name": response["industry_name"],
            "SIC_Code": response["SIC_Code"],
            "type_of_manufacturing": response["type_of_manufacturing"]
        }

        response["corporate_structure"] = {
            "subsidiary_of": response["corporate_structure_subsidiary_of"],
            "ownership": response["corporate_structure_ownership"]
        }

        response["employees"] = str(response["employees"])

        
        del response["industry_name"]
        del response["SIC_Code"]
        del response["type_of_manufacturing"]
        del response["corporate_structure_subsidiary_of"]
        del response["corporate_structure_ownership"]

        

        company_name = response["company_name"]
        company_information = response["company_information"]
        industry = response["industry"]["name"]
        headquarters_address = response["headquarters_address"]
        employees = response["employees"]
        corporate_structure = response["corporate_structure"]
        sic_code=response["industry"]["SIC_Code"]
        type_of_manufac=response["industry"]["type_of_manufacturing"]
        subsidiaries_and_brands = response["subsidiaries_and_brands"]
        
        
        print("-------------------------------------------")
        print("------------- Company information from comapny_info_extration tool ---------------")
        print("----------- company_name", company_name )
        print("----------- company_information", company_information )
        print("----------- industry", industry )
        print("----------- headquarters_address", headquarters_address )
        print("----------- employees", employees )
        print("----------- corporate_structure", corporate_structure )
        print("----------- sic_code", sic_code )
        print("----------- type_of_manufac", type_of_manufac )
        print("----------- subsidiaries_and_brands", subsidiaries_and_brands )
       
        # db_response = insert_Companyinfo_State(
        #     chat_id=id,
        #     user_id=user_id,
        #     company_name=company_name,
        #     company_information=company_information,
        #     industry=industry,
        #     SIC_code=sic_code,
        #     type_of_manufacturing=type_of_manufac,
        #     headquarters_address=headquarters_address,
        #     employees=employees,
        #     corporate_structure=corporate_structure,
        #     subsidiaries_and_brands=subsidiaries_and_brands
        # )
        # print("Db write completed", db_response) 
       
        
        result= json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "companyinfo",
                        "title": company_name,
                        "content": company_information,
                        "headquarters_address":headquarters_address,
                        "employees":employees,
                        "subsidiaries_and_brands": subsidiaries_and_brands
                    })
        return result
    
    except Exception as e:
        return json.dumps({
                            "viewType": "simplechat",
                            "content": str(f"Data not avaiable for the require company."),
                            })



"""     
        
        
        
        