import logging
import random
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from utilities.CreateGuid import generate_guide
from Config.cosmosdb import database
from DatabaseCosmos.UpdatingCheckboxAndCheckboxWithGrouping import upsertUserSelectedData
# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="resources",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)

def initialize_resource(user_id: str, id: str):
    logging.info(f"Initializing resources with user_id: {user_id} and chat_id: {id}")
    print("Initializing Resources")
    try:
        # Check if the item already exists
        existing_item = getResources(id)
        if existing_item:
            print("Item already exists")
            return existing_item

        # guid=generate_guide()
        item = {
            # 'id': guid,
            'user_id': user_id,
            'id': id,
            "resources": []
        }
        container.create_item(body=item)
        print(f'Created new item on {id}')
        return item
        
    except Exception as e:
        print("Error" ,e)
        
        print("Read Failed!")
        return None 
    
def getResources(id: str):
    print("Reading Resources")
    try:
        logging.info(f"Querying chat_id: {id} from Resources")
        # query = f"SELECT * FROM c WHERE c.user_id = '{user_id}' AND c.id = '{id}'"
        query = f"SELECT * FROM c WHERE c.id = '{id}'"
        
        items = container.query_items(query=query, enable_cross_partition_query=True)
        
        item_list = list(items)
        if item_list:
            print(f"Retrieved item: {item_list[0]}")
            return item_list[0]
        else:
            print("No item found")
            return None
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
        return None
    
#update resources
def updateResources(id: str, resources: list):
    print("Updating Resources Section")
    try:
        logging.info(f"Updating Resources for chat_id: {id}")
        item = getResources(id)
        if item:
            item['resources'] = resources
            response = container.upsert_item(body=item)
            print(f'Upserted Item\'s Id is {response["id"]}')
            return response
        else:
            print("Item not found")
            return None
        
    except Exception as e:
        print("Error", e)
        print("Upsert Failed!")
        return None

#delete chatDB
def deleteResources(id: str):
    print("Deleting Resources")
    try:
        logging.info(f"Deleting chat_id: {id} from Resources")
        item = getResources(id)
        if item:
            container.delete_item(item, partition_key=id)
            print(f'Deleted item with Id {id}')
            return item
        else:
            print("Item not found")
            return None
        
    except Exception as e:
        print("Error", e)
        print("Delete Failed!")
        return None
