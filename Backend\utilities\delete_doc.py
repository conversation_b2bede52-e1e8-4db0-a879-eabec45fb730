import os

from Config.blobService import blob_service_client
from dotenv import load_dotenv
load_dotenv()

container_name = os.getenv("Blob_output_container_name")
def delete_file_from_blob(user_id, chat_id):

    try:
        # Get the container client
        container_client = blob_service_client.get_container_client(container_name)
        
        # Prefix for the chat_id folder
        prefix = f"{user_id}/{chat_id}/"
        
        # List all blobs with the prefix
        blobs = container_client.list_blobs(name_starts_with=prefix)
        
        # Delete each blob under the chat_id
        for blob in blobs:
            print(f"Deleting blob: {blob.name}")
            container_client.delete_blob(blob.name)
                
        print(f"All blobs under chat_id '{chat_id}' have been deleted successfully.")
    except Exception as e:
        print(f"An error occurred: {e}")