import logging
from datetime import datetime, timedelta
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from Config.cosmosdb import database
from utilities.CreateGuid import generate_guide
# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="webscrapping",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)  

def delete_item(user_id):
    """
    delete item by chat ID.
    """
    print('\n1.12 Deleting Item by Id\n')

    container.delete_item(item=user_id, partition_key=user_id)

    print('Deleted item\'s Id is {0}'.format(user_id))
def insert_webscrapping(user_id: str,costing):
    logging.info("Inserting row in webscrapping")
    print("Inserting row in webscrapping")
    try:
        #delete_item(user_id=user_id)
        guid=generate_guide()
        item = {
                'id': guid,
                'user_id': user_id,
                'createdAt': datetime.utcnow().isoformat(),
                'costing':costing
            }
        container.create_item(body=item)
        print(f'Created new scrapping with Id {guid}')
        
        return item
        
    except Exception as e:
         print("Error" ,e)
        
         print("Read Failed!")
         return None       
    
def read_webscrappingData(user_id):
    print("reading costing")
    try:
        query = f"SELECT * FROM c WHERE c.user_id = '{user_id}'"
        results = []
        for item in container.query_items(query=query, enable_cross_partition_query=True):
            results.append({
                'id': item['id'],
                'user_id': item['user_id'],
                'createdAt': item.get('createdAt'),
                'costing': item.get('costing')
            })

        logging.info("Retrieved (query).")
        print("Retrieved (query).", results)
        logging.info("Applying query.")

        current_date = datetime.now()
        # Calculate the date one month ago
        one_month_ago = current_date - timedelta(days=30)  # Approximate one month as 30 days

        # Check if any of the results' createdAt is before one month ago
        filtered_results = []
        for result in results:
            created_at = result.get('createdAt')
            if created_at:
                # Assuming createdAt is stored as a string in ISO 8601 format
                created_at_date = datetime.fromisoformat(created_at)
                if created_at_date >= one_month_ago:
                    filtered_results.append(result['costing'])

        if not filtered_results:
            return None
        else:
            return filtered_results
            

       
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
    
        return None