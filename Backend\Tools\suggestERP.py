import os
from dotenv import load_dotenv
import pandas as pd
from langchain_core.prompts import ChatPrompt<PERSON>emplate
from langchain_core.tools import tool
import json
import re,requests
from Tools.WebscrappingAndCleaning import scrapping
import markdown2
from docx import Document
from docx.shared import RGBColor, Pt, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
from bs4 import BeautifulSoup
from utilities.upload_file import upload_file_to_blob, generate_file
from DatabaseCosmos.UserCurrentChat import upsert_userConversation, read_userConversation
from DatabaseCosmos.StatesChecklist import updateERPState, validation, States, updateAppModState
from DatabaseCosmos.Company_info_State import insert_Companyinfo_State, read_CompanyInfo
from utilities.get_erp import get_erp
from DatabaseCosmos.ERP_State import insert_ERP_State, read_ERPState
from LLMChains.Base_LLMChain_ModelClass import <PERSON><PERSON>MChainClass
from StructuredSchemas.erp_extraction_schema import ERPStructureModel
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
from DatabaseCosmos.Webscrapping import read_webscrappingData, insert_webscrapping
from Config.azureAI import llm
from Tools.suggestPills import getSuggestedPills
from openai import AzureOpenAI
from utilities.openai_services import OpenAIService
from langchain_core.tools import Tool
from langchain_google_community import GoogleSearchAPIWrapper
import io
from DatabaseCosmos.Buisness_process_State import read_BPState 

load_dotenv()

os.environ["GOOGLE_CSE_ID"] = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_SEARCH_API_KEY")

conversation_histories = {}

# Initialize OpenAI client
endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")

client = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    azure_endpoint=endpoint,
    api_version="2024-08-01-preview"
)

# Define document styling constants
HEADING1_COLOR = "0078D7"  
HEADING2_COLOR = "505050"  
HEADING3_COLOR = "505050" 
PARAGRAPH_COLOR = "787880"  
TABLE_HEADER_BG_COLOR = "0078D7"  # Blue background
TABLE_HEADER_TEXT_COLOR = "FFFFFF"  # White text

erp_extraction_system_prompt = f"""
                You are provided a company's data
Suggest the ERP Platform of Microsoft according to the company with company info object
Read the given company data precisely, then suggest up to 5 ERP dynamics 365.
Remember to return just the business process IDs
Transform the given suggestion into the structured output provided.
"""

erp_extraction_chain = BaseLLMChainClass(
                                            erp_extraction_system_prompt,
                                            ERPStructureModel
                                            )

@tool(return_direct=True)
def suggest_ERP_platform(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is used to select / add / recommend / suggest / scope of the ERP Platform"""
    print("i am in erp")
    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "erp_module",
            "errorkey": "companyNameNotFound"
        })
    
    bp_info = read_BPState(id)
    if not bp_info:
        return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "simplechat",
                        "tool_name": "erp_module",
                        "content": "You have not selected any Business Process. Please select at least one Business Process to proceed.",
                    })
    else:
        if bp_info:
            items = bp_info[0]['items']
            selected_bp = [item for item in items if item["isChecked"]]
            print("BP---:",selected_bp)
        else:
            selected_bp = "null"
        
        if len(selected_bp) == 0:
            return json.dumps({
                "id": id,
                "user_id": user_id,
                "viewType": "simplechat",
                "tool_name": "erp_module",
                "content": "You have not selected any Business Process. Please select at least one Business Process to proceed."
            })
        
    
    updateAppModState(user_id=user_id, value="False")
    # get suggested pills
    suggested_pills = getSuggestedPills(user_id)

    erp_testdata = read_ERPState(id)
    
    #get Costing
    licensing_cost = read_webscrappingData(user_id=user_id)
    
    if licensing_cost is None:
        licensing_cost = scrapping()
        insert_webscrapping(user_id=user_id, costing=licensing_cost)

    # get document
    docx_file_path = f"Comparision_document.docx"

    erp_file_url = generate_file(user_id, id, docx_file_path)

    if erp_testdata:
    # if False:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "erp",
            "tool_name": "erp_module",
            "title": f"{company_info['companyname']} -> ERP Platform" if company_info else "ERP Platform",
            "type": "Checkbox",
            "description": erp_testdata[0]["description"] if erp_testdata[0]["description"] else "ERP Platform you have selected",
            "document": erp_file_url,
            "fields": erp_testdata[0]["items"],
            "pills": suggested_pills
        })  
    else:
        print(company_info)
        introduction_prompt= [
            {
                "role": "system",
                "content": """

                Write an introductory paragraph recommending Microsoft Dynamics 365 for a company. As why it will be useful to utilize the serivce. Also keep in mind the company industry it operates in and try to keep is short and to the point . paragraph should start from Microsoft offers multiple ERP platform...."""},
            {
                "role": "user",
                "content": f"Company information to create comparision document max 30 words:{company_info}"
            }
        ]
        
        intro_response = openAIsearch(client=client,chathistory=introduction_prompt)
        #intro_response = f"""Microsoft offers multiple ERP platforms for manufacturing organizations."""
        erp_list = [{
                "type": "Checkbox",
                "label": "Dynamics 365 Finance and Supply Chain",
                "isChecked": False,
                "id": "1",
                "group": "Dynamics 365 Finance and Supply Chain",
                "description": "Manage your finances and supply chain operations with Dynamics 365 Finance and Supply Chain."
            },
                {
                "type": "Checkbox",
                "label": "Dynamics 365 Business Central",
                "isChecked": False,
                "id": "2",
                "group": "Dynamics 365 Business Central",
                "description": "Streamline your small to medium-sized business operations with Dynamics 365 Business Central."
            },
        ]
        
        comaprision_system_msg = [
            {
                "role": "system",
                "content": """You are an expert assistant capable of providing detailed and structured comparisons 
for enterprise software solutions. Based on the provided company information and licensing details 
as dynamic variables, you will create a comprehensive comparison table between Dynamics 365 Finance 
and Supply Chain Management and Dynamics 365 Business Central. 

The table must cover some of the following aspects, related to the context:

1. **Target Audience**
2. **Licensing Cost and Structure**
3. **Core Functionalities**
4. **Advanced Functionalities**
5. **Industry Fit / Best-Suited Company Types**
6. **Scalability and Growth Potential**
7. **Implementation Time and Complexity**
8. **Deployment Options (Cloud, On-Premises, Hybrid)**
9. **User Interface and Usability**
10. **Customization Capabilities**
11. **Third-party Integration Support**
12. **Microsoft Ecosystem Integration (Power Platform, Azure, etc.)**
13. **Automation and AI Features**
14. **Compliance and Regulatory Support**
15. **Security Features and Certifications**
16. **Localization and Multi-Currency Support**
17. **Supply Chain Management Capabilities**
18. **Financial Management Depth (Multi-entity, Consolidation, etc.)**
19. **Inventory and Warehouse Management**
20. **Project Management Capabilities**
21. **Manufacturing Capabilities**
22. **CRM and Sales Features**
23. **Reporting and Business Intelligence (BI) Tools**
24. **Mobile Access and Remote Work Support**
25. **User Training and Learning Resources**
26. **Partner Ecosystem and Community Support**
27. **Upgrade and Maintenance Effort**
28. **Total Cost of Ownership (TCO)**
29. **Return on Investment (ROI)**
30. **Future-proofing and Innovation Roadmap**

Ensure the information is accurate, clearly structured, and suitable for decision-makers comparing ERP solutions in various industries and scales. Tailor explanations to match the company's size, goals, and regulatory needs where applicable.
"""},
            {
                "role": "user",
                "content": f"""Please generate a detailed comparison table between Dynamics 365 Finance and Supply Chain Management
                and Dynamics 365 Business Central based on the provided details.
                The comparison should focus on features, pricing (with all costs in one row and should not contain <br> tag), 
                and how each product suits the needs of a large-scale company like Dell, 
                considering different licensing tiers.
                        Company information:{company_info} ,
                        lisencing cost:{licensing_cost}
                """
                }
            ]
        response = openAIsearch(client=client, chathistory=comaprision_system_msg)
        response = response.replace("<br>", ",")

        doc = Document()
        

        # Add content to the document with custom styling
        add_styled_heading(doc, f"ERP Comparison Document for {company_info['companyname']}", level=1)

        # Parse and apply Markdown formatting to each line
        for line in split_markdown(str(response)):
            add_md_paragraph(doc, line, markdown=response)

        output_stream = io.BytesIO()
        doc.save(output_stream)
        output_stream.seek(0)  # Reset stream position for uploading

        # Generate SAS URL by uploading the in-memory file
        docx_url = upload_file_to_blob(file_name=docx_file_path, output_stream=output_stream, user_id=user_id, id=id)

        insert = insert_ERP_State(chat_id=id, user_id=user_id, erp_list=erp_list, description=intro_response)
        print("database insertion on Data Migration state completed")

        # Initializing Company Search State in DB True
        updateERPState(user_id=user_id, value="True")
        
        return json.dumps({
                    "id": id,
                    "user_id": user_id,
                    "viewType": "erp",
                    "tool_name": "erp_module",
                    "title": f"{company_info['companyname']} -> ERP Platform",
                    "type": "Checkbox",
                    "description": f"{intro_response}",
                    "fields": erp_list,
                    "document": docx_url,
                    "pills": suggested_pills
                })

def add_styled_heading(doc, text, level=1):
    """Add a heading with custom styling"""
    heading = doc.add_heading(text, level=level)
    
    # Set specific styling based on heading level
    if level == 1:
        for run in heading.runs:
            run.font.color.rgb = RGBColor.from_string(HEADING1_COLOR)
            run.font.size = Pt(16)
            run.font.bold = True
            run.font.name = "Segoe UI Semibold"
    elif level == 2:
        for run in heading.runs:
            run.font.color.rgb = RGBColor.from_string(HEADING2_COLOR)
            run.font.size = Pt(14)
            run.font.bold = True
            run.font.name = "Segoe UI Semibold"
    elif level == 3:
        for run in heading.runs:
            run.font.color.rgb = RGBColor.from_string(HEADING3_COLOR)
            run.font.size = Pt(12)
            run.font.bold = True
            run.font.name = "Segoe UI Semibold"
    
    # Add spacing after heading
    heading.paragraph_format.space_after = Pt(12)
    return heading

def openAIsearch(client, chathistory):
    try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=chathistory,
                temperature=0.7,
            )
            return response.choices[0].message.content
    except Exception as e:
            raise Exception(f"OpenAI API request failed: {str(e)}")
    
def webScrapping(url, id):
    headers = {
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:115.0) Gecko/20100101 Firefox/115.0"
    }
    
    res = requests.get(url, headers=headers)
    soup = BeautifulSoup(res.text, 'html.parser')
    
    button_tags = soup.find_all('a')
    for button in button_tags:
        button.decompose()
    
    return soup.find("div", {"id": id}).get_text(strip=True, separator='\n')

def parse_costing(raw_text, category):
    lines = raw_text.strip().split("\n")
    plans = {}
    current_plan = None

    for line in lines:
        if "free trial" in line.lower():
            current_plan = "Free Trial"
            plans[current_plan] = "Free for 30 days"
        elif "essentials" in line.lower():
            current_plan = "Essentials"
        elif "premium" in line.lower() and "team members" not in line.lower():
            current_plan = "Premium"
        elif "team members" in line.lower():
            current_plan = "Team Members"
        elif "$" in line:
            # Extract the price
            if current_plan:
                plans[current_plan] = line.strip()
                current_plan = None

    # Format into string
    result = f"{category}:\n"
    for plan, cost in plans.items():
        result += f"  {plan}: {cost}\n"
    return result.strip()

def companySearch(query:str)-> tuple[str, str]:
    """"""
    search = GoogleSearchAPIWrapper()
    # Perform the search to get raw data for artifact
    raw_results = search.results(query, num_results=5)

    # Summarize content as a simple readable format for the LLM
    content = "\n".join(
        f"{result['title']}: {result['snippet']}" for result in raw_results
    )

    # Return both content and artifact (raw data)
    artifact = raw_results  # Raw JSON or structured response for deeper access
    return content, artifact

def split_markdown(content):
    lines = content.strip().splitlines()
    result = []
    buffer = []
    in_table = False
 
    for line in lines:
        if re.match(r'^\|.*\|$', line):  # Check if the line is part of a table
            buffer.append(line)
            in_table = True
        else:
            if in_table:  # Add the buffered table lines as a single entry
                result.append("\n".join(buffer))
                buffer = []
                in_table = False
            result.append(line)
 
    # Append any remaining buffer content (in case the content ends with a table)
    if buffer:
        result.append("\n".join(buffer))
 
    new_arr = []
    for i in result:
      if i == '':
        continue
      new_arr.append(i)
 
    return new_arr

def add_md_paragraph(doc, text, markdown):
    """Add a paragraph with Markdown interpretation for headings, bold, italics, lists, and tables with custom styling."""
    if text.startswith("# "):  # Heading level 1
        add_styled_heading(doc, text[2:].strip(), level=1)
    elif text.startswith("## "):  # Heading level 2
        add_styled_heading(doc, text[3:].strip(), level=2)
    elif text.startswith("### "):  # Heading level 3
        add_styled_heading(doc, text[4:].strip(), level=3)
    elif text.startswith("- ") or text.startswith("* "):  # Bullet point
        paragraph = doc.add_paragraph(style='ListBullet')
        prefix = "- " if text.startswith("- ") else "* "
        content = text[len(prefix):].strip()
        
        parts = re.split(r"(\*\*|\*|__|)", content)  # Split by Markdown syntax for bold/italic
        bold, italic = False, False
        for part in parts:
            if part in ("**", "__"):
                bold = not bold
            elif part == "*":
                italic = not italic
            else:
                run = paragraph.add_run(part)
                run.font.color.rgb = RGBColor.from_string(PARAGRAPH_COLOR)
                run.font.size = Pt(11)
                run.bold = bold
                run.italic = italic
                run.font.name = "Segoe UI (Body)"
        
        # Add spacing after paragraph
        paragraph.paragraph_format.space_after = Pt(6)
    elif text.startswith("|") and "|" in text:  # Table row
        create_word_table(doc=doc, markdown=text)
    else:
        # Regular paragraph with bold and italic handling
        paragraph = doc.add_paragraph()
        parts = re.split(r"(\*\*|\*|__|)", text)  # Split by Markdown syntax for bold/italic
        bold, italic = False, False
        for part in parts:
            if part in ("**", "__"):
                bold = not bold
            elif part == "*":
                italic = not italic
            else:
                run = paragraph.add_run(part)
                run.font.color.rgb = RGBColor.from_string(PARAGRAPH_COLOR)
                run.font.size = Pt(11)
                run.font.name = "Segoe UI (Body)"
                run.bold = bold
                run.italic = italic
        
        # Add spacing after paragraph
        paragraph.paragraph_format.space_after = Pt(10)

def parse_markdown_table(markdown_table):
    """Parses a markdown table into a list of headers and rows."""
    lines = markdown_table.strip().split("\n")
    headers = ["**" + cell.strip().replace("*", '') + "**" for cell in lines[0].split("|")[1:-1]]
    rows = [
        [cell.strip() for cell in line.split("|")[1:-1]]
        for line in lines[2:]
    ]
    return headers, rows
 
def add_bold_text(cell, text):
    """Adds bold text to a Word cell with custom styling."""
    if "**" in text:
        parts = re.split(r"(\*\*.*?\*\*)", text)
        for part in parts:
            if part.startswith("**") and part.endswith("**"):
                run = cell.add_paragraph().add_run(part[2:-2])
                run.bold = True
                run.font.size = Pt(11)
                run.font.name = "Segoe UI (Body)"
            else:
                run = cell.add_paragraph().add_run(part)
                run.font.size = Pt(11)
                run.font.name = "Segoe UI (Body)"
    else:
        run = cell.add_paragraph().add_run(text)
        run.font.size = Pt(11)
        run.font.name = "Segoe UI (Body)"
def set_cell_shading(cell, color):
    """Set the background color of a table cell"""
    cell_properties = cell._element.tcPr
    if cell_properties is None:
        cell_properties = OxmlElement('w:tcPr')
        cell._element.append(cell_properties)
    
    shading = OxmlElement('w:shd')
    shading.set(qn('w:fill'), color)  # Set background color
    cell_properties.append(shading)

def create_word_table(markdown, doc):
    """Creates a Word document table from headers and rows with custom styling and repeating headers."""
    headers, rows = parse_markdown_table(markdown)
    
    # Ensure rows and headers are not empty
    if not headers or not rows:
        raise ValueError("Headers or rows cannot be empty")
    
    table = doc.add_table(rows=len(rows) + 1, cols=len(headers))
    table.style = "Table Grid"
    
    # Set repeating header using XML directly - this is more reliable
    tblPr = table._element.xpath('w:tblPr')[0]
    tblHeader = OxmlElement('w:tblHeader')
    tblHeader.set(qn('w:val'), "true")
    tblPr.append(tblHeader)
    
    # Make sure the first row is actually marked as a header row
    for row_idx, row in enumerate(table.rows):
        # Get the properties element of the row
        trPr = row._tr.get_or_add_trPr()
        if row_idx == 0:  # First row
            # Mark as header row
            tblHeader = OxmlElement('w:tblHeader')
            tblHeader.set(qn('w:val'), "true")
            trPr.append(tblHeader)
    
    # Add headers with custom styling
    for idx, header in enumerate(headers):
        if idx < len(headers):
            cell = table.cell(0, idx)
            # Set background color for header cells
            set_cell_shading(cell, TABLE_HEADER_BG_COLOR)
            
            # Add header text with custom styling
            paragraph = cell.paragraphs[0]
            run = paragraph.add_run(header.replace("**", ""))
            run.bold = True
            run.font.color.rgb = RGBColor.from_string(TABLE_HEADER_TEXT_COLOR)
            run.font.size = Pt(12)
            run.font.name = "Segoe UI (Body)"
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        else:
            print(f"Warning: Header index {idx} out of range.")
 
    # Add rows
    for row_idx, row in enumerate(rows):
        if row_idx + 1 < len(table.rows):  # Check if row index is within valid range
            for col_idx, cell_text in enumerate(row):
                if col_idx < len(headers):  # Check if column index is valid
                    cell = table.cell(row_idx + 1, col_idx)
                    add_bold_text(cell, cell_text)
                else:
                    print(f"Warning: Column index {col_idx} out of range.")
        else:
            print(f"Warning: Row index {row_idx + 1} out of range.")
    
    # Add space after table
    doc.add_paragraph().paragraph_format.space_after = Pt(12)