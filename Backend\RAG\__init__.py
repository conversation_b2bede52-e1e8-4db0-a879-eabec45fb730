from RAG.chat_summary import generate_chat_master_summary, create_chat_sections_summary
from Config.blobService import blob_service_client
from RAG.retriever import summary_retriever , chunk_retriever
from docx import Document
import io
import json
import os
from DatabaseCosmos.chatDB_Summary_State import initialize_chatDB, getChatDB, updateChatSection, updateChatSummary, updateSimilaritySearchFileIds
from DatabaseCosmos.UserCurrentChat import read_userConversation

class RAG_implementation:
    def __init__(self, id, user_id,top_k=3):
       self.id = id
       self.user_id = user_id
       self.top_k = top_k
       self.fileIds = []
       self.retreiveFileIdIndex = 0
       self.getChatDb = {}

       initialize_chatDB(user_id=self.user_id, id=self.id)

    def initialize_chatDB_object(self):
        initialize_chatDB(user_id=self.user_id, id=self.id)

    def getChatDBObj(self):
        getChatDbObj = getChatDB(self.id)
        return getChatDbObj

    def get_document_from_blob(self , blob_name):
 
        blob_client = blob_service_client.get_blob_client(container=os.getenv("blob_rag_container_name"), blob=blob_name)
        blob_url = blob_client.url
        
        # Download the blob as a stream
        blob_data = blob_client.download_blob()
        doc_bytes = blob_data.readall()
        doc_stream = io.BytesIO(doc_bytes)
        document = Document(doc_stream)
        return document , blob_url

    
    def create_document_summmary(self , blob_name):
        """
        Main function to execute the document processing and chat summary generation workflow.
        """
        # Process and upload the document to upload at indexer

        document , blob_url = self.get_document_from_blob(blob_name)
        # Upload and process the document content
        self.upload_documents_in_raw_and_summary_indexes(document , blob_url)

    def create_chat_sections_and_master_summary(self):
        """
        Generate a summary of chat content from a text file.

        Args:
            file_path (str): The path to the chat history file.

        Returns:
            str: The generated chat summary.
        """
        content = read_userConversation(self.id)
        print("chat Histiry Fetched!!")

        sections_summary_list = create_chat_sections_summary(str(content['History']))  # Create sections from the content
        chat_master_summary = generate_chat_master_summary(sections_summary_list)  # Generate the summary

        for index in range(len(sections_summary_list)):
            sections_summary_list[index] = dict(sections_summary_list[index])

        updateChatSection(id=self.id, chatSections=sections_summary_list)
        updateChatSummary(id=self.id, chatSummary=str(chat_master_summary))
        print("UPDATED")    
        return True

    def retrieve_file_id_and_final_content(self):
        
        chatDBObj = self.getChatDBObj()
        chat_master_summary = chatDBObj["chatSummary"]
        file_ids = summary_retriever(chat_master_summary , self.top_k)  # Retrieve the summary based on the chat
        # Retrieve detailed content related to the file ID
        updateSimilaritySearchFileIds(id=self.id, similarFileIds=file_ids)
        print("FILE ID RECIEVED")
        self.fileIds = file_ids
        return self.fileIds

    def query_rag(self, query):
        chatDBObj = self.getChatDBObj()

        if  chatDBObj["ifSimilaritySearchFileIdsExists"]:
            print("FileIds Exist")
            self.fileIds = chatDBObj["similarFileIds"]
            answer = chunk_retriever(self.fileIds[self.retreiveFileIdIndex], query)
            return answer
        else:

            if chatDBObj["ifChatSummaryExists"]:
                print("Chat Summary Exists")
                self.fileIds = self.retrieve_file_id_and_final_content()
                answer = chunk_retriever(self.fileIds[self.retreiveFileIdIndex], query)
                return answer
            else:
                print("Creating chat summaries and getting file_ids")
                self.create_chat_sections_and_master_summary()
                self.fileIds = self.retrieve_file_id_and_final_content()
                answer = chunk_retriever(self.fileIds[self.retreiveFileIdIndex], query)
                return answer
                
                
                
                # if chatDBObj["ifChatSectionExists"]:
                #     print("Chat Sections Summary Exists")
                #     sectionSummary = chatDBObj["chatSections"]
                #     masterSummary = generate_chat_master_summary(sectionSummary)
                #     updateChatSummary(id = self.id , chatSummary=masterSummary) 
                #     self.fileIds = self.retrieve_file_id_and_final_content()
                #     answer = chunk_retriever(self.fileIds[self.retreiveFileIdIndex], query)
                #     return answer
                # else:
                #     print("Creating chat summaries and getting file_ids")
                #     self.create_chat_sections_and_master_summary()
                #     self.fileIds = self.retrieve_file_id_and_final_content()
                #     answer = chunk_retriever(self.fileIds[self.retreiveFileIdIndex], query)
                #     return answer

