def upsertUserSelectedData(chat_id,selected_list,container):
    print("upsert User Selected Data")
    query = f"SELECT * FROM c WHERE c.chat_id = '{chat_id}'"
    results =[]
    unique_id =""
    user_id=""
    # Execute query and retrieve items
    for item in container.query_items(query=query, enable_cross_partition_query=True):
        description = item.get('description','')
        results.extend(item.get('items', []))
        unique_id = item['id']
        user_id = item['user_id']
    
    # Print retrieved items
    print("Retrieved items:", results)

    # Create a dictionary to map each item by its ID for easy lookup
    existing_items = {item['id']: item for item in results}

    # Step 1: Set isChecked to False for all items in existing_items
    for item in existing_items.values():
        item['isChecked'] = False

    # Step 2: Set isChecked to True for items whose IDs are in selected_list
    for new_item in selected_list:
        new_item_id = new_item.get('id')  # Extract the 'id' from each dictionary
        if new_item_id in existing_items:
            # ID found in existing items, set isChecked to True
            existing_items[new_item_id]['isChecked'] = True
        else:
            # ID not found in existing items, so add new item with isChecked set to True
            results.append({
                "type": "checkbox",
                "label": new_item.get("label"),
                "description": new_item.get("description", ""),
                "id": new_item_id,
                "isChecked": True
            })

    # Prepare the updated items list for the body
    updated_results = list(existing_items.values())

    # Add any new items appended to results
    updated_results.extend([item for item in results if item['id'] not in existing_items])

    # Print the updated results
    print(f"Updated Results: {updated_results}")

    # Prepare the body item with updated results
    body_item = {
        'id': unique_id,
        'chat_id': chat_id,
        'user_id': user_id,
        'createdAt': '2024-11-07',  # Timestamp when the document is created
        'items': updated_results,
        'description': description,
    }

    # Upsert the item in the container
    response = container.upsert_item(body=body_item)
    return response