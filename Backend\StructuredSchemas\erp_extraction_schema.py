from pydantic import BaseModel, Field
from typing import List, Optional

class ERPModel(BaseModel):
    type: str = Field(default="Checkbox")
    label: str = Field(description="Label for the ERP.")
    isChecked: bool= Field(False)
    id: str = Field(description="ID for the ERP. eg:1")
    group: str =Field(description = "it will be same as Label")
    description: str = Field(description="Description of the ERP.")

class ERPStructureModel(BaseModel):
    """It will use to extract ERP ids"""

    erp_list: List[ERPModel] = Field(description="list of ERP's")

    