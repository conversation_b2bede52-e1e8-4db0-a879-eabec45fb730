import azure.functions as func
import logging

from Routes.ApplicationStart import application_start

app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# This Azure Function App uses a modular architecture by leveraging the concept of Blueprints. 
# Blueprints allow different functionalities of the app (authentication, session management, conversation with the AI model, 
# and document handling) to be organized into separate modules, making the codebase easier to maintain and scale


app.register_functions(application_start)





################################################################################################################################

#     # Test Code :

#     # import azure.functions as func
# app = func.FunctionApp()

# @app.route(route="test", auth_level=func.AuthLevel.ANONYMOUS)
# def test(req: func.HttpRequest) -> func.HttpResponse:
#         return func.HttpResponse("Hello World")


################################################################################################################################