# schema define. 
from pydantic import BaseModel, Field
from typing import List, Optional

class SimpleChat(BaseModel):
    """It will use to do simple chat and it will answer user questions by from OpenAI. """

    id: str = Field(description="Chat ID") 
    user_id: str = Field(description="User ID")
   
    viewType: str = Field(description="Category of the view type.")
    content: str = Field(description="Message from the user")

    

    
class SimpleChatSchema(BaseModel):
    output: SimpleChat


