from pptx import Presentation
from pptx.util import Inches
from PIL import Image  
import json
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE_TYPE

class PresentationGenerator:
    def __init__(self, theme_path):
        with open(theme_path, 'r') as file:
            self.theme = json.load(file)
            file.close()
            
        self.generateWithStaticSlides = self.theme.get('generateWithStaticSlides', 'False').strip().lower() == 'true'
        print(self.generateWithStaticSlides)

        if self.generateWithStaticSlides:
            self.template_path = self.theme.get('templateWithStaticSlides')
            self.prs = Presentation(self.template_path)
            self.lengthOfSaticSlides = len(self.prs.slides)
        else:
            self.template_path = self.theme.get('templatePath')
            self.prs = Presentation(self.template_path)
            self.lengthOfSaticSlides = len(self.prs.slides)       

        self.default_bg_path = self.theme.get('defaultBgPath')

        self.default_title_bg_path = self.theme.get('defaultTitleBgPath', self.default_bg_path) if self.theme.get('defaultTitleBgPath') != '' else self.default_bg_path
        self.first_page_bg_path = self.theme.get('firstPageBgPath', self.default_bg_path) if self.theme.get('firstPageBgPath') != '' else self.default_bg_path
        self.last_page_bg_path = self.theme.get('lastPageBgPath', self.default_bg_path) if self.theme.get('lastPageBgPath') != '' else self.default_bg_path

        self.max_table_rows = self.theme.get('table').get('maxRows', 10) if self.theme.get('table').get('maxRows') != '' else 10
        self.max_words = self.theme.get('paragraph').get('maxWords', 100) if self.theme.get('paragraph').get('maxWords') != '' else 100

        self.break_from_title = self.theme.get('breakFromTitle', '') if self.theme.get('breakFromTitle') != '' else ''
        self.add_static_slide_after_slide = int(self.theme.get('addStaticSlideAfterSlide', 0) if self.theme.get('addStaticSlideAfterSlide') != '' or None else 0)

        self.default_font = self.theme.get('font', 'Aptos') if self.theme.get('font') != '' else 'Aptos'

        self.add_number_on_slides = self.theme.get('addNumbersOnSlides', False) if self.theme.get('addNumbersOnSlides') != '' else False

        self.table_style_path = self.theme.get('table').get('tableStylePath', './DocumentGeneration/Presentation/tableStyle.json') if self.theme.get('table').get('tableStylePath') != '' else './DocumentGeneration/Presentation/tableStyle.json'
        self.table_style = self.get_style_name(self.theme.get('table').get('tableStyle', 'Themed Style 1 - Accent 1')) if self.theme.get('table').get('tableStyle') != '' else 'Themed Style 1 - Accent 1'

        self.costingImagePath = self.theme.get('costingImagePath', '')
    
    def apply_title_properties(self, placeholder):
        title_properties = self.theme.get('title', {})
        font_size = title_properties.get('font_size', 32)
        font_color = title_properties.get('font_color', '#3E3E4B')
        bold = title_properties.get('bold', True)

        for paragraph in placeholder.text_frame.paragraphs:
            for run in paragraph.runs:
                run.font.size = Inches(font_size / 72)  # Convert points to inches
                run.font.color.rgb = RGBColor.from_string(font_color.strip('#'))
                run.font.bold = bold

    def apply_paragraph_properties(self, placeholder):
        paragraph_properties = self.theme.get('paragraph', {})
        font_size = paragraph_properties.get('font_size', 24)
        font_color = paragraph_properties.get('font_color', '#787880')
        bold = paragraph_properties.get('bold', False)

        for paragraph in placeholder.text_frame.paragraphs:
            for run in paragraph.runs:
                run.font.size = Inches(font_size / 72)  # Convert points to inches
                run.font.color.rgb = RGBColor.from_string(font_color.strip('#'))
                run.font.bold = bold

    def apply_table_properties(self, table):
        table_properties = self.theme.get('table', {})
        font_size = table_properties.get('font_size', 12)
        font_color = table_properties.get('font_color', '#3D3D4B')
        header_properties = table_properties.get('headers', {})
        header_font_size = header_properties.get('font_size', 14)
        header_font_color = header_properties.get('font_color', '#3D3D4B')
        header_bold = header_properties.get('bold', True)

        for row_idx, row in enumerate(table.rows):
            for cell in row.cells:
                for paragraph in cell.text_frame.paragraphs:
                    for run in paragraph.runs:
                        if row_idx == 0:  # Header row
                            run.font.size = Inches(header_font_size / 72)  # Convert points to inches
                            run.font.color.rgb = RGBColor.from_string(header_font_color.strip('#'))
                            run.font.bold = header_bold
                        else:
                            run.font.size = Inches(font_size / 72)  # Convert points to inches
                            run.font.color.rgb = RGBColor.from_string(font_color.strip('#'))

    def apply_default_font(self):
        for slide in self.prs.slides:
            for shape in slide.shapes:
                if not shape.has_text_frame:
                    continue
                for paragraph in shape.text_frame.paragraphs:
                    for run in paragraph.runs:
                        run.font.name = self.default_font

    def set_background(self, slide, file_path):
        # Set the background image for the slide
        slide_width = self.prs.slide_width
        slide_height = self.prs.slide_height
        image_shape = slide.shapes.add_picture(file_path, Inches(0), Inches(0), width=slide_width, height=slide_height)
        image_shape._element.getparent().remove(image_shape._element)  # Remove image from default order
        slide.shapes._spTree.insert(2, image_shape._element)  # Insert image behind text elements

        return slide
    
    def get_style_name(self, guid):
        with open(self.table_style_path, "r") as f:
            styles = json.load(f)
            f.close()
        return styles[guid]
    
    def create_first_page(self, dictionary):
        for slide_layout in self.prs.slide_layouts:
            if slide_layout.name == "First Page Layout":
                break
        slide = self.prs.slides.add_slide(slide_layout)

        if "bgPath" in dictionary and dictionary['bgPath'] != "":
            slide = self.set_background(slide, dictionary["bgPath"])
        else:
            slide = self.set_background(slide, self.first_page_bg_path)

        # Set the company name and document title
        company_name_placeholder = slide.placeholders[10]  # Company name placeholder
        document_title_placeholder = slide.placeholders[11]  # Document title placeholder
        
        company_name_placeholder.text = dictionary["companyName"]
        document_title_placeholder.text = dictionary["documentTitle"]

        return self.prs

    def create_paragraph_slide(self, dictionary):
        for slide_layout in self.prs.slide_layouts:
            if slide_layout.name == "Paragraph Layout":
                break
        slide = self.prs.slides.add_slide(slide_layout)

        if "bgPath" in dictionary and dictionary['bgPath'] != "":
            slide = self.set_background(slide, dictionary["bgPath"])
        else:
            slide = self.set_background(slide, self.default_bg_path)

        # Set the title and content
        title_placeholder = slide.placeholders[0]  # Title placeholder
        content_placeholder = slide.placeholders[10]  # Content placeholder

        title_placeholder.text = dictionary["title"]
        self.apply_title_properties(title_placeholder) # Using title properties

        paragraph = dictionary["paragraph"]
        words = paragraph.split()

        if len(words) > self.max_words:
            content_placeholder.text = ' '.join(words[:self.max_words])
            self.apply_paragraph_properties(content_placeholder)  # Apply formatting immediately

            remaining_words = words[self.max_words:]
            while remaining_words:
                slide = self.prs.slides.add_slide(slide_layout)
                if "bgPath" in dictionary and dictionary['bgPath'] != "":
                    slide = self.set_background(slide, dictionary["bgPath"])
                else:
                    slide = self.set_background(slide, self.default_bg_path)

                title_placeholder = slide.placeholders[0]
                content_placeholder = slide.placeholders[10]

                title_placeholder.text = dictionary["title"]
                self.apply_title_properties(title_placeholder)
                content_placeholder.text = ' '.join(remaining_words[:self.max_words])
                self.apply_paragraph_properties(content_placeholder)  # Apply formatting immediately

                remaining_words = remaining_words[self.max_words:]

                #self.apply_title_properties(title_placeholder) # Using title
                #self.apply_paragraph_properties(content_placeholder) # Using paragraph properties
        else:
            content_placeholder.text = paragraph
            self.apply_paragraph_properties(content_placeholder) # Using paragraph properties

        return self.prs


    def create_table_slide(self, dictionary):
        for slide_layout in self.prs.slide_layouts:
            if slide_layout.name == "Table Layout":
                break

        table_data = dictionary["table"]
        num_rows = len(table_data)
        num_cols = len(table_data[0])
        headers = table_data[0]
        start_row = 1  # Start from the second row since the first row is headers

        while start_row < num_rows:
            slide = self.prs.slides.add_slide(slide_layout)

            # Set background
            if "bgPath" in dictionary and dictionary['bgPath'] != "":
                slide = self.set_background(slide, dictionary["bgPath"])
            else:
                slide = self.set_background(slide, self.default_bg_path)

            # Set the title
            title_placeholder = slide.placeholders[0]
            title_placeholder.text = dictionary["title"]
            self.apply_title_properties(title_placeholder)

            # Get table placeholder position and size
            table_placeholder = slide.placeholders[10]
            x, y, cx, cy = table_placeholder.left, table_placeholder.top, table_placeholder.width, table_placeholder.height
            # print(x, y, cx, cy)  # Print the dimensions to debug

            end_row = min(start_row + self.max_table_rows - 1, num_rows - 1)  # Adjust for header row

            # Add the table with dynamic size based on the placeholder
            shape = slide.shapes.add_table(end_row - start_row + 2, num_cols, x, y, cx, cy)  # +2 for header row
            table = shape.table

            # Delete the placeholder after adding the table
            slide.shapes._spTree.remove(table_placeholder._element)

            # Apply the custom style to the table
            tbl = shape._element.graphic.graphicData.tbl
            tbl[0][-1].text = self.table_style

            # Write headers to the table
            for col in range(num_cols):
                table.cell(0, col).text = headers[col]

            # Write data to the table
            for row in range(start_row, end_row + 1):
                for col in range(num_cols):
                    table.cell(row - start_row + 1, col).text = table_data[row][col]

            start_row = end_row + 1

            self.apply_table_properties(table)  # Apply custom table properties

        return self.prs

    def create_title_slide(self, dictionary):
        for slide_layout in self.prs.slide_layouts:
            if slide_layout.name == "Title Page Layout":
                break
        slide = self.prs.slides.add_slide(slide_layout)

        if "bgPath" in dictionary and dictionary['bgPath'] != "":
            slide = self.set_background(slide, dictionary["bgPath"])
        else:
            slide = self.set_background(slide, self.default_title_bg_path)

        # Set the title and subtitle
        title_placeholder = slide.placeholders[10]  # Title placeholder
        subtitle_placeholder = slide.placeholders[11]  # Subtitle placeholder
        
        title_placeholder.text = dictionary["title"]
        subtitle_placeholder.text = dictionary["subtitle"]

        return self.prs
    
    def create_costing_slide(self, dictionary):
        for slide_layout in self.prs.slide_layouts:
            if slide_layout.name == "Costing Layout":
                break
        slide = self.prs.slides.add_slide(slide_layout)

        # Set the background image behind the existing image shape
        if "bgPath" in dictionary and dictionary['bgPath'] != "":
            slide = self.set_background(slide, dictionary["bgPath"])
        else:
            slide = self.set_background(slide, self.default_bg_path)

        # Add the image to the placeholder
        picture_placeholder = slide.placeholders[14]
        picture_placeholder.insert_picture(self.costingImagePath)

        # Set the title and other placeholders
        title_placeholder = slide.placeholders[0]  # Title placeholder
        duration_placeholder = slide.placeholders[10]  # Duration placeholder
        budget_placeholder = slide.placeholders[11]  # Budget placeholder
        estimated_hours_placeholder = slide.placeholders[12]  # Estimated Hours placeholder
        total_team_members_placeholder = slide.placeholders[13]  # Total Team Members placeholder

        title_placeholder.text = dictionary["title"]
        duration_placeholder.text = dictionary['duration']
        budget_placeholder.text = dictionary['budget']
        estimated_hours_placeholder.text = dictionary['estimatedHours']
        total_team_members_placeholder.text = dictionary['totalTeamMembers']

        self.apply_title_properties(title_placeholder)  # Using title properties

        return self.prs
    
    def create_image_slide(self, dictionary):
        for slide_layout in self.prs.slide_layouts:
            if slide_layout.name == "Image Layout":
                break
        slide = self.prs.slides.add_slide(slide_layout)

        if "bgPath" in dictionary and dictionary['bgPath'] != "":
            slide = self.set_background(slide, dictionary["bgPath"])
        else:
            slide = self.set_background(slide, self.default_bg_path)

        # Set the title
        title_placeholder = slide.placeholders[0]  # Title placeholder
        title_placeholder.text = dictionary["title"]
        self.apply_title_properties(title_placeholder) # Using title properties

        picture_placeholder = slide.placeholders[10]

        # Add the image
        image_path = dictionary["imagePath"]
        img = Image.open(image_path)
        img_width, img_height = img.size

        # Convert dimensions to PowerPoint's measurement units (inches)
        aspect_ratio = img_width / img_height
        max_width = Inches(8)  # Max width for the image
        max_height = Inches(6)  # Max height for the image

        if img_width > img_height:
            width = min(max_width, Inches(img_width / 96))  # Scale by DPI (96)
            height = width / aspect_ratio
        else:
            height = min(max_height, Inches(img_height / 96))
            width = height * aspect_ratio

        # Add the image, with the placeholder's dimensions using shape index of that placeholder
        left = slide.shapes[2].left
        top = slide.shapes[2].top

        slide.shapes.add_picture(image_path, left, top, width=width, height=height)

        return self.prs

    def generate_presentation(self, list_of_objects):

        if self.generateWithStaticSlides:
            for i in range(self.lengthOfSaticSlides):
                slide = self.prs.slides[i]
                has_background = any(shape for shape in slide.shapes if shape.shape_type == 13)  # 13 is the shape type for pictures, checking if static slide has a background image, if not then add ours
                if not has_background:
                    self.set_background(slide, self.default_bg_path)

        for slide_data in list_of_objects:
            if slide_data["type"] == "paragraph":
                self.create_paragraph_slide(slide_data)
            elif slide_data["type"] == "table":
                self.create_table_slide(slide_data)
            elif slide_data["type"] == "title":
                self.create_title_slide(slide_data)
            elif slide_data["type"] == "image":
                self.create_image_slide(slide_data)
            elif slide_data["type"] == "firstPage":
                self.create_first_page(slide_data)
            elif slide_data["type"] == "costing":
                self.create_costing_slide(slide_data)
            else:
                print(f"Slide type {slide_data['type']} not supported")
        
        print("Presentation generated successfully")
        if self.generateWithStaticSlides:
            self.reindex_slides()

        # Add slide numbers to the bottom left corner of each slide
        if self.add_number_on_slides:
            self.add_slide_numbers()
        
        # Apply default font to all slides
        self.apply_default_font()

        return self.prs
    
    def add_slide_numbers(self):
        for i, slide in enumerate(self.prs.slides):
            left = Inches(0.30)
            top = self.prs.slide_height - Inches(0.80)
            width = Inches(0.5)
            height = Inches(0.5)
            txBox = slide.shapes.add_textbox(left, top, width, height)
            tf = txBox.text_frame
            p = tf.add_paragraph()
            p.text = str(i + 1)
            p.font.size = Inches(0.20)
        return self.prs
    
    def reindex_slides(self):        
        # Reindex slides
        slides_len = list(range(len(self.prs.slides)))
        # print(slides_len)
        layout_slides_1_4 = slides_len[:4]
        layout_slides_5_9 = slides_len[4:11]
        generated_slides = slides_len[self.lengthOfSaticSlides:]

        # print(generated_slides, layout_slides_1_4, layout_slides_5_9)

        #Insert layout slides 1-4 after the first generated slide
        new_order = generated_slides[:self.add_static_slide_after_slide] + layout_slides_1_4 + generated_slides[self.add_static_slide_after_slide:]

        # print(new_order)

        # Find the index of the title slide with title from the breakup point given in theme.json
        if self.break_from_title != '':
            title_slide_index = None
            for i, slide in enumerate(self.prs.slides):
                for shape in slide.shapes:
                    if shape.has_text_frame and shape.text == self.break_from_title:
                        title_slide_index = i
                        break
                if title_slide_index is not None:
                    break
            # print(title_slide_index)

            for index in range(len(new_order)):
                if new_order[index] == title_slide_index:
                    title_slide_index = index
                    break
            # print(title_slide_index)

            #Insert layout slides 5-9 before the title slide
            if title_slide_index is not None:
                new_order = new_order[:title_slide_index] + layout_slides_5_9 + new_order[title_slide_index:]
            else:
                # Insert layout slides 5-9 after the last generated slide
                new_order = new_order[:-1] + layout_slides_5_9 + new_order[-1:] # Insert just before the last slide if title slide not found
        else:
            new_order = new_order[:-1] + layout_slides_5_9 + new_order[-1:]
        
        # print(new_order)

        # Reorder slides based on new_order
        slides = list(self.prs.slides._sldIdLst)
        slides[:] = [slides[i] for i in new_order]
        self.prs.slides._sldIdLst[:] = slides

        print("Slides reindexed successfully")
        return self.prs