# schema define. 
from pydantic import BaseModel, Field
from typing import List, Optional


class CompanyInfo(BaseModel):
    """It will use to search for the company"""
    id: str = Field(description="Chat ID")
    user_id: str = Field(description="User ID")
   
    viewType: str = Field(description="Category of the view type.")
    title: str = Field(description="Name of the Company + state, where it is")
    
    content : str = Field(description="Detailed information or content about the company")
    industry: List[str] = Field(description="Name of the Industry the company belongs to")
    
    SIC_Code: str = Field(description="SIC_Code, id available else return empty string")
    type_of_manufacturing: List[str] = Field(description="type of manufacturing of the company")
    
    headquarters_address: str = Field(description="headquarter address of the company")
    employees: int = Field(description="Number of employees with in the company")
    
    corporate_structure: str = Field(description="Name of the parent company")
    subsidiaries_and_brands: List[str] = Field(description="subsidiaries of the company")
    
    
  
    
    
class WebSearchOutputSchema(BaseModel):
    output: CompanyInfo


