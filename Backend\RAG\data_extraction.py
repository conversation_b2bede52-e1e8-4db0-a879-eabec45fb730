import uuid
import hashlib
import datetime

def metadata_extraction(doc , file_path):
    """
    Extract metadata from a Word document and generate a unique file identifier.

    Args:
        file_path (str): The path to the Word document.

    Returns:
        dict: A dictionary containing the extracted metadata and a unique file ID.
    """
    core_props = doc.core_properties  # Access document core properties

    # Extract relevant metadata fields
    metadata = {
        "author": core_props.author,
        "title": core_props.title,
        "subject": core_props.subject,
        "keywords": core_props.keywords,
        "last_modified_by": core_props.last_modified_by,
        "created": core_props.created,
        "modified": core_props.modified
    }

    # Generate a unique file ID using the current timestamp and title hash
    now = datetime.datetime.now()
    hash_object = hashlib.md5(core_props.title.encode())
    hash_hex = hash_object.hexdigest()[:4]
    guid = uuid.uuid4()
    file_id = f"{now.strftime('%Y%m%d%H%M%S')}-{guid.hex[:12]}-{hash_hex}"
    
    metadata["file_id"] = file_id
    metadata["file_path"] = file_path

    return metadata

def extract_text_and_tables(doc):
    """
    Extract all text and table content from a Word document.

    Args:
        file_path (str): The path to the Word document.

    Returns:
        str: A string containing the extracted text and tables in order.
    """
    content = []

    # Iterate through each element in the document's body
    for element in doc.element.body:
        if element.tag.endswith('tbl'):  # Process tables
            for table in doc.tables:
                if element == table._element:  # Match the table element
                    table_content = []
                    for row in table.rows:
                        row_data = [cell.text.strip() for cell in row.cells]
                        table_content.append(" | ".join(row_data))
                    if table_content:
                        content.append("\n".join(table_content))
                    break
        elif element.tag.endswith('p'):  # Process paragraphs
            for paragraph in doc.paragraphs:
                if element == paragraph._element:  # Match the paragraph element
                    if paragraph.text.strip():  # Add non-empty paragraphs
                        content.append(paragraph.text)
                    break

    # Combine and return the extracted content as a single string
    return "\n".join(content)
