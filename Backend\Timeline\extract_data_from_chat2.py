from utilities.fetchChat import fetchChatSummary
from typing import Dict, List, <PERSON><PERSON>
def extract_data(chat_id: str) -> Tuple[Dict, List, List, List]:
        """
        Extract data from chat history using the chat ID.
        
        Args:
            chat_id: The ID of the chat to extract data from
            
        Returns:
            Tuple of (company_info, data_migration, integration, modules)
        """
        chat_history = fetchChatSummary(
            id=chat_id,
            getChatHistory=False,
            returnRaw=True
        )
        
        # Initialize dictionaries and lists
       
        data_migration = []
        integration = []
        modules = []
        company = []
        
        # Extract data migration information
        if "datamigration" in chat_history:
            data_migration = chat_history["datamigration"]
            print("Data migration" , data_migration)
        
        # Extract integration information
        if "integration" in chat_history:
            integration = chat_history["integration"]
            print("Integration" , integration)
        
        # Extract modules information
        if "modules" in chat_history:
            modules = chat_history["modules"]
            print("Modules" , modules)
        
        filtered_company_data = {}

        if "company" in chat_history:
            company_data = chat_history["company"]
            
         
            for key in ["companyinformation", "companyname", "industry"]:
                if key in company_data:
                    filtered_company_data[key] = company_data[key]

        return modules , integration , data_migration, filtered_company_data
