import os
from dotenv import load_dotenv
from openai import AzureOpenAI
from utilities.openai_services import OpenAIService

load_dotenv()
conversation_histories = {}

# Initialize OpenAI client
endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
deployment = os.getenv("DEPLOYMENT_NAME", "chat")


client = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    azure_endpoint=endpoint,
    api_version="2024-05-01-preview"
)

# Function to handle OpenAI request
def handle_openai_request(user_id, user_input,id):
    
    return OpenAIService.handle_request(
        id,user_id, user_input, client, conversation_histories
    )

