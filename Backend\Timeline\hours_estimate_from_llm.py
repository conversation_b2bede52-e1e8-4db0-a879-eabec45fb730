from typing import Dict, Optional
import json
from Config.azureAI import client
# System prompts
system_prompts = {
    'datamigration': """
    You are a Dynamics 365 Finance and Operations data migration expert. Your task is to estimate the total build effort (in hours) required for data migration based on company-specific details. Consider data extraction, transformation, mapping, validation rules, and script development when calculating build hours.

    Consider industry-specific complexities (e.g., automobile manufacturing has complex product and vendor structures).            Return ONLY a single integer representing total hours needed.
    """,
    
    'modules': """
        You are a Dynamics 365 Finance and Operations integrations expert. Based on the company information and modules provided, estimate the total hours needed for module implementation.
        For each module, consider:

    - Basic configuration complexity
    - Standard out-of-the-box functionality
    - Potential minimal customization needs
    
    Return ONLY a single integer representing total hours needed.
    """,
    
    'erp': """
    You are a Dynamics 365 Finance and Operations integrations expert. Your task is to estimate the total build effort (in hours) required for system integrations based on company-specific details. Consider API development, data mapping, transformation logic, middleware configuration, authentication, error handling, and testing when calculating build hours.

    Consider industry-specific complexities (e.g., healthcare requires strict compliance and real-time data exchange). Return ONLY a single integer representing total hours needed.

    Your response must be a valid JSON string in exactly this format:
    {"total_hours": <number>, "integration_points": {"<point name>": <number>, ...}}

    Example response:
    {"total_hours": 30, "integration_points": {"Sales Order": 120, "Inventory": 80}}
    """
}

# Human prompts
human_prompts = {
    'datamigration': """
    Company Name: {company_name}
    Industry: {industry}
    Company Information: {company_info}

    Information of Data Categories to Migrate and Number of Records per category: 
    {data_categories}

    Estimate total hours for complete data migration.
    """,
    
    'modules': """
    Company Name: {company_name}
    Industry: {industry}
    Company Information: {company_info}

    Modules to Implement:
    {modules}

    Estimate total hours for complete module implementation.
    """,
    
    'erp': """
    Company Name: {company_name}
    Industry: {industry}
    Company Information: {company_info}

    Integration Points:
    {integration_points}

    Provide hours estimate for each integration point in the specified JSON format.
    """
}


def format_data(data_dict: Dict, category: str) -> Dict:
        """Format input data based on category"""
        base_info = {
            'company_name': data_dict['company']['companyname'],
            'industry': data_dict['company']['industry'],
            'company_info': data_dict['company']['companyinformation']
        }
        
        if category == 'datamigration':
            base_info['data_categories'] = "\n".join(
                f"- {item['Category']}: {item['Description']} : {item['Value']} records"
                for item in data_dict['datamigration']
            )
        elif category == 'modules':
            base_info['modules'] = "\n".join(
                f"- {item['Category']}: {item['Training Description']}"
                for item in data_dict['modules']
            )
        elif category == 'erp':
         
            base_info['integration_points'] = "\n".join(
                f"- {item['Integration Point']}: {item['Integration  Description']} : {item['Value']}"
                for item in data_dict['integration']
            )
        return base_info

def get_hours_estimate(data_dict: Dict, category: str) -> Optional[int | Dict]:
    """Get hours estimate for specific category"""
    try:
        formatted_data = format_data(data_dict, category)
        
        messages = [
            {"role": "system", "content": system_prompts[category]},
            {"role": "user", "content": human_prompts[category].format(**formatted_data)}
        ]

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            temperature=0.5,
            max_tokens=150,
            response_format={"type": "json_object"} if category == 'erp' else None
        )

        content = response.choices[0].message.content.strip()
        
        if category == 'erp':
            try:
                return json.loads(content)
            except json.JSONDecodeError as e:
                print(f"Error parsing ERP JSON response: {str(e)}")
                # Return a simple total if JSON parsing fails
                try:
                    total = int(''.join(filter(str.isdigit, content)))
                    return {"total_hours": total, "integration_points": {}}
                except:
                    return None
        else:
            return int(content)

    except Exception as e:
        print(f"Error getting {category} estimate: {str(e)}")
        return None

def get_all_estimates(data_dict: Dict) -> Dict:
    
    return {
        'data_migration': get_hours_estimate(data_dict, 'datamigration'),
        'module_implementation': get_hours_estimate(data_dict, 'modules'),
        'erp_integration': get_hours_estimate(data_dict, 'erp')
    }



