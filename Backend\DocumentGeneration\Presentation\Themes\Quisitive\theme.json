{"title": {"font_size": 32, "font_color": "#3E3E4B", "bold": true}, "paragraph": {"font_size": 24, "font_color": "#787880", "bold": false, "maxWords": 100}, "table": {"font_color": "#3D3D4B", "font_size": 12, "headers": {"font_size": 14, "font_color": "#3D3D4B", "bold": true}, "maxRows": 12, "tableStyle": "Light Style 1 - Accent 4", "tableStylePath": "./DocumentGeneration/Presentation/tableStyle.json"}, "font": "Sofia Pro", "addNumbersOnSlides": "True", "generateWithStaticSlides": "True", "addStaticSlideAfterSlide": 1, "breakFromTitle": "Proposal", "costingImagePath": "./DocumentGeneration/Presentation/Themes/Quisitive/images/costing_img.png", "templatePath": "./DocumentGeneration/Presentation/Themes/Quisitive/slideLayouts/layouts.pptx", "templateWithStaticSlides": "./DocumentGeneration/Presentation/Themes/Quisitive/slideLayouts/layoutsWithStaticSlides.pptx", "defaultBgPath": "./DocumentGeneration/Presentation/Themes/Quisitive/backgrounds/slide2.jpg", "defaultTitleBgPath": "./DocumentGeneration/Presentation/Themes/Quisitive/backgrounds/titlepage.jpg", "firstPageBgPath": "./DocumentGeneration/Presentation/Themes/Quisitive/backgrounds/firstpage.jpg", "lastPageBgPath": ""}