from DatabaseCosmos.StatesChecklist import getUserState


def getSuggestedPills(user_id:str):

    states=getUserState(user_id)
    

    # Mapping of states to their respective pillPlaceholders and pillPrompts
    pill_mapping = {
        'buisness_process': 'Let me select the business process',
        'erp_platform': 'Let me select the ERP platform',
        'application_module': 'Let me select the dynamics application modules',
        'integration': 'Add the scope for integrations',
        'data_migration': 'Add the scope for data migrations',
        'timeline':'Suggest timeline for the project',
        'cost_estimation':'Suggest estimation for the project',
        'document_generate':'Generate a document',
        'ppt_generate':'Generate a presentation'
    }


    try:
        
        pill_array = [
            {
                "pillPlaceolder": key.replace("_", " "), 
                "pillPrompt": pill_mapping[key]
            }
            for key, value in states.items() if value == 'False' and key in pill_mapping
        ]
        # pill_array.append({"pillPlaceolder": "document_generate", "pillPrompt": "generate a document"})
        # pill_array.append({"pillPlaceolder": "ppt_generate", "pillPrompt": "generate a presentation"})

        if not pill_array:
            return []
        
        else:
            return pill_array
    
    except Exception as e:
        return []
    


# getSuggestedPills("roohana")