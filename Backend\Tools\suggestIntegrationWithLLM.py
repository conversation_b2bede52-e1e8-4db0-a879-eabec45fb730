import os
from dotenv import load_dotenv
import pandas as pd
# from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate
from langchain_core.tools import tool
import json

from DatabaseCosmos.Company_info_State import read_CompanyInfo

from DatabaseCosmos.ERP_State import read_ERPState
from DatabaseCosmos.App_module_State import read_AppModule_State
from DatabaseCosmos.Buisness_process_State import read_BPState
from DatabaseCosmos.Integration_State import insert_INT_State, read_INTState
from LLMChains.Base_LLMChain_ModelClass import BaseLLMChainClass
from StructuredSchemas.integrationLLM_extraction_schema import ISVStructureModel
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
from Tools.suggestPills import getSuggestedPills
from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from DatabaseCosmos.StatesChecklist import updateIntegrationState


# Load environment variables from .env file
load_dotenv()


@tool(return_direct=True)
def suggest_integrations(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is used to select / add / recommend / suggest / scope of the Integrations/ISVs"""

    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "integration",
            "errorkey": "companyNameNotFound"
        })
    
    # get suggested pills 
    suggested_pills= getSuggestedPills(user_id)
    integration_list =  read_INTState(id)

    # prompt placeholders
    erp_list = ["Finance and Operations", "Human Resource", "Marketing", "Supply Chain Management"]
    company_details = ""

    if integration_list:
        #return json.dumps(integration_list[0]["object"])
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "integration",
            "tool_name": "integration",
            "title": f"{company_info['companyname']} -> Integrations" if company_info else "Integrations",
            "type": "checkboxWithTextGrouping",
            "description": integration_list[0]["description"] if integration_list[0]["description"] else "Integrations",
            "fields": integration_list[0]["items"],
            "pills": suggested_pills
        })
    else:
        # company_info = read_CompanyInfo(id)

        if company_info:

            company_details = f"""
                            Company Name: {company_info['companyname']}
                            Company Information: {company_info['companyinformation']}
                            Number of Employees: {company_info['employees']}
                            Headquarters Location: {company_info['headquarters_address']}
                            Industry: {company_info['industry']}
                        """
            
            erp_testdata = read_ERPState(id)
            print("Here is list of retrieved erps", erp_testdata)
            if erp_testdata: 

                print("Have both company and erp")

                checked_groups = set()

                # Loop through each item and add group to set if isChecked is True and group exists
                for entry in erp_testdata:
                    for item in entry["items"]:
                        if item.get("isChecked") and item.get("group"):
                            checked_groups.add(item["group"])

                # Convert set to list if needed
                erp_list = list(checked_groups)

                print("here is unique list of erps", erp_list)

        erp_list_formatted = '\n'.join(erp_list)

        bp_info = read_BPState(id)
        if bp_info:
            items = bp_info[0]['items']
            selected_bp = [item for item in items if item["isChecked"]]
            print("BP---:",selected_bp)
            selected_bp = str(selected_bp).replace("{", '').replace("}", '').replace("[", '').replace("]", '').replace("},",'')
        else:
            selected_bp = "null"

        isv_extraction_system_prompt = f"""
Generate a list of Microsoft Dynamics 365 ISVs/Integrations based on the provided ERP platform and optional company details. 
Categorize the integrations into relevant areas based on the ERP platform such as Finance and Operations, Human Resource, Marketing, Supply Chain Management, etc. 
For each category, list 2-4 relevant ISVs or integrations such as Warehouse Management, Transportation Management, Tax Compliance, Banking Systems, etc along with suggested/placeholder values such as Blue Yonder, Vertex, Descartes, Kyriba, etc

ERP Platform:
{erp_list_formatted}

(Optional) Company Details:
{company_details}

Business Process Selection:
{selected_bp}
"""
        isv_extraction_chain = BaseLLMChainClass(isv_extraction_system_prompt, ISVStructureModel)
        
        response = isv_extraction_chain.structure({"input": "Give me list of ISVs/Integrations", "type":""})
        print(response)
        isv_list = response["isv_list"]
        reasoning = response["reasoning"]

        #print("Here is the isv list", isv_list)
        isv_list_only = [
        {
            "type": item.type,
            "label": item.integration_name,
            "inputPlaceHolders": item.placeholder_suggested_value,
            "isChecked": item.isChecked,
            "id": item.id,
            "description": item.description,
            "group": item.group
        }
        for item in isv_list
        ]

        int_object = {
            "id": id,
            "user_id": user_id,
            "viewType": "integration",
            "tool_name": "integration",
            "title": f"{company_info['companyname']} -> Integrations" if company_info else "Integrations",
            "type": "checkboxWithTextGrouping",
            "description": reasoning,
            "fields": isv_list_only,
            "pills": suggested_pills
            }
        
        # insert= insert_INT_State(chat_id=id, int_list=isv_list_only,int_object=int_object, user_id=user_id )
        insert= insert_INT_State(chat_id=id, int_list=isv_list_only, description=reasoning, user_id=user_id )
        print("insertion into db is completed")
        print(insert)
        updateIntegrationState(user_id, "True")

        
        return json.dumps(int_object)

# # # Call the function and print the result
# result = suggest_business_processes(company_info)
# print(result)
