import pandas as pd
df = pd.read_csv(r"./Data/BusinessProcesses.csv", encoding='utf-8')

def get_erp(erp_id_list: list):
    
    # Initialize the result list
    result = []
    
    # Iterate over each row in the dataframe
    for _, row in df.iterrows():
        # Check if the current ID is in the bp_id list
        is_checked = 'true' if row['ID'] in erp_id_list else 'false'
        
        # Create the object with the required structure
        erp_object = {
            "type": "Checkbox",
            "label": row['ERP'],
            "description": row['Purpose'],
            "id": row['ID'],
            "isChecked": is_checked
        }
        
        # Append to the result list
        result.append(erp_object)

    # print(result)
    
    return result

# # Usage
# bp_id_list = ['BP003', 'BP004', 'BP007', 'BP008', 'BP012']
# business_processes = get_business_processes(bp_id_list)
# print(business_processes)
