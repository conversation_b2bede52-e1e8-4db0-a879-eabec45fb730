import os

from Config.blobService import blob_service_client
from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from datetime import datetime, timedelta
from dotenv import load_dotenv
load_dotenv()

container_name = os.getenv("Blob_output_container_name")
input_container_name = os.getenv("Blob_input_container_name")
def GenerateSAS_URL(user_id,id,company_name,type):

    try:
        
        blob_name = f"{user_id}/{id}/{company_name}{type}"
        
    
        sas_token = generate_blob_sas(
            account_name = blob_service_client.account_name,
            container_name = container_name,
            blob_name = blob_name,
            account_key = os.environ['BLOB_ACCOUNT_KEY'],  # Use the account key to sign the SAS token
            permission = BlobSasPermissions(read = True),  # Allow read access only
            expiry = datetime.now() + timedelta(hours = 400),  # Token expires in 4 hour
            protocol = 'https',  # Enforce HTTPS for secure access
            version = '2022-11-02'
        )

        # Full SAS URL
        blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{container_name}/{blob_name}"
        sas_url = f"{blob_url}?{sas_token}"

        if(sas_url):
            print("SAS URL:", sas_url)
            return sas_url
        else:
            return None
    except:
        None



def generate_benchmark_file_sas_url():
    
    # Set the container and folder where the benchmark file is stored
    container_name = os.getenv("Blob_input_container_name")
    folder_path = "Hours Benchmarks/admin/"
    
    # Get container client
    container_client = blob_service_client.get_container_client(input_container_name)
    
    # List blobs in the folder to find the file (assuming there's only one)
    blob_list = list(container_client.list_blobs(name_starts_with=folder_path))
    
    # Get the first (and presumably only) file
    blob_name = blob_list[0].name
    
    # Get blob client
    blob_client = blob_service_client.get_blob_client(
        container=input_container_name,
        blob=blob_name
    )
    
    # Generate SAS token
    sas_token = generate_blob_sas(
        account_name=blob_client.account_name,
        container_name=input_container_name,
        blob_name=blob_name,
        account_key=os.environ['BLOB_ACCOUNT_KEY'],
        permission=BlobSasPermissions(read=True),
        expiry=datetime.now() + timedelta(hours=400),  
        protocol='https',
        version='2022-11-02'
    )
    
    # Construct full SAS URL
    blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{input_container_name}/{blob_name}"
    sas_url = f"{blob_url}?{sas_token}"
    
    return sas_url
    
