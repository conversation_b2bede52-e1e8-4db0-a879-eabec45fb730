from docx import Document
from docx.shared import Pt, RGBColor, Inches
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
import json
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
from docx.enum.text import WD_UNDERLINE
import requests
from io import BytesIO
from DocumentGeneration.Document.cover_titles import WordContentController
from DocumentGeneration.Document.cover_titles import WordContentController2


class DocumentGenerator_user_template:
    def __init__(self, theme_path,content_values):
        """Initialize with document data and template file path."""
        with open(theme_path, 'r') as file:
            self.theme = json.load(file)
        # with open(content_values, 'r') as file:
        #     self.content_values = json.load(file)
        self.content_values = content_values
       
        # self.table_style = self.theme.get('table').get('tableStyle', 'List Table 1 Light Accent 3') if self.theme.get('table').get('tableStyle') != '' else 'Themed Style 1 - Accent 1'
    
    def save_document(self):
        """Save the document to the specified file path."""
        return self.doc
        # self.doc.save(file_path)
 
    def add_content(self, content):
        """Process and add content based on its type."""
        content_type = content.get("type")
       
        if content_type == "section":
            self.add_heading(content.get("text", ""), level=content.get("level", 1))
        elif content_type == "heading":
            self.add_heading(content.get("text", ""), level=content.get("level", 2))
        elif content_type == "paragraph":
            self.add_paragraph(content.get("text", ""), content.get("title", ""))
        elif content_type == "table":
            self.add_table(content.get("data", []), content.get("title", ""))
        elif content_type == "image":  
            self.add_image(content.get("url", ""), content.get("title", ""))
        elif content_type == "list":
            self.add_list(content.get("data", []), content.get("title", ""))
        else:
            print(f"Unsupported content type: {content_type}")
 
    def add_heading(self, title_text, level=1):
        """Add a heading to the document dynamically."""
        if title_text:
            self.doc.add_heading(title_text, level=level)
            return self.doc 
 
    def add_paragraph(self, paragraph_text, title):
        """Add a paragraph to the document."""
        if title != "":
            self.doc.add_paragraph(title)
        if paragraph_text:
            self.doc.add_paragraph(paragraph_text)
 
    def add_table(self, table_data, title):
        """Add a table to the document."""
        if not table_data:
            return
        if title != "":
            self.doc.add_paragraph(title)
       
        # Create the table
        table = self.doc.add_table(rows=1, cols=len(table_data[0]))
        table.rows[0]._tr.get_or_add_trPr().append(parse_xml(r'<w:tblHeader {}/>'.format(nsdecls('w'))))
 
        hdr_cells = table.rows[0].cells
        for i, header in enumerate(table_data[0]):
            hdr_cells[i].text = header
           
     
       
        for row_data in table_data[1:]:
            row_cells = table.add_row().cells
            for i, cell_text in enumerate(row_data):
                row_cells[i].text = cell_text
       
        #if len(self.doc.tables) == 1:
        # for style in self.doc.styles:
        #     if style.type == WD_STYLE_TYPE.TABLE:
        #         print(f"- {style.name}")
           
                   
           
        # table.style = self.table_style
        self.apply_table_borders(table)
        self.apply_table_properties(table)
   
    def add_image(self, image_url, title):
        """Add an image to the document."""
        if title != "":
            self.doc.add_paragraph(title)
        if image_url:
            self.doc.add_picture(image_url)
           
    def apply_cell_style(self, cell, header=False):
        """Apply styling to table cells."""
        cell_paragraph = cell.paragraphs[0]
        cell_paragraph.paragraph_format.left_indent = Inches(0.1)
        cell_paragraph.paragraph_format.right_indent = Inches(0.1)
        cell_paragraph.paragraph_format.space_after = Pt(0)
       
        for run in cell_paragraph.runs:
            run.font.size = Pt(10)
 
        if header:
            for run in cell_paragraph.runs:
                run.font.bold = True
 
    def apply_table_borders(self, table):
        """Apply borders to the table."""
        tbl = table._tbl
        tblBorders = OxmlElement('w:tblBorders')
       
        for side in ['top', 'left', 'bottom', 'right', 'insideH', 'insideV']:
            border = OxmlElement(f'w:{side}')
            border.set(qn('w:val'), 'single')
            border.set(qn('w:space'), '0')
            border.set(qn('w:sz'), '4')
            tblBorders.append(border)
       
        tbl.tblPr.append(tblBorders)
    
    def apply_table_properties(self, table):
        """Apply properties such as font size and color to the table."""
        table_properties = self.theme.get('table', {})
       
        # General font properties
        font_size = table_properties.get('font_size', 10)
        font_color = table_properties.get('font_color', '#3D3D4B')
       
        # Header-specific properties
        header_properties = table_properties.get('headers', {})
        header_font_size = header_properties.get('font_size', 11)
        header_font_color = header_properties.get('font_color', '#3D3D4B')
        header_bold = header_properties.get('bold', True)
       
        # Iterate over all rows in the table
        for row_idx, row in enumerate(table.rows):
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        if row_idx == 0:  # Header row
                            run.font.size = Pt(header_font_size)  # Set font size for header
                            run.font.color.rgb = RGBColor.from_string(header_font_color.strip('#'))  # Set font color for header
                            run.font.bold = header_bold  # Set bold for header
                        else:  # Non-header rows
                            run.font.size = Pt(font_size)  # Set font size for non-header
                            run.font.color.rgb = RGBColor.from_string(font_color.strip('#'))  # Set font color for non-header
                            run.font.bold = False  # Ensure non-header text is not bold
    # Ensure non-header text is not bold
    
    def add_list(self, items, title):
        """Add a bulleted list to the document."""
 
        if title != "":
            self.doc.add_paragraph(title)
        for item in items:
            paragraph = self.doc.add_paragraph()
            run = paragraph.add_run('•\t')  # Add bullet manually
            run.bold = True  # Optional: Make the bullet bold
            paragraph.add_run(item)  # Add the list item text
            paragraph.paragraph_format.left_indent = Inches(0.25)
            paragraph.paragraph_format.space_after = Pt(6)
 
 
    def generate_document_from_user_template(self, document_data, template_path):
        """Generate the document based on the provided document data."""
        
        # to read data from url. 
        response = requests.get(template_path)
        response.raise_for_status()  # Raise an error if the request fails
        self.doc = Document(BytesIO(response.content))
        
        
        self.document_data = document_data
        # To read data from local file
        # self.doc = Document(template_path)  # Initialize the document object
        content_controller = WordContentController(template_path)
        self.doc = content_controller.update_content_controls(self.content_values)
        
        
        for item in self.document_data:
            self.add_content(item)  # Assuming add_content modifies/returns content
        # # # Save the generated document
        # output_file = "new_document10.docx"
        doc=self.save_document()
    
        return doc
        
        
 
 
class DocumentGenerator_base_template:
    def __init__(self, theme_path,content_values):
        """Initialize with document data and template file path."""
        with open(theme_path, 'r') as file:
            self.theme = json.load(file)
            self.content_values = content_values

        #self.default_font = self.theme.get('font', 'Aptos') if self.theme.get('font') != '' else 'Aptos'
        #self.table_style = self.theme.get('table').get('tableStyle', 'List Table 1 Light Accent 3') if self.theme.get('table').get('tableStyle') != '' else 'Themed Style 1 - Accent 1'
 
    def save_document(self, file_path):
        """Save the document to the specified file path."""
        self.doc.save(file_path)
 
    def add_content(self, content):
        """Process and add content based on its type."""
        content_type = content.get("type")
       
        if content_type == "section":
            self.add_heading(content.get("text", ""), level=content.get("level", 1))
        elif content_type == "heading":
            self.add_heading(content.get("text", ""), level=content.get("level", 2))
        elif content_type == "paragraph":
            self.add_paragraph(content.get("text", ""), content.get("title", ""))
        elif content_type == "table":
            self.add_table(content.get("data", []), content.get("title", ""))
        elif content_type == "image":  
            self.add_image(content.get("url", ""), content.get("title", ""))
        elif content_type == "list":
            self.add_list(content.get("data", []), content.get("title", ""))
        else:
            print(f"Unsupported content type: {content_type}")
 
        #self.apply_default_font()
 
 
    def apply_paragraph_properties(self, paragraph):
        paragraph_properties = self.theme.get('paragraph', {})
        font_size = paragraph_properties.get('font_size', 12)
        font_color = paragraph_properties.get('font_color', '#787880')
       
 
        # Iterate through runs in the paragraph
        for run in paragraph.runs:
            run.font.size = Pt(font_size)  # Font size in points
            run.font.color.rgb = RGBColor.from_string(font_color.strip('#'))
           
 
    def apply_paragraph_title_properties(self, title):
        title_properties = self.theme.get('title', {})
        font_size = title_properties.get('font_size', 24)
        font_color = title_properties.get('font_color', '#787880')
        bold = title_properties.get('bold', False)
 
        for run in title.runs:
            run.font.size = Pt(font_size)  # Font size in points
            run.font.color.rgb = RGBColor.from_string(font_color.strip('#'))
            run.font.bold = bold
       
    def add_paragraph(self, paragraph_text, title):
        """Add a paragraph to the document."""
        if title != "":
            title_pro = self.doc.add_paragraph(title)
            self.apply_paragraph_title_properties(title_pro)
        if paragraph_text:
            para = self.doc.add_paragraph(paragraph_text)
            self.apply_paragraph_properties(para)    
 
 
 
    def apply_heading_properties(self, heading, level):
        """
        Applies formatting properties to a heading paragraph based on the theme and level.
 
        Args:
            heading: A `docx.text.Paragraph` object representing the heading.
            level: The heading level (1-6).
        """
        # Get the heading properties for the given level
        heading_properties = self.theme.get('headings', {}).get(str(level), {})
        font_size = heading_properties.get('font_size', 12)
        font_color = heading_properties.get('font_color', '#000000')
        bold = heading_properties.get('bold', False)
        italic = heading_properties.get('italic', False)
        underline = heading_properties.get('underline', False)
 
        # Apply properties to each run in the heading
        for run in heading.runs:
            run.font.size = Pt(font_size)
            run.font.color.rgb = RGBColor.from_string(font_color.strip('#'))
            run.font.bold = bold
            run.font.italic = italic
            run.font.underline = WD_UNDERLINE.SINGLE if underline else None
 
    def add_heading(self, title_text, level=1):
        """Add a heading to the document dynamically."""
        if title_text:
            heading  = self.doc.add_heading(title_text, level=level)
            self.apply_heading_properties(heading, level)
   
    def add_image(self, image_url, title):
        """Add an image to the document."""
        if title != "":
            self.doc.add_paragraph(title)
        if image_url:
            self.doc.add_picture(image_url)      
 
    def add_table(self, table_data, title):
        """Add a table to the document."""
        if not table_data:
            return
        if title != "":
            self.doc.add_paragraph(title)
       
        # Create the table
        table = self.doc.add_table(rows=1, cols=len(table_data[0]))
        table.rows[0]._tr.get_or_add_trPr().append(parse_xml(r'<w:tblHeader {}/>'.format(nsdecls('w'))))
 
        hdr_cells = table.rows[0].cells
        for i, header in enumerate(table_data[0]):
            hdr_cells[i].text = header
       
        for row_data in table_data[1:]:
            row_cells = table.add_row().cells
            for i, cell_text in enumerate(row_data):
                row_cells[i].text = cell_text
                   
        #table.style = self.table_style
        self.apply_table_borders(table)
        self.apply_table_properties(table)
   
 
           
    # def apply_cell_style(self, cell, header=False):
    #     """Apply styling to table cells."""
    #     cell_paragraph = cell.paragraphs[0]
    #     cell_paragraph.paragraph_format.left_indent = Inches(0.1)
    #     cell_paragraph.paragraph_format.right_indent = Inches(0.1)
    #     cell_paragraph.paragraph_format.space_after = Pt(0)
       
    #     for run in cell_paragraph.runs:
    #         run.font.size = Pt(10)
 
    #     if header:
    #         for run in cell_paragraph.runs:
    #             run.font.bold = True
 
    # def apply_default_font(self):
    #     """Apply default font to all text in the document."""
    #     # Iterate over all paragraphs in the document
    #     for paragraph in self.doc.paragraphs:
    #         # Iterate over all runs in the paragraph
    #         for run in paragraph.runs:
    #             # Apply the default font to each run
    #             run.font.name = self.default_font
               
    def apply_table_borders(self, table):
        """Apply borders to the table."""
        tbl = table._tbl
        tblBorders = OxmlElement('w:tblBorders')
       
        for side in ['top', 'left', 'bottom', 'right', 'insideH', 'insideV']:
            border = OxmlElement(f'w:{side}')
            border.set(qn('w:val'), 'single')
            border.set(qn('w:space'), '0')
            border.set(qn('w:sz'), '4')
            tblBorders.append(border)
       
        tbl.tblPr.append(tblBorders)
 
 
 
    def apply_table_properties(self, table):
        """
        Apply properties such as font size, font color, and background color to the table.
 
        Args:
            table: A `docx.table.Table` object to style.
        """
        table_properties = self.theme.get('table', {})
       
        # General font properties
        font_size = table_properties.get('font_size', 10)
        font_color = table_properties.get('font_color', '#3D3D4B')
       
        # Header-specific properties
        header_properties = table_properties.get('headers', {})
        header_font_size = header_properties.get('font_size', 11)
        header_font_color = header_properties.get('font_color', '#3D3D4B')
        header_bold = header_properties.get('bold', True)
        header_bgcolor = header_properties.get('bgcolor', '#0078D7')  # Default light gray background
       
        # Iterate over all rows in the table
        for row_idx, row in enumerate(table.rows):
            for cell in row.cells:
                # Apply background color for header row
                if row_idx == 0:  # Header row
                    cell._tc.get_or_add_tcPr().append(
                        parse_xml(f'<w:shd {nsdecls("w")} w:fill="{header_bgcolor.strip("#")}"/>')
                    )
               
                # Apply font styles for all paragraphs in the cell
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        if row_idx == 0:  # Header row
                            run.font.size = Pt(header_font_size)  # Set font size for header
                            run.font.color.rgb = RGBColor.from_string(header_font_color.strip('#'))  # Set font color for header
                            run.font.bold = header_bold  # Set bold for header
                        else:  # Non-header rows
                            run.font.size = Pt(font_size)  # Set font size for non-header
                            run.font.color.rgb = RGBColor.from_string(font_color.strip('#'))  # Set font color for non-header
                            run.font.bold = False
  # Ensure non-header text is not bold
 
    def add_list(self, items, title):
        """Add a bulleted list to the document."""
 
        if title != "":
            self.doc.add_paragraph(title)
        for item in items:
            paragraph = self.doc.add_paragraph()
            run = paragraph.add_run('•\t')  # Add bullet manually
            run.bold = True  # Optional: Make the bullet bold
            run.font.color.rgb = RGBColor.from_string("0078D7")
            paragraph.add_run(item)  # Add the list item text
            paragraph.paragraph_format.left_indent = Inches(0.25)
            paragraph.paragraph_format.space_after = Pt(6)
            
 
    def generate_document_from_base_template(self, document_data, template_path):
        """Generate the document based on the provided document data."""
        self.document_data = document_data
       

        content_controller = WordContentController2(template_path)
        self.doc = content_controller.update_content_controls(self.content_values)
 
         # Initialize the document object
        for item in self.document_data:
            self.add_content(item)
       
        # # Save the generated document
        # output_file = "new_document10.docx"
        # self.save_document(f".\\base\\output_docs_template1\\{output_file}")
        # print(f"Document saved as {output_file}")
        return self.doc