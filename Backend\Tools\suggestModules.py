import os
from dotenv import load_dotenv
import pandas as pd
# from langchain_openai import AzureChatOpenAI
# from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated

import json

from LLMChains.Base_LLMChain_ModelClass import BaseLLMChainClass
from StructuredSchemas.modules_extraction_schema import ModulesStructureModel
from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.App_module_State import insert_AppModule_State,read_AppModule_State
from DatabaseCosmos.Buisness_process_State import read_BPState
from utilities.get_modules import get_modules

from Config.azureAI import llm

from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from DatabaseCosmos.StatesChecklist import updateAppModState
from DatabaseCosmos.ERP_State import read_ERPState
from Tools.suggestPills import getSuggestedPills
from DatabaseCosmos.Buisness_process_State import read_BPState
from DatabaseCosmos.ERP_State import read_ERPState
# Load environment variables from .env file
load_dotenv()
# Load the local CSV file with ID
df = pd.read_csv(r"./Data/Modules.csv", encoding='utf-8')
print("pandas readed")


modules_extraction_system_prompt = f"""
                You are provided a company's data
                You are also provided ERP selection
                And also provided some Microsoft Dynamics Business processes

                Instructions:
                If ERP selection is provided, display only the modules related to that ERP and business processes.
                If no ERP selection is provided, display modules for both Dynamics 365 Finance & Supply Chain Management and Dynamics 365 Business Central.

                Remember to return just the modules IDs. Note the return modules list can not be empty.
                Transform the given suggestion into the structured output provided.

                Here is a list of available Module IDs with their purposes:
                Module IDs:  {df.to_string(index=False)}
                """

modules_extraction_chain = BaseLLMChainClass(
                                            modules_extraction_system_prompt, 
                                            ModulesStructureModel
                                            )

@tool(return_direct=True)
def suggest_modules_tool(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is used to select / add / recommend / suggest / scope of the Application Modules"""

    #modules_df = pd.read_csv(r"./Data/Modules.csv", encoding='utf-8')
    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "application_module",
            "errorkey": "companyNameNotFound"
        })
    bp_info = read_BPState(id)
    if not bp_info:
        return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "simplechat",
                        "tool_name": "application_module",
                        "content": "You have not selected any Business Process. Please select at least one Business Process to proceed.",
                    })
    erp_info = read_ERPState(id)
    if not erp_info:
        return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "simplechat",
                        "tool_name": "application_module",
                        "content": "You have not selected any ERP. Please select at least one ERP to proceed.",
                    })
    
    # #************************** comment by Roohana
    # module_info = read_AppModule_State(id)
    # if  module_info:
    #      suggested_pills= getSuggestedPills(user_id)
    #      return json.dumps({
    #         "id": id,
    #         "user_id": user_id,
    #         "viewType": "modules",
    #         "tool_name": "application_module",
    #         "title": "Modules",
    #         "type": "Checkbox",
    #         "description": "This form allows you to select relevant modules.",
    #         "fields": module_info[0]["items"],
    #         "pills":suggested_pills
    #     })
    # else:
    #***************************
    module_info = read_AppModule_State(id)
    if  module_info:
        updateAppModState(user_id=user_id,value="True")
        suggested_pills= getSuggestedPills(user_id)
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "modules",
            "tool_name": "application_module",
            "title": f"{company_info['companyname']} -> Modules" if company_info else "Modules",
            "type": "Checkbox",
            "description": module_info[0]["description"] if module_info[0]["description"] else "Modules you have selected",
            "fields": module_info[0]["items"],
            "pills":suggested_pills
        })
    else:
    
                    
        if company_info:
                
                company_input = f"""
                                Company Name: {company_info['companyname']}
                                Company Information: {company_info['companyinformation']}
                                Number of Employees: {company_info['employees']}
                                Headquarters Location: {company_info['headquarters_address']}
                                Industry: {company_info['industry']}
                            """
                
                erp = read_ERPState(id)
                if erp:
                    items = erp[0]['items']
                    selected_erp = [item for item in items if item["isChecked"]]
                    print("ERP---:",selected_erp)
                else:
                    selected_erp = "null"
                #     modules_df['Module Group'] = modules_df['Module Group'].str.lower()
            
                #     if len(selected_erp) == 1 and "finance" in selected_erp[0]["label"].lower():
                #         modules_df = modules_df[modules_df['Module Group'].isin(['finance', 'supply chain management'])]
                #     if len(selected_erp) == 1 and "business central" in selected_erp[0]["label"].lower():
                #         modules_df = modules_df[modules_df['Module Group'].isin(['business central'])] 
                
                # pd.set_option('display.max_rows', None)

                bp_info = read_BPState(id)
                if bp_info:
                    items = bp_info[0]['items']
                    selected_bp = [item for item in items if item["isChecked"]]
                    print("BP---:",selected_bp)
                else:
                    selected_bp = "null"

                if len(selected_erp) == 0 and len(selected_bp) == 0:
                    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "simplechat",
                        "tool_name": "application_module",
                        "content": "You have not selected any ERP or Business Process. Please select at least one ERP and one Business Process to proceed.",
                    })
                elif len(selected_bp) == 0:
                    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "simplechat",
                        "tool_name": "application_module",
                        "content": "You have not selected any Business Process. Please select at least one Business Process to proceed.",
                    })
                elif len(selected_erp) == 0:
                    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "simplechat",
                        "tool_name": "application_module",
                        "content": "You have not selected any ERP. Please select at least one ERP to proceed.",
                    })
                

                modules_df = f"""
                        
                    Company information:'''{company_input}''', \n
                    ERP Selection: '''{selected_erp}''', \n
                    Business Process Selection: '''{selected_bp}''', \n
                    """

                # Invoke the LLM with the prompt
                response = modules_extraction_chain.structure({"input": modules_df})
                print("Modules output",response)
                modules_list = get_modules(response["modules_id"],id)
                reasoning = response["reasoning"]
        else:
                modules_list = get_modules([],id)
                reasoning = "No company information provided."

        # modules_list.sort(key=lambda x: not x["isChecked"])
        insert = insert_AppModule_State(id, modules_list=modules_list, description=reasoning, user_id=user_id)
        print("insertion into db is completed")
        print(insert)
        
        
        
        #Intitializing Company Search State in DB True
        print("updating BP state to true in db -----------------------------------")
        updateAppModState(user_id=user_id,value="True")
        
        # get suggested pills 
        suggested_pills= getSuggestedPills(user_id)
        
        

        return json.dumps({
                    "id": id,
                    "user_id": user_id,
                    "viewType": "modules",
                    "tool_name": "application_module",
                    "title": f"{company_info['companyname']} -> Modules" if company_info else "Modules",
                    "type": "checkboxWithGrouping",
                    "description": reasoning,
                    "fields": modules_list,
                    "pills": suggested_pills
                })
