ppt_template = """
Title: Microsoft Dynamics 365 Finance and Operations Implementation
Content Overview: Provide a concise summary of the project's goals, objectives, and expected outcomes.

Title: Executive Summary
Content Overview: Summarize the company's background, vision, and alignment with the project's objectives.

Title: Scope of Work
Content Overview:
Clearly define the boundaries of the project, including tasks and deliverables.
Include details about:
Business Processes
Modules
ERP Systems
Integrations
Present this information in a tabular format individually for clarity and readability, if the information reagarding details is available in the 'Context'.

Title: Sure Step Methodology
Content Overview:
Provide a visual overview of the Sure Step phases: Diagnostic, Analysis, Design, Development, Deployment, and Operation.
Highlight the key benefits of using a structured methodology: predictable outcomes, reduced risk, and clear expectations.

Title: Phases of Implementation
Content Overview:
Detail each Sure Step phase using bullet points.
For each phase, outline high-level activities to give a clear understanding of the workflow.

Title: Out of Scope
Content Overview:
List the exclusions from the project based on the SOW.
Include any additional exclusions or details provided in the Chat History under this section.

Title: Responsibilities and Project Timeline
Content Overview:
Clearly delineate the responsibilities of both the client and the implementation team. Present this in a table format for easy reference.
Highlight key milestones for each phase of the project timeline.

Title: Next Steps
Content Overview: Actionable items for the client to review and approve the SOW and sign off on the project.
"""


doc_template= """
SOW Presentation Outline

Phase 1: Table of Content
Title: Table of Content
Content Overview: Contains headings with page number.

Phase 2: Introduction
Title: Introduction
Content Overview: Contains introduction of the document

Phase 3: Executive Summary
Title: Executive Summary
Content Overview: Contains Executive Summary

Phase 4: Project Objectives and Scope
Title: Project Objectives and Scope
Content Overview: 
  Subsection: Project Scope
  Content Overview: Subsection description here.
  Subsection: Project Scope Additional
  Content Overview: Subsection description here.
  
Phase 5: Project Approach and Timeline
Title: Project Approach and Timeline
Content Overview: 
  Subsection: Approach Sure Step 365 Methodology
  Content Overview: Subsection description here.
  Subsection: Timeline
  Content Overview: Subsection description here.
  Subsection: Key Project Service Deliverables
  Content Overview: Subsection description here.
  
Phase 6: Project Governance Approach
Title: Project Governance Approach
Content Overview: 
  Subsection: Project Management
  Content Overview: Subsection description here.
  Subsection: Project Completion
  Content Overview: Subsection description here.
  Subsection: General Customer Responsibilities
  Content Overview: Subsection description here.
  Subsection: Project Assumptions
  Content Overview: Subsection description here.
  
Phase 7: Fees
Title: Fees
Content Overview: 

Phase 8: Table 1
Title: Table 1
Content Overview: This section contains tabular data.

Phase 9: Table 2
Title: Table 2
Content Overview: This section contains tabular data.

Phase 10: Table 3
Title: Table 3
Content Overview: This section contains tabular data.

Phase 11: Table 4
Title: Table 4
Content Overview: This section contains tabular data.

Phase 12: Table 5
Title: Table 5
Content Overview: This section contains tabular data.

Phase 13: Table 6
Title: Table 6
Content Overview: This section contains tabular data.

Phase 14: Table 7
Title: Table 7
Content Overview: This section contains tabular data.

Phase 15: Table 8
Title: Table 8
Content Overview: This section contains tabular data.

Phase 16: Table 9
Title: Table 9
Content Overview: This section contains tabular data.

Phase 17: Table 10
Title: Table 10
Content Overview: This section contains tabular data.

Phase 18: Table 11
Title: Table 11
Content Overview: This section contains tabular data.

Phase 19: Table 12
Title: Table 12
Content Overview: This section contains tabular data.

Phase 20: Table 13
Title: Table 13
Content Overview: This section contains tabular data.

Phase 21: Table 14
Title: Table 14
Content Overview: This section contains tabular data.

Phase 22: Table 15
Title: Table 15
Content Overview: This section contains tabular data.

Phase 23: Table 16
Title: Table 16
Content Overview: This section contains tabular data.

Phase 24: Table 17
Title: Table 17
Content Overview: This section contains tabular data.

Phase 25: Table 18
Title: Table 18
Content Overview: This section contains tabular data.

Phase 26: Table 19
Title: Table 19
Content Overview: This section contains tabular data.

Phase 27: Table 20
Title: Table 20
Content Overview: This section contains tabular data.

Phase 28: Table 21
Title: Table 21
Content Overview: This section contains tabular data.

Phase 29: Table 22
Title: Table 22
Content Overview: This section contains tabular data.

Phase 30: Table 23
Title: Table 23
Content Overview: This section contains tabular data.

Phase 31: Table 24
Title: Table 24
Content Overview: This section contains tabular data.

"""



doc_template2="""
Phase 1: Table of Contents
Title: Table of Contents
Content Overview: Contains headings with page numbers.

Phase 2: Introduction
Title: Introduction
Content Overview: Brief introduction to the document.

Subheading Level 2: Executive Summary
Content Overview: Brief executive summary of the company.

Subheading Level 2: Project Objectives and Scope
Content Overview: Overview of the project's objectives and scope.

Subheading Level 3: Objectives
Content Overview: List of project objectives.

Subheading Level 3: Project Scope
Content Overview: Details of the project scope.

Subheading Level 4: General Project Scope
Content Overview: Overview of general project scope.

Subheading Level 4: Software Products/Technologies
Content Overview: List of software products and technologies.

Subheading Level 4: Application Modules/Components In-Scope
Content Overview: Details of in-scope application modules/components.

Subheading Level 4: Processes In-Scope
Content Overview: Overview of in-scope processes.

Subheading Level 4: Additional Project Scope
Content Overview: Details of additional scope items.

Phase 3: Project Approach and Timeline
Title: Project Approach and Timeline
Content Overview: Overview of the project methodology and timeline.

Subheading Level 2: Approach – Sure Step 365 Methodology
Content Overview: Explanation of the Sure Step 365 methodology.

Subheading Level 3: Project Initiation Phase
Content Overview: Activities during the initiation phase.

Subheading Level 3: Analysis Phase
Content Overview: Activities during the analysis phase.

Subheading Level 3: Design Phase
Content Overview: Activities during the design phase.

Subheading Level 3: Development Phase
Content Overview: Activities during the development phase.

Subheading Level 3: Deployment Phase
Content Overview: Activities during the deployment phase.

Subheading Level 3: Operations Phase
Content Overview: Activities during the operations phase.

Subheading Level 2: Timeline
Content Overview: Estimated timeline for the project.

Phase 4: Project Governance Approach
Title: Project Governance Approach
Content Overview: Details of governance structure and processes.

Subheading Level 2: Project Management
Content Overview: Overview of project management activities.

Subheading Level 2: Communication Plan
Content Overview: Communication protocols for the project.

Subheading Level 2: Issue/Risk Management Procedure
Content Overview: Procedure for managing project risks and issues.

Subheading Level 2: Change Management Process
Content Overview: Steps for managing changes in the project scope.

Subheading Level 2: Critical Path Decisions
Content Overview: Key decisions affecting the project timeline.

Subheading Level 2: Project Steering Committee
Content Overview: Roles and responsibilities of the steering committee.

Subheading Level 2: Architecture Board
Content Overview: Technical governance and oversight details.

Phase 5: Fees
Title: Fees
Content Overview: Breakdown of project costs and billing structure.

Phase 6: Tables
Table 1: Overall Project Objectives
Table 2: Services in Scope
Table 3: Software Products/Technologies Required
Table 4-A: In-Scope Application Modules/Components
Table 5: Integration Interfaces in Scope
Table 6: Data Migration Scope
Table 7: Data Migration Responsibilities
Table 8: Custom Code Scope
Table 9: Custom Coding Responsibilities
Table 10: Functional Training Sessions
Table 11: Areas Out of Scope
"""



doc_template2_1 ="""
0.  **Table of Contents**: 
Title: Table of Contents
Content Overview: Contains headings with page numbers.

1. **Document Type**: Professional Project Proposal Document

2. **Purpose**: The purpose of the generated document is to create a comprehensive and detailed project proposal that outlines the project\'s objectives, scope, approach, timeline, governance, and associated fees. This document is intended to provide clear and structured information to stakeholders and decision-makers to facilitate understanding and approval of the project.

3. **Content Organization**: The LLM should organize the content following the provided structure, which includes phases, titles, content overviews, and tables. The document should be divided into sections, each corresponding to a specific phase of the project. Each section should have a title, followed by a content overview that summarizes the headings and titles within that section. Subheadings should be used to further divide the content within each section, ensuring a logical flow and easy navigation. Tables should be included where indicated, and each table should have a name and a brief definition or description of its content.

4. **Formatting Requirements**: 
   - **Headings**: Use clear and distinct headings for each main section (e.g., 1: Introduction, 2: Executive Summary, etc.).
   - **Subheadings**: Include subheadings to organize the content within each main section. Use different levels of subheadings to indicate the hierarchy (e.g., Subheading Level 2, Subheading Level 3, etc.).
   - **Content Overview**: Each main section should start with a content overview that provides a summary of the headings and titles within that section.
   - **Tables**: Where tables are mentioned, include a table structure with a name and definition. Each table should be clearly labeled (e.g., Table 1, Table 2, etc.) and should include a brief description of its content.
   - **Consistent Formatting**: Ensure consistent formatting throughout the document, including font style, size, and alignment for headings, subheadings, and body text.
   - **Phases**: Clearly delineate phases within the "Project Approach and Timeline" section, detailing the specific activities and timelines for each phase.

**Example Structure**:

1. **Introduction**
   - **Title**: Introduction
   - **Content Overview**: Summary of the headings and titles.

2. **Executive Summary**
   - **Title**: Executive Summary
   - **Content Overview**: Summary of the headings and titles.

3. **Project Objectives and Scope**
   - **Title**: Project Objectives and Scope
   - **Content Overview**: Summary of the headings and titles.
   - **Subheading Level 2**: Project Scope
     - **Subheading Level 3**: General Project Scope
     - **Subheading Level 3**: Software Products/Technologies
     - **Subheading Level 3**: Application Modules/Components In-Scope
     - **Subheading Level 3**: Processes In-Scope
   - **Subheading Level 2**: Project Scope – Additional
     - **Subheading Level 3**: Integration and Interfaces
     - **Subheading Level 3**: Data Conversion
     - **Subheading Level 3**: Modification/Enhancement Scope
     - **Subheading Level 3**: Environments and Installation
     - **Subheading Level 3**: Training and Knowledge Transfer
       - **Subheading Level 4**: Informal Knowledge Transfer
     - **Subheading Level 3**: Areas Out of Scope

4. **Project Approach and Timeline**
   - **Title**: Project Approach and Timeline
   - **Content Overview**: Summary of the headings and titles.
   - **Subheading Level 2**: Approach – Sure Step 365 Methodology
     - **Subheading Level 3**: Project Initiation Phase (Week 1 of 21 weeks)
     - **Subheading Level 3**: Analysis & Design Phase (Week 1-6 of 21 weeks)
     - **Subheading Level 3**: Build Phase (Weeks 7-14 of 21 weeks)
     - **Subheading Level 3**: Test & Deployment Phase (Week 15-19 of 21 weeks)
     - **Subheading Level 3**: Operations Phase (Week 20–21 of 21 weeks)
   - **Subheading Level 2**: Timeline
   - **Subheading Level 2**: Key Project Service Deliverables

5. **Project Governance Approach**
   - **Title**: Project Governance Approach
   - **Content Overview**: Summary of the headings and titles.
   - **Subheading Level 2**: Project Management
     - **Subheading Level 3**: Communication Plan
     - **Subheading Level 3**: Issue/Risk Management Procedure
     - **Subheading Level 3**: Change Management Process
     - **Subheading Level 3**: Critical Path Decisions
     - **Subheading Level 3**: Project Steering Committee
     - **Subheading Level 3**: Architecture Board
     - **Subheading Level 3**: Escalation Process
     - **Subheading Level 3**: Service Deliverable Acceptance Process
   - **Subheading Level 2**: Project Completion
   - **Subheading Level 2**: General Customer Responsibilities
   - **Subheading Level 2**: Project Assumptions
     - **Subheading Level 3**: Infrastructure Assumptions
     - **Subheading Level 3**: Other Assumptions

6. **Fees**
   - **Title**: Fees
   - **Content Overview**: Summary of the headings and titles.
   - **Tables**: Include tables with structure definitions where mentioned.
     - **Table 1**: Extract table structure.
     - **Table 2**: Extract table structure.
     - ...
     - **Table 24**: Extract table structure.

"""

doc_template2_lasest= """
1. **Document Type**: Project Proposal Document

2. **Purpose of the Generated Document**: The purpose of the generated document is to outline the objectives, approach, scope, governance, and fees for a proposed project. It serves as a comprehensive guide for stakeholders to understand the project plan, timeline, deliverables, management processes, and expected costs.

3. **Content Organization**:
    - The document should be organized into clearly defined phases, titles, and content overviews.
    - Each section should start with a phase number followed by the title.
    - Each title should be followed by a "Content Overview" which summarizes the main points of the section.
    - The document should include detailed headings and subheadings to break down the information systematically.
    - Where tables are mentioned, include the table name and a brief description of the table structure.

4. **Formatting Requirements**:
    - **Headings**: Use appropriate heading levels (e.g., Heading 1 for main titles, Heading 2 for sections, Heading 3 for sub-sections, etc.).
    - **Subheadings**: Ensure the hierarchy of subheadings is clear and consistent throughout the document.
    - **Content Overview**: Each section must include a "Content Overview" that succinctly summarizes the section\'s content.
    - **Tables**: Where tables are indicated, include a placeholder for the table name and a brief description of the table structure.

---
### 0: Table of Contents
Title: Table of Contents
Content Overview: This section provides a detailed table of contents, listing all the major headings and their corresponding page numbers. e.g Introduction ............................................ page 1 


### 1: Introduction
**Title**: Introduction
**Content Overview**: The introduction summarizes the purpose and structure of this document, outlining its key objectives and sections.

### 2: Executive Summary
**Title**: Executive Summary
**Content Overview**: The executive summary gives an overview of the project's main goals, expected outcomes, and high-level deliverables.

### 3: Project Objectives and Scope
**Title**: Project Objectives and Scope
**Content Overview**: This section outlines the project's objectives, scope, and deliverables. It includes details about the technologies, processes, and goals.

#### Subheading Level 2: Project Scope
#### Subheading Level 3: General Project Scope
#### Subheading Level 3: Software Products/Technologies
#### Subheading Level 3: Application Modules/Components In-Scope
#### Subheading Level 3: Processes In-Scope
#### Subheading Level 2: Project Scope – Additional
#### Subheading Level 3: Integration and Interfaces
#### Subheading Level 3: Data Conversion
#### Subheading Level 3: Modification/Enhancement Scope
#### Subheading Level 3: Environments and Installation
#### Subheading Level 3: Training and Knowledge Transfer
##### Subheading Level 4: Informal Knowledge Transfer
#### Subheading Level 3: Areas Out of Scope

### 4: Project Approach and Timeline
**Title**: Project Approach and Timeline
**Content Overview**: Provide a summary of the headings and titles included in this document.

#### Subheading Level 2: Approach – Sure Step 365 Methodology
#### Subheading Level 3: Project Initiation Phase (Week 1 of 21 weeks)
#### Subheading Level 3: Analysis & Design Phase (Week 1-6 of 21 weeks)
#### Subheading Level 3: Build Phase (Weeks 7-14 of 21 weeks)
#### Subheading Level 3: Test & Deployment Phase (Week 15 - 19 of 21 weeks)
#### Subheading Level 3: Operations Phase (Week 20 – 21 of 21 weeks)
#### Subheading Level 2: Timeline
#### Subheading Level 2: Key Project Service Deliverables

### 5: Project Governance Approach
**Title**: Project Governance Approach
**Content Overview**: Provide a summary of the headings and titles included in this document.

#### Subheading Level 2: Project Management
#### Subheading Level 3: Communication Plan
#### Subheading Level 3: Issue/Risk Management Procedure
#### Subheading Level 3: Change Management Process
#### Subheading Level 3: Critical Path Decisions
#### Subheading Level 3: Project Steering Committee
#### Subheading Level 3: Architecture Board
#### Subheading Level 3: Escalation Process
#### Subheading Level 3: Service Deliverable Acceptance Process
#### Subheading Level 2: Project Completion
#### Subheading Level 2: General Customer Responsibilities
#### Subheading Level 2: Project Assumptions
##### Subheading Level 3: Infrastructure Assumptions
##### Subheading Level 3: Other Assumptions

### 6: Fees
**Title**: Fees
**Content Overview**: Provide a summary of the headings and titles included in this document.


### 7: Tables
**Table 1**: Overall Project Objectives
**Table 2**: Services in Scope
**Table 3**: Software Products/Technologies Required
**Table 4**: In-Scope Application Modules/Components
**Table 5**: Integration Interfaces in Scope
**Table 6**: Data Migration Scope
**Table 7**: Data Migration Responsibilities
**Table 8**: Custom Code Scope
**Table 9**: Custom Coding Responsibilities
**Table 10**: Functional Training Sessions
**Table 11**: Areas Out of Scope
Content Overview:
Outline actionable items for the client, such as reviewing the SOW, providing feedback, and signing off on the project.
Use concise, clear instructions for the client to follow.
"""