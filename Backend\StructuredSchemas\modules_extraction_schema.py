from pydantic import BaseModel, Field
from typing import List, Optional

class ModulesStructureModel(BaseModel):
    """It will use to extract Modules IDs"""

    modules_id: List[str] = Field(description="List of Modules IDs e.g. ['M1', 'M2',..]")
    reasoning: str = Field(description="Short 1 or 2 line description for Reasoning behind the suggested modules. Just for the sake of understanding. Dont mention module IDs in this field.")
