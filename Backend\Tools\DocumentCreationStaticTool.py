from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import io

from docx import Document
from docx.shared import Pt, Inches, RGBColor, Cm
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
from utilities.create_timeline_chart3 import create_gantt_chart, get_ganttchart_data

from DatabaseCosmos.Company_info_State import read_CompanyInfo
from utilities.upload_file import upload_file_to_blob

import re
from datetime import date

from docx.shared import Inches
from LLMChains.document_data_prompts import introduction_data,project_scope_data,executive_summary, project_approch_data,fees_table_data, project_timeline_data
from utilities.fetchChat import fetchChatSummary
from DatabaseCosmos.Timeline import GetTimeline
from utilities.create_timeline_chart import create_chart
from Tools.suggestPills import getSuggestedPills

from docx.oxml.ns import qn
from docx.shared import Pt, RGBColor
from DatabaseCosmos.ProjectEstimation import read_ProjectEstimation_State
from DatabaseCosmos.resource_state import getResources

from Tools.suggestPills import getSuggestedPills

import pandas as pd
import os
from Config.blobService import blob_service_client

# Function to create a document
def create_document():
    return Document()


# Function to add a table to the document
def add_table(document, headers, rows):
    table = document.add_table(rows=1 + len(rows), cols=len(headers))
    table.style = 'Table Grid'

    # Add headers
    header_cells = table.rows[0].cells
    for idx, header in enumerate(headers):
        header_cells[idx].text = header
        header_cells[idx].paragraphs[0].runs[0].font.bold = True

    # Add data rows
    for row_data in rows:
        row_cells = table.add_row().cells
        for idx, cell_data in enumerate(row_data):
            row_cells[idx].text = str(cell_data)


# Assuming `image` contains the image data in bytes
def add_image_to_doc(image_data):
    """Add an image to the Word document."""
    # Save image bytes to an in-memory stream
    image_stream = io.BytesIO(image_data)
    
    return image_stream
    # Add the image to the document
    # doc.add_picture(image_stream, width=Inches(5))  # Adjust width as needed


# Function to add a footer to the document
def add_footer(doc, footer_text):
    """
    Adds a footer with left-aligned text and right-aligned page number to every section in the document.
    :param doc: The `Document` object.
    :param footer_text: The text to add to the footer.
    """
    for section in doc.sections:
        # Access the footer of the section
        footer = section.footer

        # Add the first paragraph for left-aligned footer text
        paragraph1 = footer.paragraphs[0] if footer.paragraphs else footer.add_paragraph()
        run1 = paragraph1.add_run(footer_text)
        run1.font.color.rgb = RGBColor(169, 169, 169)  # Light Grey color
        run1.font.size = Pt(10)  # Set font size
        run1.font.name = 'Segoe UI (Body)'

        # Left-align the footer text
        paragraph1.alignment = 0  # Left alignment (0: left, 1: center, 2: right)

        # Add the second paragraph for right-aligned page number
        paragraph2 = footer.add_paragraph()
        run2 = paragraph2.add_run()

        # Define the XML for inserting the page number field
        fld_code = OxmlElement('w:fldSimple')
        fld_code.set(qn('w:instr'), 'PAGE')  # The PAGE field is for page numbers

        # Add the page number field to the run
        run2._r.append(fld_code)

        # Optionally adjust the font size and style for the page number
        run2.font.size = Pt(10)
        run2.font.name = 'Segoe UI (Body)'

        # Right-align the page number
        paragraph2.alignment = 2  # Right alignment (0: left, 1: center, 2: right)


# # Function to add a table row to the document
# def add_table_row(doc, row_text, insert_before):
#     """
#     Create or append to a table in the document based on row text in Markdown format.
#     The table is inserted immediately before the specified paragraph.
#     """
#     # Split the row text by '|' to create individual cells
#     cells = [cell.strip() for cell in row_text.strip('|').split('|')]

#     # Skip separator rows (e.g., "|---------|")
#     if all(cell.replace('-', '').strip() == '' for cell in cells):
#         return

#     # Check if this is the start of a new table
#     if row_text.startswith("| ") and row_text.strip().endswith("|"):
#         # Create a new table
#         table = doc.add_table(rows=1, cols=len(cells))
#         table.style = 'Table Grid'

#         # Move the table before the placeholder paragraph
#         insert_before._element.addprevious(table._element)

#         # Add header row
#         for i, cell_text in enumerate(cells):
#             header_cell = table.cell(0, i)
#             header_cell.text = cell_text

#             # Apply background color to header cells
#             cell_properties = header_cell._element.get_or_add_tcPr()
#             shading_element = OxmlElement('w:shd')
#             # shading_element.set(qn('w:fill'), 'D9D9D9')  # Light gray background
#             cell_properties.append(shading_element)

#             # Style the header text
#             paragraph = header_cell.paragraphs[0]
#             print("-------------------paragraph---------", paragraph)
#             paragraph = paragraph.replace("**", "")
#             print("-------------------paragraph---------", paragraph)
            
#             run = paragraph.runs[0]
#             # run.bold = True
#             run.font.size = Pt(10)
#             run.font.name = 'Segoe UI (Body)'
#     else:
#         # Append rows to the current table
#         table = insert_before.previous_element  # Reference the previous table
#         row = table.add_row().cells
#         for i, cell_text in enumerate(cells):
#             # Clear the cell text to add formatted content
#             row[i].text = ""
#             paragraph = row[i].paragraphs[0]
#             print("-------------------paragraph---------", paragraph)
#             paragraph = paragraph.replace("**", "")
            
#             print("-------------------paragraph2---------", paragraph)
            

#             # Remove '**' markers from the cell text
#             clean_text = cell_text.replace('**', '').strip()

#             # Add the cleaned text to the paragraph
#             run = paragraph.add_run(clean_text)
#             run.font.size = Pt(10)
#             run.font.name = 'Segoe UI (Body)'

# Function to add a table row to the document
def add_table_row(doc, row_text, insert_before):
    """
    Create or append to a table in the document based on row text in Markdown format.
    The table is inserted immediately before the specified paragraph.
    """
    # Split the row text by '|' to create individual cells
    cells = [cell.strip() for cell in row_text.strip('|').split('|')]

    # Skip separator rows (e.g., "|---------|")
    if all(cell.replace('-', '').strip() == '' for cell in cells):
        return

    # Check if this is the start of a new table
    if row_text.startswith("| ") and row_text.strip().endswith("|"):
        # Create a new table
        table = doc.add_table(rows=1, cols=len(cells))
        table.style = 'Table Grid'

        # Move the table before the placeholder paragraph
        insert_before._element.addprevious(table._element)

        # Add header row
        for i, cell_text in enumerate(cells):
            header_cell = table.cell(0, i)
            
            
            # remove <br> from text 
            header_cell.text = cell_text.replace('<br>', '').strip()
            # Remove '**' markers from the header cell text
            header_cell.text = cell_text.replace('**', '').strip()

            # Apply background color to header cells
            cell_properties = header_cell._element.get_or_add_tcPr()
            shading_element = OxmlElement('w:shd')
            # shading_element.set(qn('w:fill'), 'D9D9D9')  # Light gray background
            cell_properties.append(shading_element)

            # Style the header text
            paragraph = header_cell.paragraphs[0]
            run = paragraph.runs[0]
            run.font.size = Pt(10)
            run.font.name = 'Segoe UI (Body)'


# Markdown to Word processing function
def add_md_paragraph(doc, text, insert_before):
    """Add a paragraph with Markdown interpretation at the specified location."""
    
    def customize_run_style(paragraph, font_size, font_color, font_name):
        """
        Customize the font style of all runs in a paragraph.
        :param paragraph: The paragraph object.
        :param font_size: Font size in points.
        :param font_color: Tuple (R, G, B) for the font color.
        :param font_name: Font name as a string.
        """
        for run in paragraph.runs:
            run.font.size = Pt(font_size)
            run.font.color.rgb = RGBColor(*font_color)
            run.font.name = font_name

    if text.startswith("# "):  # Heading level 1
        paragraph = insert_before.insert_paragraph_before(text[2:].strip())
        paragraph.style = "Heading 1"
        customize_run_style(paragraph, font_size=16, font_color=(0, 120, 215), font_name="Segoe UI Semibold")  # Dark Blue

    elif text.startswith("## "):  # Heading level 2
        paragraph = insert_before.insert_paragraph_before(text[3:].strip())
        paragraph.style = "Heading 2"
        customize_run_style(paragraph, font_size=14, font_color=(80,80,80), font_name="Segoe UI Semibold")  # Black

    elif text.startswith("### "):  # Heading level 3
        paragraph = insert_before.insert_paragraph_before(text[4:].strip())
        paragraph.style = "Heading 3"
        customize_run_style(paragraph, font_size=12, font_color=(80,80,80), font_name="Segoe UI Semibold")  # Black

    elif text.startswith("#### "):  # Heading level 4
        paragraph = insert_before.insert_paragraph_before(text[5:].strip())
        paragraph.style = "Heading 4"
        customize_run_style(paragraph, font_size=11, font_color=(0, 188, 242), font_name="Segoe UI")  # blue

    elif text.startswith("##### "):  # Heading level 5
        paragraph = insert_before.insert_paragraph_before(text[6:].strip())
        paragraph.style = "Heading 5"
        customize_run_style(paragraph, font_size=10, font_color=(80,80,80), font_name="Segoe UI (Body)")  # Black

    elif text.startswith("- "):  # Bullet point
        paragraph = insert_before.insert_paragraph_before()
        paragraph.style = "List Bullet"
        parts = re.split(r"(\*\*|\*|__|)", text[2:].strip())  # Split for bold/italic
        bold, italic = False, False
        for part in parts:
            if part == "**":  # Toggle bold
                bold = not bold
            elif part == "*":  # Toggle italic
                italic = not italic
            else:
                run = paragraph.add_run(part)
                run.bold = bold
                run.italic = italic
        customize_run_style(paragraph, font_size=10, font_color=(80, 80, 80), font_name="Segoe UI (Body)")  # Black

    elif "|" in text and text.startswith("|"):  # Handle simple Markdown table row
        # Assuming `add_table_row` is defined elsewhere
        print("Adding table row:", text)  # Debugging print
        add_table_row(doc, text,insert_before)

    else:  # Plain text or Markdown with bold/italic
        parts = re.split(r"(\*\*|\*|__|)", text)  # Split for bold/italic
        bold, italic = False, False
        paragraph = insert_before.insert_paragraph_before()
        for part in parts:
            if part == "**":
                bold = not bold
            elif part == "*":
                italic = not italic
            else:
                run = paragraph.add_run(part)
                run.bold = bold
                run.italic = italic
                # Customize font properties
                run.font.size = Pt(10)
                run.font.name = 'Segoe UI (Body)'
        customize_run_style(paragraph, font_size=10, font_color=(80, 80, 80), font_name="Segoe UI (Body)")  #Black


# Function to replace a placeholder with Markdown content
def replace_with_md(doc, placeholder, md_text):
    """Replace a placeholder in the document with parsed Markdown text."""
    for paragraph in doc.paragraphs:
        if placeholder in paragraph.text:
            insert_before = paragraph  # Get the position of the placeholder
            paragraph.text = ""  # Clear the placeholder
            for line in md_text.strip().splitlines():
                add_md_paragraph(doc, line, insert_before)
            break  # Exit after replacing the placeholder


# Function to replace a placeholder with an image
def adding_image_data_to_doc(doc, placeholder, image, width=None, height=None):
    """
    Replace a placeholder in the document with an image.
    :param doc: The `Document` object.
    :param placeholder: The placeholder text to replace.
    :param image_path: The path to the image file.
    :param width: Optional width of the image (in inches).
    :param height: Optional height of the image (in inches).
    """
    
    for paragraph in doc.paragraphs:
        if placeholder in paragraph.text:
            # Clear the placeholder text
            paragraph.text = ""

            # Add the image to the same paragraph
            run = paragraph.add_run()
            
            # image = io.BytesIO(image_data)
            
            if width and height:
                run.add_picture(image, width=Inches(width), height=Inches(height))
            elif width:
                run.add_picture(image, width=Inches(width))
            elif height:
                run.add_picture(image, height=Inches(height))
            else:
                run.add_picture(image)
            break  # Exit after replacing the first matching placeholder


# def fees_rates_per_hour_estimation(user_id,id):
    
#     container_name = os.getenv("Blob_output_container_name")
#     container_client = blob_service_client.get_container_client(container_name)
#     folder_path = f"Resource_costing/"
#     blob_list = container_client.list_blobs(name_starts_with=folder_path)
#     blobs = list(blob_list)
#     if not blobs:
#         return json.dumps({
#             "id": id,
#             "user_id": user_id,
#             "viewType": "error",
#             "tool_name": "costEstimation",
#             "errorkey": "ResourceCostingNotFound"
#         })
   
#     file_blob = blobs[0]
#     blob_client = container_client.get_blob_client(file_blob.name)
#     download_stream = blob_client.download_blob()
#     file_content = download_stream.readall()
#     resource_costing_df = pd.read_csv(io.BytesIO(file_content))
    
#     return resource_costing_df

#This is the new function for fees_rates_per_hour_estimation as per dyanmic resorce concept
def fees_rates_per_hour_estimation(id):
    get_resources = getResources(id)
    # Extract Role and Rate into a list of dictionaries
    rate_per_resource = [{"Role": resource["role"], "Rate": resource["rate"]} for resource in get_resources["resources"]]

    # Create DataFrame
    resource_costing_df = pd.DataFrame(rate_per_resource)
    return resource_costing_df


# Updated `createDynamicFlowPPT_Tool` function
@tool(return_direct=True)
def createDynamicFlowDocument_Tool(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool generates a detailed document when user asked to create or draft a document or word document and returns the document's URL."""

    docx_url = ""

    intro_msg_summary = fetchChatSummary(id=id, getBP=False, getModules=False, getERP=False, getIntegration=False, getDM=False, getChatHistory=False)

    if intro_msg_summary == "":
        intro_msg_summary = "Want to create a detailed document for the company: "
    document_type = "company_name"
    company_info = read_CompanyInfo(id)
    if company_info:
        document_type = company_info['companyname']
    else:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name":"documentcreation",
            "errorkey": "companyNameNotFound"
        })
        
    print("Conversational History...", intro_msg_summary)



    msg_summary = fetchChatSummary(id=id)
    
    if msg_summary == "":
        msg_summary = "Want to create a detailed document for the company: "
        
    print("Conversational History...", msg_summary)


    ##################################### Doc Creation #####################
    # get the static a Word document
    # read the document. It contains the static data with title page, in which we add the dynamic data. 
    doc_path = "./Data/SOWMicrosoftDynamic_static.docx"
    doc = Document(doc_path)
    
    
    # placeholder data containes the placeholder name, from title page which we need to replace with the dynamic data.
    username = "John"
    placeholder_data = {
        "#companyname#": document_type,
        "#username#": username,
        "#date#": str(date.today()),
    }
    
    # Replace placeholders in the document
    for paragraph in doc.paragraphs:
        for placeholder, replacement in placeholder_data.items():
            if placeholder in paragraph.text:
                paragraph.text = paragraph.text.replace(placeholder, replacement)
                
        
    for table in doc.tables:
            # Iterate over all rows in the table
            for row in table.rows:
                # Iterate over all cells in the row
                for cell in row.cells:
                    # Replace #companyname# with the actual company name in each cell
                    if '#companyname#' in cell.text:
                        cell.text = cell.text.replace('#companyname#', document_type)



    # Add footer with custom text
    footer_text = f"""_________________________________________________________________________________________________________
    \nStatement of Work (SOW) for '{document_type}' Microsoft Dynamics 365 \nImplementation Version 1.0 Draft \nLast modified on {date.today()}"""
    add_footer(doc, footer_text)
    timelinedata=""
    timeline_image = ""
    

    # get timeline data and create image
    timelinedata = GetTimeline(id)


    projectapproch_image = "./Data/projectApproachImage.png"
    
    #get project estimation data i.e costing.
    projectestimation_data = read_ProjectEstimation_State(id)
    
    print("--------------------- projectestimation_data ----------------------", projectestimation_data)
    
    rates_per_hour = "TBD"
    try:
        #rates_per_hour = fees_rates_per_hour_estimation(user_id, id)
        # New function call as per resource dynamic allocation
        rates_per_hour = fees_rates_per_hour_estimation(id)
    except Exception as e:
        print(f"error in reading the resource costing file {e}")
        
    finally:
 
        fees_estimation_data = {
            "projectestimation": projectestimation_data,
            "rateperhour": rates_per_hour
        }
        
    
    intro_data = introduction_data(intro_msg_summary)
    executiveSummary_data = executive_summary(intro_msg_summary)
    projectScope_data = project_scope_data(msg_summary)

    projectApproch_data = project_approch_data(msg_summary)
    feesTable_data = fees_table_data(fees_estimation_data)

    if timelinedata:
        # timeLine_data = project_timeline_data(timelinedata['timeline'])
        timeLine_data = project_timeline_data(timelinedata['timeline']['data'])

        timeline_dict = timelinedata['timeline']
        timeline_df = pd.DataFrame.from_dict(timeline_dict['data'])

        ganttchart_data = get_ganttchart_data(timeline_df)
        image = create_gantt_chart(ganttchart_data)
        # image = create_chart(timelinedata['timeline'])

        timeline_image = add_image_to_doc(image)
    
        replace_with_md(doc, "#timeline#", timeLine_data)
        adding_image_data_to_doc(doc, "#timeline_image#", timeline_image, width=6, height=5)
        
    else: 
        replace_with_md(doc, "#timeline#", "No data available")
        replace_with_md(doc, "#timeline_image#", "No data available")
    
    # Replace Markdown placeholders with formatted content
    replace_with_md(doc, "#introduction#", intro_data)
    replace_with_md(doc, "#executivesummary#", executiveSummary_data)
    replace_with_md(doc, "#projectscope#", projectScope_data)
    replace_with_md(doc, "#projectapproach#", projectApproch_data)
    
    adding_image_data_to_doc(doc, "#ProjectApproachImagePlaceholder#", projectapproch_image)
    
    replace_with_md(doc, "#fees#", feesTable_data)
        
        
    # Save the document
    docx_file_path = f"{user_id}/{id}/{document_type}_document.docx"

    # Save the rendered document to an in-memory stream
    output_stream = io.BytesIO()
    doc.save(output_stream)
    output_stream.seek(0)  # Reset stream position for uploading
    # doc.save(file_path)

    #generating sas url
    # print("generating SAS URL....")
    docx_url = upload_file_to_blob(
                                file_name=docx_file_path,
                                output_stream=output_stream,
                                user_id=user_id,
                                id= id            
                                )
    
    ppt_url = ""
    # get suggested pills 
    suggested_pills = getSuggestedPills(user_id)

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "documentcreation",
                        "tool_name":"documentcreation",
                        "url": {
                            "ppt_url" : ppt_url,
                            "docx_url": docx_url,
                        },
                        "pills" : suggested_pills
                    }
                )

