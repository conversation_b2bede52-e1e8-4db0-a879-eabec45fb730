from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import io

from docx import Document
from docx.shared import Pt, Inches, RGBColor, Cm
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.oxml import OxmlElement

from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.Buisness_process_State import read_BPState
from DatabaseCosmos.App_module_State import read_AppModule_State
from DatabaseCosmos.ERP_State import read_ERPState
from DatabaseCosmos.UserCurrentChat import read_userConversation, upsert_userConversation
from utilities.upload_file import upload_file_to_blob
from utilities.download_template import download_template_from_blob
from utilities.default_template import doc_template, doc_template2
from add_title import title_page

# from langchain.memory import ConversationBufferMemory
from Config.azureAI import llm
from langchain_core.prompts import ChatPromptTemplate
import re
from pptx import Presentation
from pptx.util import Pt, Inches
from pptx.enum.text import PP_ALIGN
from pptx.enum.text import MSO_ANCHOR
from datetime import date

import os

from LLMChains.document_static_data_prompts import introduction_data,project_scope_data,executive_summary, project_approch_data,project_governance_data,fees_table_data, project_timeline_data,project_governace_static,area_out_of_scope_static
from utilities.fetchChat import fetchChatSummary
from DatabaseCosmos.Timeline import GetTimeline
from utilities.create_timeline_chart import create_chart

file_path1 = "./src/presentation/QuisitiveTemplate/Slide1.JPG"
file_path2 = "./src/presentation/QuisitiveTemplate/Slide2.JPG"
file_path3 = "./src/presentation/QuisitiveTemplate/Slide3.JPG"
file_path4 = "./src/presentation/QuisitiveTemplate/Slide4.JPG"

# blob_name = "PPT3_Template.pptx"

# context_extraction_template = """
# I am providing you an extracting text template from a PPT as 'context'
# You have to make a system prompt that I would pass to another LLM and that LLM will create the text according to the flow given in the PPT.
# Take the context just as a template, don't mention the details in it
# Remember to mention title from template, Don't mention content from it

# You have to analyze the given 'context' and Remember to create a precise and to the point system prompt from it, as a PPT generation template
# context: '''{context}'''

# system prompt:
# """

# context_extraction_prompt = ChatPromptTemplate.from_messages(
#             [
#                 ("system", context_extraction_template),
#                 ("human", "{input}")
#             ]
#         )

# # Create a LangChain chain to use the template 
# # this is use to create LLM prompt on the basis of template provided
# context_extraction_chain = context_extraction_prompt | llm


# LLM Prompt that generate document content
document_creation_template = """
Todays Date: {date}
You are providing a 'Template Guide' and 'context'
You have to create a detail SOW document on the basis of 'Template Guide' according to the 'context' provided. 
You need to explain or add things related to the 'context' that is provided to you in order to create a detail SOW document.
you also need to create table where its given in the template. 
Try to stick with the provided context or data for document generation. 

Document is generating by Quisitive
Rememeber the following things:
- Not to mention numbers while generating list.
- Create tables for the headings or sub headings where content 'Template Guide' has mentioned 'Table' in it
- Also add things that user had asked in the 'Chat History' in 'context'
following are the details:

Document Template Guide: '''{ppt_system_prompt}'''

context: '''{context}'''
"""

document_creation_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", document_creation_template),
                ("human", "{input}")
            ]
        )

document_creation_chain = document_creation_prompt | llm




# Function to create a document
def create_document():
    return Document()

# Function to add a title to the document
def add_title(document, title):
    title_para = document.add_paragraph()
    title_run = title_para.add_run(title)
    title_run.bold = True
    title_run.font.size = Pt(24)
    title_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

# Function to add content to the document
def add_content(document, content):
    content_para = document.add_paragraph(content)
    content_para.style.font.size = Pt(12)

# Function to add a table to the document
def add_table(document, headers, rows):
    table = document.add_table(rows=1 + len(rows), cols=len(headers))
    table.style = 'Table Grid'

    # Add headers
    header_cells = table.rows[0].cells
    for idx, header in enumerate(headers):
        header_cells[idx].text = header
        header_cells[idx].paragraphs[0].runs[0].font.bold = True

    # Add data rows
    for row_data in rows:
        row_cells = table.add_row().cells
        for idx, cell_data in enumerate(row_data):
            row_cells[idx].text = str(cell_data)

# Assuming `image` contains the image data in bytes
def add_image_to_doc(doc, image_data):
    """Add an image to the Word document."""
    # Save image bytes to an in-memory stream
    image_stream = io.BytesIO(image_data)
    # Add the image to the document
    doc.add_picture(image_stream, width=Inches(5))  # Adjust width as needed

        
   

# def add_index_section(doc, content, level=1):
#     """
#     Recursively adds index content (headings, subheadings, sub-subheadings) to the document.

#     :param doc: The `Document` object to which the index is added.
#     :param content: The index content to render (list of dictionaries).
#     :param level: The current depth level (1 for main heading, 2 for subheading, etc.).
#     """
#     for item in content:
#         # Add main heading
#         para = doc.add_paragraph()
#         run = para.add_run(item['title'])
        
#         # Set font size, font, and color
#         run.font.size = Pt(10)  # Font size set to 10
#         run.font.name = 'Segoe UI'  # Font family set to 'Segoe UI'
#         run.font.color.rgb = RGBColor(0, 0, 0)  # Font color set to black

#         # Ensure no bold or italic (they are the default so we don't explicitly set them)
#         # Remove any unwanted styles (bold, italic)
#         run.bold = False
#         run.italic = False

#         # Limit heading levels (Heading 1, Heading 2, etc.)
#         para.style = f'Heading {level}' if level <= 3 else 'Normal'

#         # Handle subheadings recursively
#         subheadings = item.get('subheadings', [])
#         if isinstance(subheadings, dict):  # Check if subheadings are structured
#             add_index_section(doc, [subheadings], level=level + 1)
#         elif isinstance(subheadings, list):  # Handle plain lists of subheadings
#             for sub in subheadings:
#                 sub_para = doc.add_paragraph(f"    {sub}", style=f'Heading {level + 1}' if level + 1 <= 3 else 'Normal')
                
#                 # Set font size, font, and color for subheadings
#                 sub_run = sub_para.add_run(sub)
#                 sub_run.font.size = Pt(10)  # Font size set to 10
#                 sub_run.font.name = 'Segoe UI'  # Font family set to 'Segoe UI'
#                 sub_run.font.color.rgb = RGBColor(0, 0, 0)  # Font color set to black

#                 # Ensure no bold or italic for subheadings
#                 sub_run.bold = False
#                 sub_run.italic = False

#                 sub_para.alignment = 0  # Left align for subheadings
                
                
def add_footer(doc, footer_text):
    
    
    """
    Adds a footer with left-aligned text and right-aligned page number.
    :param doc: The `Document` object.
    :param footer_text: The text to add to the footer.
    """
    # Access the first section (this will be used for adding footers)
    section = doc.sections[0]
    
    # Access the footer of the section
    footer = section.footer

    # Add the first paragraph for left-aligned footer text
    paragraph1 = footer.paragraphs[0] if footer.paragraphs else footer.add_paragraph()
    run1 = paragraph1.add_run(footer_text)
    run1.font.color.rgb = RGBColor(169, 169, 169)  # Light Grey color
    run1.font.size = Pt(10)  # Set font size
    run1.font.name = 'Segoe UI (Body)'

    # Left-align the footer text
    paragraph1.alignment = 0  # Left alignment (0: left, 1: center, 2: right)

    # Add the second paragraph for right-aligned page number
    paragraph2 = footer.add_paragraph()
    run2 = paragraph2.add_run()

    # Define the XML for inserting the page number field
    fld_code = OxmlElement('w:fldSimple')
    fld_code.set(qn('w:instr'), 'Page No.')  # The PAGE field is for page numbers
    fld_code.set(qn('w:str'), '1')
    # fld_code.set(qn('w:instr'), 'PAGE')  # The PAGE field is for page numbers

    # Add the page number field to the run
    run2._r.append(fld_code)
    
    # Optionally adjust the font size and style for the page number
    run2.font.size = Pt(10)
    run2.font.name = 'Segoe UI (Body)'

    # Right-align the page number
    paragraph2.alignment = 2  # Right alignment (0: left, 1: center, 2: right)






# Updated `createDynamicFlowPPT_Tool` function
@tool(return_direct=True)
def createDynamicFlowDocument_Tool(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool generates a detailed document when user asked to create or draft a document or word document and returns the document's URL."""
    # print("----------------------------------------")
    # print("---------- In document creation tool --------------")

    docx_url = ""

    intro_msg_summary = fetchChatSummary(id=id, getBP=False, getModules=False, getERP=False, getIntegration=False, getDM=False, getChatHistory=False)

    if intro_msg_summary == "":
        intro_msg_summary = "Want to create a detailed document for the company: "
    document_type = "company_name"
    company_info = read_CompanyInfo(id)
    if company_info:
        document_type = company_info['companyname']
    else:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "errorkey": "companyNameNotFound"
        })
        
    print("Conversational History...", intro_msg_summary)



    msg_summary = fetchChatSummary(id=id)
    
    # print("--------------------------------------------")
    # # print("-------------------------------------------- msg_summary:   ", msg_summary)
 
    if msg_summary == "":
        msg_summary = "Want to create a detailed document for the company: "
        
    print("Conversational History...", msg_summary)

   
# ------------------     
    
    
    # get static headings data from LLM

    # Example index content
    index_content=[
        {"title": "Introduction", "subheadings": []},
        {"title": "Executive Summary", "subheadings": []},
        {"title": "Project Objectives and Scope", "subheadings":["Project Scope", "General Project Scope", "Table 2: Services in Scope","Software Products/Technologies","Table 3: Software Products/Technologies Required", "Application Modules/Components In-Scope","Table 4: In-Scope Application Modules/Components", "Processes In-Scope","Table 5: In-Scope Processes","Project Scope - Additional", "Integration and Interfaces","Table 6: Integration Interfaces in Scope","Table 7: Integration Interfaces Responsibilities","Data Conversion","Table 8: Data Migration Scope","Table 9: Data Migration Responsibilities","Modification/Enhancement Scope","Table 10: Custom Code Scope","Table 11: Custom Coding Responsibilities","Environments and Installation","Training and Knowledge Transfer","Informal Knowledge Transfer"]},
        {"title": "Project Approach", "subheadings": ["Approach - Sure Step 365 Methodology", "Project Initiation Phase"," Analysis & Design Phase","Build Phase","Test & Deployment Phase","Operations Phase","Key Project Service Deliverables","Timeline Approach"]},
        {"title": "Project Governance", "subheadings": ["Project Management","Communication Plan","Issue/Risk Management Procedure","Change Management Process","Critical Path Decisions","Project Steering Committee","Architecture Board","Escalation Process","Service Deliverable Acceptance Process","Project Completion","General Customer Responsibilities","Project Assumptions","Infrastructure Assumptions","Other Assumptions"]},
        {"title": "Fees", "subheadings": []},
    ]


    ##################################### Doc Creation #####################
    # Create a new Word document
    # doc = Document()

    # # Add content to the document
    # doc.add_heading(f"Statement of Work (SOW) for \n '{document_type}' ", level=1)
    
    # doc.add_heading(f"")
    # doc.add_heading(f"Microsoft Dynamics 365 Implementation", level=1)
    # doc.add_heading(f"")
    
    # doc.add_heading(f"Prepared for", level=5 )
    # doc.add_heading(f"{document_type}", level=4)
    # doc.add_heading(f"{date.today()}",level=4)
    # doc.add_heading("Version 1.0 Final",level=4)
    # # Add footer with custom text
    # footer_text = f"""_______________________________________________________________________________________________________
    # \nStatement of Work (SOW) for '{document_type}' Microsoft Dynamics 365 \nImplementation Version 1.0 Draft \nLast modified on {date.today()}"""
    # add_footer(doc, footer_text)
    
    


    
    # doc.add_page_break()
    
    # get image
    timelinedata = GetTimeline(id)
    
    
    # image = create_chart(timelinedata['timeline'])
    
   
    intro_data = introduction_data(intro_msg_summary)
    # executiveSummary_data = executive_summary(intro_msg_summary)
    

    # projectScope_data = project_scope_data(msg_summary)
    # areaOutOfScope_data = area_out_of_scope_static()
    # projectGovernance_data= project_governace_static()
    # projectApproch_data = project_approch_data(msg_summary)
    
    # timeLine_data = project_timeline_data(timelinedata['timeline'])
    
    # # # projectGovernance_data = project_governance_data(msg_summary)
    # feesTable_data = fees_table_data(msg_summary)
    
    # Add title for the index page
    # doc.add_heading('Introduction', level=2)
    
    
    # --------------- add Title Page ------------------
    
    # doc_path = "./Data/SOW_Document_Static -Final.docx"
    
    doc_path = "./Data/SOW_Microsoft_Dynamics_front_page.docx"
    
    doc_path = title_page(doc_path, document_type, "John")
    doc = Document(doc_path)
    
        

     
     # Add title for the index page
    doc.add_heading('Table of Content', level=1)

    # Loop through the index content to add headings and subheadings
    
    
 
    # Iterate through each item in the index content
    for index, item in enumerate(index_content, start=1):
        # Add main heading with custom font settings
        para = doc.add_paragraph(f"{index}. {item['title']}", style='Normal')
        run = para.runs[0]  # Get the run object (the text part)
        run.font.size = Pt(10)
        run.font.name = 'Segoe UI'
        run.font.color.rgb = RGBColor(0, 0, 0)  # Black color
        para.alignment = 0  # Left align the paragraph

        # Add subheadings (if any) with custom font settings
        for sub_index, subheading in enumerate(item.get('subheadings', []), start=1):
            sub_para = doc.add_paragraph(f"    {index}.{sub_index} {subheading}", style='Normal')
            sub_run = sub_para.runs[0]  # Get the run object (the text part)
            sub_run.font.size = Pt(10)
            sub_run.font.name = 'Segoe UI'
            sub_run.font.color.rgb = RGBColor(0, 0, 0)  # Black color
            sub_para.alignment = 0  # Left align the paragraph

                
    # Add a page break after the index page
    doc.add_page_break()
    
    
    

    
        
    for line in str(intro_data).splitlines():
        add_md_paragraph(doc, line)
    
    doc.add_page_break()
    
    # for line in str(executiveSummary_data).splitlines():
    #     add_md_paragraph(doc, line)
    # doc.add_page_break()
    
    # for line in str(projectScope_data).splitlines():
    #     add_md_paragraph(doc, line)
    # for line in areaOutOfScope_data.splitlines():
    #     if line.strip():
    #         add_md_paragraph(doc, line)
        
    # doc.add_page_break()
        
    # # for line in str(projectApproch_data).splitlines():
    # #     add_md_paragraph(doc, line)
    # # doc.add_page_break()
    
    # for line in str(timeLine_data).splitlines():
    #     add_md_paragraph(doc, line)
        
    # # dump image into document. 
    # add_image_to_doc(doc, image)
    # doc.add_page_break()
        
    # # for line in str(projectGovernance_data).splitlines():
    # #     add_md_paragraph(doc, line)  
    # # doc.add_page_break()
    
    # for line in projectGovernance_data.splitlines():
    #     if line.strip():  # Skip empty lines
    #         add_md_paragraph(doc, line)
    # doc.add_page_break()
        
    # for line in str(feesTable_data).splitlines():
    #     add_md_paragraph(doc, line)
    
               
        
        
    # Save the document
    docx_file_path = f"{user_id}/{id}/{document_type}_document.docx"

    # Save the rendered document to an in-memory stream
    output_stream = io.BytesIO()
    doc.save(output_stream)
    output_stream.seek(0)  # Reset stream position for uploading
    # doc.save(file_path)

    #generating sas url
    # print("generating SAS URL....")
    docx_url = upload_file_to_blob(
                                blob_name=docx_file_path,
                                output_stream=output_stream
                                )
    
    ppt_url = ""

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "documentcreation",
                        "tool_name":"documentcreation",
                        "url": {
                            "ppt_url" : ppt_url,
                            "docx_url": docx_url,
                        }
                    }
                )






################################ Docx Creation ############################################


def add_md_paragraph(doc, text):
    """Add a paragraph with Markdown interpretation for headings, bold, italics, lists, and tables."""
    # Heading level 1
    if text.startswith("# "):  
        heading = doc.add_heading(text[2:].strip(), level=1)
        heading_run = heading.runs[0]
        heading_run.font.size = Pt(16)
        heading_run.font.name = 'Segoe UI Semibold'
        heading_run.font.color.rgb = RGBColor(0,120,215) # Dark Blue
        heading.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
    
    # Heading level 2   
    elif text.startswith("## "):  
        heading = doc.add_heading(text[3:].strip(), level=2)
        heading_run = heading.runs[0]
        heading_run.font.size = Pt(14)
        heading_run.font.name = 'Segoe UI Semibold'
        heading_run.font.color.rgb = RGBColor(80,80,80) # Black
        heading.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
        # Add left indentation
        # heading.paragraph_format.left_indent = Cm(0.4)
        # heading.paragraph_format.space_before = Pt(12)
        # heading.paragraph_format.space_after = Pt(6)
      
    # Heading level 3  
    elif text.startswith("### "):  
        heading = doc.add_heading(text[4:].strip(), level=3)
        heading_run = heading.runs[0]
        heading_run.font.size = Pt(12)
        heading_run.font.name = 'Segoe UI Semibold'
        heading_run.font.color.rgb = RGBColor(80, 80, 80)  # Black
        heading.paragraph_format.left_indent = Cm(0.5)
        heading.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
        
    # Heading level 4    
    elif text.startswith("#### "):  
        heading = doc.add_heading(text[5:].strip(), level=3)
        heading_run = heading.runs[0]
        heading_run.font.size = Pt(11)
        heading_run.font.name = 'Segoe UI'
        heading_run.font.color.rgb = RGBColor(0, 188, 242)  # Blue
        heading.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
        
    elif text.startswith("##### "):  # Heading level 5
        heading = doc.add_heading(text[6:].strip(), level=4)
        heading_run = heading.runs[0]
        heading_run.font.size = Pt(10)
        heading_run.font.name = 'Segoe UI (Body)'
        heading_run.font.color.rgb = RGBColor(80,80,80)  # Black
        heading.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
        
    #   # NEW CONDITION: Check for text starting with ** (bold line)
    # elif text.startswith("**"):
    #     paragraph = doc.add_paragraph()
    #     run = paragraph.add_run(text[2:].strip())  # Strip "**" from the start
    #     run.bold = True
    #     run.font.size = Pt(10)  # Set font size
    #     run.font.name = 'Segoe UI (Body)'  # Set font style
    #     paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT    
        
    elif text.startswith("- "):  # Bullet point
        # doc.add_paragraph(text[2:].strip(), style='ListBullet')
        paragraph = doc.add_paragraph(style='ListBullet')
        # Handle bold within bullet points
        parts = re.split(r"(\*\*|\*|__|)", text[2:].strip())  # Split by Markdown syntax for bold/italic
        bold, italic = False, False
        for part in parts:
            if part == "**":  # Toggle bold state
                bold = not bold
            elif part == "__":  # Toggle bold state (for handling underscores as bold)
                bold = not bold
            elif part == "*":  # Toggle italic state
                italic = not italic
            else:
                run = paragraph.add_run(part)
                run.bold = bold  # Apply bold formatting
                run.italic = italic  # Apply italic formatting
                run.font.size = Pt(10)  # Set the font size to 10 points
                run.font.name = 'Segoe UI (Body)'  # Set the font style
    
    elif text.startswith("** "):  # Sub-bullet point (Bold)
        doc.add_heading(text[2:].strip(),  level=5)
    #     bullet = doc.add_paragraph(text[2:].strip(), style='ListBullet')
    #     bullet.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
    
    # elif text.startswith("** "):  # Sub-bullet point
    #     doc.add_heading(text[2:].strip(),  level=5)
        
    elif text.startswith("* "):  # Sub-bullet point
        # doc.add_paragraph(text[2:].strip(), style='ListBullet2')
        bullet = doc.add_paragraph(text[2:].strip(), style='ListBullet')
        bullet.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
        
    elif text.startswith("|") and "|" in text:  # Table row
        add_table_row(doc, text)
    else:
        # Regular paragraph with bold and italic handling
        paragraph = doc.add_paragraph()
        parts = re.split(r"(\*\*|\*|__|)", text)  # Split by Markdown syntax for bold/italic
        bold, italic = False, False
        for part in parts:
            if part == "**":  # Start or end of bold text
                bold = not bold  # Toggle bold state
            elif part == "__":
                bold = not bold  # For handling __ as bold, same logic as **
            # if part in ("** ", "__"):
            #     bold = not bold
            elif part == "*":
                italic = not italic
            else:
                run = paragraph.add_run(part)
                run.bold = bold
                run.italic = italic
                # Add font size and style (customize as needed)
                run.font.size = Pt(10)  # Set the font size to 10 points
                run.font.name = 'Segoe UI (Body)'  # Set the font style 


# def add_table_row(doc, row_text):
#     """Create or append to a table in the document based on row text in Markdown format."""
#     global current_table
#     # Split the row text by '|' to create individual cells
#     cells = [cell.strip() for cell in row_text.strip('|').split('|')]
    
#     if all(cell.replace('-', '').strip() == '' for cell in cells):
#         return

#     # Check for table headers (assume headers start new tables)
#     if current_table is None or row_text.startswith("| ") and row_text.strip().endswith("|"):
#         # Create a new table
#         current_table = doc.add_table(rows=1, cols=len(cells))
#         current_table.style = 'Table Grid'
#         # Add header row
#         for i, cell_text in enumerate(cells):
#             current_table.cell(0, i).text = cell_text
#     else:
#         # Add a row to the current table
#         row = current_table.add_row().cells
#         for i, cell_text in enumerate(cells):
#             row[i].text = cell_text

# # Initialize global variable for tracking the current table
# current_table = None

# def add_table_row(doc, row_text):
#     """Create or append to a table in the document based on row text in Markdown format."""
#     global current_table, is_header_row

#     # Split the row text by '|' to create individual cells
#     cells = [cell.strip() for cell in row_text.strip('|').split('|')]

#     # Skip separator rows (e.g., "|---------|")
#     if all(cell.replace('-', '').strip() == '' for cell in cells):
#         is_header_row = False  # Mark that header row is complete
#         return

#     # Check for table headers (assume headers start new tables)
#     if current_table is None or (is_header_row and row_text.startswith("| ") and row_text.strip().endswith("|")):
#         # Create a new table
#         current_table = doc.add_table(rows=1, cols=len(cells))
#         current_table.style = 'Table Grid'

#         # Add header row
#         for i, cell_text in enumerate(cells):
#             header_cell = current_table.cell(0, i)
#             header_cell.text = cell_text

#             # Apply background color only to header cells
#             cell_properties = header_cell._element.get_or_add_tcPr()
#             shading_element = OxmlElement('w:shd')
#             shading_element.set(qn('w:fill'), '0070C0')  # Light blue background
#             cell_properties.append(shading_element)

#             # Style the header text (font size, font style, color)
#             paragraph = header_cell.paragraphs[0]
#             run = paragraph.runs[0]
#             run.font.size = Pt(8)  # Font size
#             run.font.name = 'Segoe UI'  # Font style
#             run.font.bold = True  # Bold for header
#             run.font.color.rgb = RGBColor(0, 0, 0)  # Black text

#         is_header_row = False  # Mark header row as processed

#     else:
#         # Add a row to the current table (body rows)
#         row = current_table.add_row().cells
#         for i in range(len(current_table.columns)):
#             if i < len(cells):
#                 row[i].text = cells[i]  # Add cell content
#             else:
#                 row[i].text = ''

#         # No background shading for body rows (ensured by default)
#         for cell in row:
#             cell_properties = cell._element.get_or_add_tcPr()
#             # Ensure no shading is added for body rows
#             shading_element = cell_properties.find(qn('w:shd'))
#             if shading_element is not None:
#                 cell_properties.remove(shading_element)

# # Initialize global variables for tracking the current table and header state
# current_table = None
# is_header_row = True



def add_table_row(doc, row_text):
    """Create or append to a table in the document based on row text in Markdown format."""
    global current_table

    # Split the row text by '|' to create individual cells
    cells = [cell.strip() for cell in row_text.strip('|').split('|')]

    # Skip separator rows (e.g., "|---------|")
    if all(cell.replace('-', '').strip() == '' for cell in cells):
        return

    # Check for table headers (assume headers start new tables)
    if current_table is None or row_text.startswith("| ") and row_text.strip().endswith("|"):
        # Create a new table
        current_table = doc.add_table(rows=1, cols=len(cells))
        current_table.style = 'Table Grid'
        
        # Add header row
        for i, cell_text in enumerate(cells):
            header_cell = current_table.cell(0, i)
            header_cell.text = cell_text

            # Apply background color to header cells
            cell_properties = header_cell._element.get_or_add_tcPr()
            shading_element = OxmlElement('w:shd')
            # shading_element.set(qn('w:fill'), '0070C0')  # Blue background
            cell_properties.append(shading_element)

            # Style the header text (font size, font style, color)
            paragraph = header_cell.paragraphs[0]
            run = paragraph.runs[0]
            run.font.size = Pt(9)  # Font size
            run.font.name = 'Segoe UI'  # Font style
            # run.font.color.rgb = RGBColor(255, 255, 255)  # White text
    else:
        # Add a row to the current table
        row = current_table.add_row().cells
        for i, cell_text in enumerate(cells):
            row[i].text = cell_text

# Initialize global variable for tracking the current table
current_table = None


