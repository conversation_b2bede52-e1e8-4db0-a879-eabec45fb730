import logging
import random
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from utilities.CreateGuid import generate_guide
from Config.cosmosdb import database
from DatabaseCosmos.UpdatingCheckboxAndCheckboxWithGrouping import upsertUserSelectedData
# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="ProjectEstimation",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)

""" - Parameters
chat_id = 123(guid),
modules_list = []
user_id:123(guid)
"""
def insert_ProjectEstimation_State(total_hours_per_resource,total_hours_per_phase,total_hours_all_resources,months,phase_rate,budget_list ,average_rate, user_id: str,id):
    logging.info("Inserting row in ProjectEstimation Container")
    print("Inserting row in ProjectEstimation Container")
    try:
        guid=generate_guide()
        item = {
                'id': guid,
                'user_id': user_id,
                'chat_id':id,
                'createdAt': datetime.utcnow().isoformat(),
                "Resources": total_hours_per_resource,  
                "Phases": total_hours_per_phase,
                "Estimated Hours": total_hours_all_resources,
                "months":months,
                "PhaseRate":phase_rate,
                "Budget":budget_list,
                "AverageRate":average_rate
            }
        
        container.create_item(body=item)
        print(f'Created new item with Id {guid}')
        return item
        
    except Exception as e:
         print("Error" ,e)
        
         print("Read Failed!")
         return None       


def read_ProjectEstimation_State(chat_id):
    print("reading Application Module")
    try:
        logging.info('Applying query.')
        query = f"SELECT * FROM c WHERE c.chat_id = '{chat_id}'"
       
        item= container.query_items(query = query, enable_cross_partition_query = True)
        
        print(f"Retrieved (query) .",item)

        return list(item)[0]
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
    
        return None
    
def update_ProjectEstimation(items):
    
    try:
            
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
        

