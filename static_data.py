
project_scope= [
  {
    "type": "heading",
    "level": 1,
    "text": "Project Objectives and Scope"
  },
  {
    "type": "heading",
    "level": 2,
    "text": "Project Scope "
  },
  {
    "type":"heading",
    "level":3,
    "text":"General Project Scope"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"The project scope includes all tasks and criteria agreed upon with the customer and to deem the project a success; the approach by which to implement this scope is explained in this section."
  },
  {
    "type":"heading",
    "level":4,
    "text":"Implementation Services"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"Implementation services include services for project management, change management, system design and documentation, testing, and training."
  },
   {
    "type":"heading",
    "level":4,
    "text":"Project Management Methodology"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"We follow an industry-standard implementation methodology; Microsoft's Sure Step Agile Methodology (referenced in Section 3.1 (figure 1). Sure Step is a comprehensive customer engagement methodology describing the processes and disciplines necessary to implement Microsoft Dynamics products. We have aligned Sure Step with strong internal project management practices and tools that have historically enabled us to successfully deliver implementation projects that align with a customer's strategic goals and drivers."
  }, 
  {
    "type":"heading",
    "level":4,
    "text":"Leverage Standard Functionality"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"We plan to maximize the software's standard delivered functionality and look to find creative ways to configure gaps between Customer's requirements, its business processes and the standard software. Any customizations or modifications to the system will need to be approved by Customer prior to being completed."
  }, 
  {
    "type":"heading",
    "level":4,
    "text":"Knowledge Transfer"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"We utilize a train-the-trainer approach to maximize Customer's knowledge of the software platform and business processes."
  },
]

environments_and_installation=[
  {
    "type":"heading",
    "level":2,
    "text":"Environments and Installation"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"The environments listed in the following table will be set up and configured as part of this project. Setup Responsibility relating to installation of Microsoft Dynamics components are in scope as outlined in section 2.2.3 in conformance with the standards, as specified in the latest installation guidelines. Prerequisites are customer responsibilities, such as Azure subscription and hardware procurement, installation, operating system installation, prerequisite software, etc. as applicable.\nNote that it is Customer's responsibility to confirm that the subscriptions, hardware and software required to set up the environments is procured and set-up on time to meet the schedule defined in this document and later set forth in the Quisitive project plan. Any delays to the Quisitive project plan caused by delays in availability of subscriptions, hardware or software are subject to the Change Request process described in Section 4.1.3 Change Management Process. Quisitive will provide a 4-week notice to Customer prior to any infrastructure requests for each of the environments listed.\n It is also Customer's responsibility to support the following environments for backups and operating system maintenance. Quisitive will setup and configure Dynamics 365 as applicable within the Quisitive scope as mentioned in Section 2.2.3.\nThe following section specifies the environments required to support the project and when those environments are required relative to the Sure Step 365 project phases. All Environments will be virtual environments."
    
  },
  {
    "type":"table",
    "title":"Table: Required Environments",
    "data":[
      ["Environment","Location","Primary Installation Responsibility","Shadow Responsibility", "Environment Purpose","Ready by"],
      ["Sandbox","Azure *",	"Consultant","Customer","Testing of configurations which may be disruptive to the primary test activities","End of Project Initiation Phase"],
      ["Development Environments with ADO","Azure *",	"Consultant","Customer", "Code Development","Within [1] weeks after Initiation Phase completion"],
      ["Test",	"Azure *",	"Customer","Consultant","Requirements Analysis and Requirements Testing","Within [2] weeks after Initiation Phase completion"],
      ["Production",	"Azure *",	"Customer","Consultant","Live Operation Use","[1] weeks prior to go-live date"]
    ]
  },
  {
    "type":"heading",
    "level":3,
    "text":"Training and Knowledge Transfer"
  },
  {
    "type":"heading",
    "level":4,
    "text":"Informal Knowledge Transfer"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"A “train-the-trainer” approach will be employed to enable Customer super-users to train end users. Prior to User Acceptance Test, customer can access Dynamics 365 Sales training materials through customer center. From these, Customer will then create more pertinent and specific end user training material to be used during End User training. Knowledgeable Customer project team members (referred to as Subject Matter Experts [SMEs] or Super Users) will train end users prior to Go Live. This approach provides the Customer with increased Microsoft Dynamics knowledge and ability to support after Go Live in addition to minimizing Consultant time.\nTraining is very important and will be held continuously throughout the project. Prior to the start of the project, the Microsoft Dynamics Learning Portal will be used to give background and foundational training to Customer on Microsoft Dynamics 365. Training of SMEs should start early in the project through informal touchpoints with Dynamics 365. Touchpoints allow SMEs to get some basic knowledge of the system. By doing so repetitively, SMEs will become experts in the processes and functionality of Dynamics 365 prior to formal training sessions. This method will also improve the level of understanding of the Customer, so that the SMEs will be able to execute training for additional users as needed.\nSince formal and informal training and knowledge transfer will occur over the course of the implementation, there will be portions that will be completed onsite versus off site. When laying out the project plan, we will ensure that important training activities are conducted in person."
  },
  {
    "type":"table",
    "title":"Table: Functional Training Sessions",
    "data":[
      ["Category","Training Description",	"Total (Hrs.)"],
      ["Methodology","Training	Training on Methodology and Project Management concepts"	,"2"],
      ["SME Training","Training to prepare users to run Conference Room Pilot Scripts"	,"20"],
      ["End User Training Assistance","Customer will develop any custom user training materials/guides and conduct end user training. Consultant will assist Customer in preparation of:  training plans, training environments, training materials and training.",	"20"],
      ["","Total",	"42"]
    ]
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"Quisitive assumes that the Customer project team will also leverage the online training available in the Customer Source Dynamics website."
  }
]

out_of_scope=[
  {
    "type": "heading",
    "level": 3,
    "text": "Areas Out of Scope"
  },
  {
    "type": "paragraph",
    "title": "",
    "text": "Any area that is not explicitly listed in Sections 2.2 and 2.3 as “In-Scope” is out-of-scope for this engagement. Areas that are out of scope for this engagement include, but are not limited to, the following:"
  },
  {
    "type":"table",
    "title": "Table: Areas Out of Scope",
    "data":[
      ["Area", "Description"],
      ["Microsoft Product Licenses","Microsoft product licenses and/or software subscriptions will not be provided under this SOW. Customer is responsible for acquiring all necessary product licenses and/or software subscriptions required as a result of this SOW." ],
      ["Hardware", "Hardware will not be provided under this SOW. Customer is responsible for acquiring all necessary hardware."],
      ["Infrastructure Setup" ,"Infrastructure (required hardware and software) and/or Azure Cloud setup including any High Availability/Disaster Recovery/Load balanced environments is not in scope under this SOW."],
      ["Legacy Systems","Quisitive will not be responsible for supporting Customer legacy systems."],
      ["Integration with 3rd Party Software","Quisitive will not be responsible for integration with 3rd Party Software (not applicable to Dynamics ISVs) which are not part of the scope."],                                                                                                                                                                        
      ["Source code review","Customer will not provide Quisitive with access to non-Quisitive source code or source code information."],                                                                                                                                                                                                              
      ["Requirements Gathering","Quisitive will analyze the requirements documented for processes identified as in scope. Any business process outside of the scope will not be analyzed as part of this SOW."],                                                                                                                                      
      ["Documentation","Preparation of documentation about any existing process, previous standards, policies, or existing guidelines is out of scope under this SOW."],                                                                                                                              
      ["Process Re-engineering/Process Mapping","Design of functional business components of the solution unless specifically included in scope and delivered by Quisitive Consulting staff. Process engineering, or re-engineering activities are outside of the scope of the Quisitive responsibilities and are assumed to be completed by Customer."],                               
      ["Adoption & Change Management","Design or re-design of Customer's functional organization unless specifically included in scope and delivered by Quisitive Consulting staff."],                                                                                                                                                                       
      ["Updates Patches and Fixes", "Upgrades, Updates, Fixes and Patches are included in this scope of work, specifically: \n\tOperating Systems Versions, updated service packs, or hotfixes.\n\tDynamics for Finance & Operations Component Versions, platform updates, service packs, or hotfixes, Design Change Request (DCR).      \n\tOther Server or Customer Framework Component Versions. \nIf a fix or patch is identified as required after the start of the Project, an assessment will be made by the Project management team from Quisitive and Customer as to the need for such a fix. \nIf the fix is deemed needed by both parties to enable functionality required by Customer, Quisitive and Microsoft will make the change. \nIf the fix or patch is due to defects in the Microsoft software, Microsoft will fix at no charge to the Customer."],                                                                                                                                                                                                     
      ["Global Solution Needs","Language implementations that are not part of the core product will not be implemented under this SOW."],                                                                                                                                                                                                             
      ["Business Intelligence and Data Warehousing","Unless specifically detailed in this document, implementation and/or data replication to support business intelligence and data warehousing are not included in this SOW."],
    ]
  }
]

key_project_servie_deliverables=[
  {
    "type": "heading",
    "level": 2,
    "text": "Key Project Service Deliverables"
  },
  {
    "type": "paragraph",
    "title": "",
    "text": "The following is a list of the key Project Service Deliverables that will be produced within the scope of this SOW described in section 2.2.4. These deliverables will be reviewed under the process described in Section 4.1.8"
  },
  {
    "type": "table",
    "title": "Table: Deliverables",
    "data":[
      ["Deliverable",	"Description / Responsibility"],
      ["Project Plan",	"•	Provides scheduled tasks and activities for project team and dependencies between tasks.\n•	Describes key milestones, deliverables and deadlines.\n•	This deliverable is initially provided and owned by Quisitive, but then jointly maintained by “#companyname#” and Quisitive."],
      ["Issue Log (maintained in Azure DevOps )", "•	Provides list of project issues, recommended action and party to whom the action is assigned.\n•	Includes date opened, last updated, issue description, status, date closed \n•	This deliverable is initially provided and owned by Quisitive, but then jointly maintained by “#companyname#” and Quisitive."],
      ["Weekly Status Report",	"•	Describes weekly project progress including key tasks for prior week and current week, key issues, performance to schedule, budget and any key PM issues.\n•	This deliverable is initially provided and owned by Quisitive, but then jointly maintained by “#companyname#” and Quisitive."],
      ["Functional Requirement List & Sign off",	"•	Identifies functional requirements for all areas affected by MS Dynamics 365 Identifies whether requirement is met with standard functionality, a workaround or a modification, and a brief description of how requirement will be addressed if necessary.\n•	Identifies and prioritizes modifications.\n•	This deliverable is provided by Quisitive with key input/assistance and sign-off exclusively from “#companyname#”."],
      ["Modification Design Specification","•	Each specification describes the functional requirement, the basis for modification, other options/workarounds considered, and the changes requested to the standard product, sufficient for development of code.\n•	All modification specifications will require review and approval by “#companyname#” Project Manager prior to proceeding with coding.\n•	This deliverable is provided by Quisitive with key input/assistance and sign-off from “#companyname#”"],
      ["Ready Production Environment","•	Provides Dynamics 365 in Production ready for data conversion.\n•	Provides initial security role configuration as defined and configured by “#companyname#”.\n•	This deliverable is provided by “#companyname#” with some Quisitive assistance."],
      ["Super-User & End User Training","•	Quisitive to provide Super-Users with training in respective job functions on D365."],
      ["Final Data Conversion",	"•	Converted, reviewed and approved legacy data posted and available in production of D365."],
      ["Project Close Out Report","•	Quisitive & “#companyname#” work together to create Project close out report with complete Project toll gate review document, Knowledge transfer document and project acceptance report."]

    ]
  },
  
]

project_governace=[
  {
    "type": "heading",
    "level": 1,
    "text": "Project Governance Approach"
  },
  {
    "type": "paragraph",
    "title": "",
    "text": "The governance structure and processes the team will adhere to for the project are described in the following sections:"
  },
  {
    "type": "heading",
    "level": 2,
    "text": "Project Management"
  },
  {
    "type": "paragraph",
    "title": "",
    "text": "The project will be managed by a full-time Quisitive Project Manager to work with the Customer Project Manager. The Quisitive Project Manager will be responsible for the overall delivery of Quisitive services as specified in the table below. The Project Manager will report to the Project Management Office or the Steering Committee, which will consist of the Customer Sponsors, Customer Program Manager, and the Quisitive Delivery Leader."
  },
   {
    "type": "heading",
    "level": 3,
    "text": "Communication Plan"
  },
  {
    "type": "paragraph",
    "title": "",
    "text": "The following will be used to communicate during the project:"
  },
  {
    "type": "list",
    "title": "",
    "data": [
    "Communication plan: This document will describe the frequency, audience, and content of communication with the team and stakeholders. It will be developed by Quisitive and the customer as part of project planning.",
    "Status reports: The Quisitive team will prepare and issue regular status reports to project stakeholders per the frequency defined in the communication plan.",
    "Status meetings: The Quisitive team will schedule weekly status meetings to review the overall project status, the acceptance of deliverables, and review open problems and risks."]
  },
   {
    "type": "heading",
    "level": 3,
    "text": "Issue/Risk Management Procedure"
  },
  {
    "type": "paragraph",
    "title": "",
    "text": "The following general procedure will be used to manage active project issues and risks during the project:" 
  },
  {
    "type": "list",
    "title": "",
    "data": ['Identify : Identify and document project issues (current problems) and risks (potential problems that could affect the project).', 'Analyze and prioritize: Assess the potential impact and determine the highest priority risks and problems that will be actively managed.', 'Plan and schedule: Determine the strategy for managing priority risks and issues and identify a resource who can take responsibility for mitigation and remediation.', 'Track and report: Monitor and report the status of risks and problems.', 'Escalate: Escalate to project sponsors the high-impact problems and risks that the team is unable to resolve.', 'Control: Review the effectiveness of risk and issue management actions.']

  },
  {
    "type":"paragraph",
    "title":"",
    "text":"Active issues and risks will be regularly monitored throughout the project."
  },
  {
    "type": "heading",
    "level": 3,
    "text": "Change Management Process"
  },
  {
    "type": "paragraph",
    "title": "",
    "text":"During the project, either party can request modifications to the services described in this SOW. These changes only take effect when the proposed change is agreed upon by both parties in writing. The change management process steps are:"
  },
  {
    "type": "list",
    "title": "",
    "data": [
    "The change is documented: All change requests will be documented by Quisitive in a Quisitive change request form and submitted to the Customer. The change request form includes: \n\t 1. A description of the change. \n\t 2. The estimated effect of implementing the change." ,
    "The change is submitted: The change request form will be provided to the Customer.",
    "The change is accepted or rejected: The Customer has three business days to confirm the following to Quisitive: \n\t 1. Acceptance: The Customer accepts the change request and agrees to the terms and conditions of the change request form. \n\t 2. Rejection: The Customer does not want to proceed with the change or does not provide an approval within three business days, no changes will be performed."
] 
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"At Quisitive's discretion, time required to research and document customer-originated change requests will be billed at the standard rates specified in Section 7 'Fees.'"    
  },
  {
    "type": "heading",
    "level": 3,
    "text": "Critical Path Decisions"
  },
  {
    "type": "paragraph",
    "title": "",
    "text":"Throughout the project, Quisitive will submit requests for decisions for the Customer to act on. Decisions are assigned due dates, and it is assumed that the Customer will provide the required feedback or make decisions on the due date agreed upon by the parties."
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"Some decisions requested will be determined by Quisitive as Critical Path Decisions (CPD) with key dependencies. These will be submitted with a written Notice of Decision (NOD) request to the Customer's Project Sponsor. If the NOD request is not acted on within 5 business days of receipt by the Customer's Project Sponsor, it may impact the project critical path and may be addressed as a Project Change Request and submitted through the Change Management process."
  },
  {
    "type":"heading",
    "level":3,
    "text":"Project Steering Committee"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"The CRM Steering Committee provides overall senior management oversight and strategic direction for the project. The PROJECT Steering Committee for the project will meet per the frequency defined in the communication plan and will include the roles listed in the following table. The responsibilities for the committee include:"
  },
  {
    "type":"list",
    "title":"",
    "data":[
    "Making decisions about the project's strategic direction.",
    'Serving as a final arbiter of project issues.',
    'Approving significant change requests.'
]  
  },
  {
    "type":"table",
    "title":"Table: Project Steering Committee",
    "data":[
      ["Role","Representing"],
      ["Project Executive Sponsor","Customer"],
      ["Customer Project Sponsor","Customer"],
      ["Executive Technical Lead","Customer"],
      ["Project Manager","Customer"],
      ["Architect","Customer"],
      ["Executive Sponsor","Quisitive"],
      ["Delivery Architect / Solution Delivery Lead","Quisitive"],
      ["Project Manager","Quisitive"]
    ]
  },
  {
    "type":"heading",
    "level":3,
    "text":"Architecture Board"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"Technical Governance and solution oversight for the project will be provided by an Architecture Board, which will consist of the following key technical leadership and management representatives:"
  },
  {
    "type":"table",
    "title":"Table: Architecture Board Composition",
    "data":[
      ["Name","Representing","Title","Project Role"],
      ["TBD","Customer","Architect",""],
      ["TBD","Quisitive","Delivery Architect",""],
      ["TBD","Quisitive","Solution Architect",""]
    ]
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"The Architecture Board will hold weekly meetings and document key outcomes. Customer and Quisitive Managers will share joint responsibility for management and tracking of architectural changes, issues, risks, and decisions."
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"The Architecture board is responsible for the following:" 
  },
  {
    "type":"list",
    "title":"",
    "data":[
      "Conducting meetings to review and address architectural decisions including implications and impacts of decisions and resulting changes to the solution and project plan.",
      "Providing solution and direction oversight including managing issues and risks escalated by the solution work streams including:\n\t",
      "1. Infrastructure decisions (design, sizing, availability, configuration)\n\t"
      "Data migration, interfaces, reporting and security design\n\t"
      "Technical solution architecture (components, mapping, integration)\n\t"
      "Functional solution design (fits, customizations, ISVs)\n\t"
      "Design and technical documentation standards and programming standards\n\t"
      "Facilitate cross work stream collaboration among technical stakeholders and serve as a single platform through which key technical discussions are addressed."
    ]
  },
  {
    "type":"heading",
    "level":3,
    "text":"Escalation Process"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"The Quisitive project manager will work closely with the customer project manager, sponsor and other designees to manage project issues, risks, and change requests as described previously. The Customer will provide reasonable access to the sponsor or sponsors to expedite resolution. The standard escalation path for review, approval, or dispute resolution is as follows:"
  },
  {
    "type":"list",
    "title":"",
    "data":[
      "Project team member (Quisitive or the Customer)",
      "Project manager (Quisitive and the Customer)",
      "Quisitive delivery manager",
      "Quisitive and the Customer project sponsor",
      "Project Steering Committee" ]
  },
  {
    "type":"heading",
    "level":3,
    "text":"Service Deliverable Acceptance Process"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"During the project, Quisitive will submit certain deliverables (listed in Section 3.3 as deliverables) for the customer's review and approval. Within three business days of the date of receipt, the Customer is required to:"
  },
  {
    "type":"list",
    "title":"",
    "data":[
      "Accept the deliverable by signing, dating, and returning a service deliverable acceptance form, which can be sent by email, or by using (or partially using) the deliverable \n\t Or",
      "Reject the deliverable by notifying Quisitive in writing; the Customer must include the reason(s) for rejection."
    ]
  },
  {
    "type":"heading",
    "level":3,
    "text":"Document Deliverables"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"The review and approval of documents will be governed by the following:  The Quisitive Project Manager, or his/her designee, will prepare a Deliverable Acceptance Form (DAF) and forward it with the respective document deliverable to the Customer Project Manager, or Customer designee, for review and acceptance."
  },
  {
    "type":"list",
    "title":"",
    "data":[
      "Customer project manager will be responsible for the distribution of documents, organizing internal reviews, and collating feedback into a single document. It is required that the Customer complete all reviews and comments with tracking activated in the original document.",
      "Customer and Quisitive will review the comments promptly and agree on those which require the document to be changed.",
      "Quisitive will change the document in line with the agreement and resubmit the updated documents within 2 business days for final sign-off.",
      "Customer will review the revised DAF and either indicate its agreement by signing and returning the DAF or communicate any further revisions required to the Consultant."
    ]
  },
  {
    "type":"heading",
    "level":3,
    "text":"Document Acceptance Classification and Criteria"
  },
  {
   "type":"list",
   "title":"",
   "data":["Major error in understanding or design which will prevent the solution from working.",
          "Minor an item or error which will not have a major impact and can be corrected in the following design documents.",
          "Cosmetic incorrect spelling, grammar, or formatting."
          ] 
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"Any document with zero Major, 2 or fewer Minor, and/or 10 or fewer Cosmetic items will be accepted."
  },
  {
    "type":"heading",
    "level":2,
    "text":"Project Completion"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"Quisitive will provide Services defined in this SOW within a period of 18 weeks as mentioned in Section 3.2 Timeline. If additional services are required that are out of scope as defined herein, the Change Management process as defined in Section 4.1.3 may be followed and the contract modified."
  },
  {
    "type":"heading",
    "level":2,
    "text":"General Customer Responsibilities"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"In addition to any Customer activities identified elsewhere in this SOW, Customer will perform or provide the following:"
  },
  {
    "type":"list",
    "title":"",
    "data":[
      "Provide suitable workspaces, including meeting rooms and web/tele-conferencing equipment.",
      "Provide resources and connectivity giving the Quisitive onsite team access to appropriate facilities, internet, and e-mail.",
      "Provide access to all necessary Customer work sites, systems logon and passwords as well as material and resources as needed and as reasonably requested by us in advance.",
      "Assume responsibility for management of all non-Quisitive managed vendors.",
      "Provide access with proper licenses to all necessary tools and third-party products required for Quisitive to complete its assigned tasks.",
      "Acquire Azure subscription required to support the environments as defined in the scope section of this SOW.",
      "Provide personnel with specific business expertise and detailed knowledge of the current systems and business processes (SME, Business Analysts, Data analysts, etc.)."  
    ]
  },
  {
    "type":"heading",
    "level":2,
    "text":"Project Assumptions"
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"All estimates regarding fees, timelines, and our detailed solution are based on information provided by the Customer to date, and all the listed assumptions within this document being validated as true during this project. They are also based on the Customer and Quisitive working in partnership, as described within the approach and governance sections of the document. Anything that differs materially regarding the information provided, the approach and governance documented, or the assumptions, can result in Quisitive raising a change request to cover additional work or extended durations as a direct result. Notwithstanding anything to the contrary herein, Customer shall not be responsible for additional fees required as a result of any acts, omissions, or occurrences outside of its reasonable control including, without limitation, Quisitive's failure to timely comply with its obligations and responsibilities as set forth herein or Quisitive's failure to perform as stated herein."
  },
  {
    "type":"paragraph",
    "title":"",
    "text":"The Services and delivery schedule for this project are based on the following assumptions:"
  },
  {
    "type":"list",
    "title":"",
    "data":[   
      "Staffing the project with the right resources requires 4 to 6 weeks. For this reason, the project initiation phase will not start until at least 4 weeks after the SOW is signed by Quisitive and Customer.",
      "The standard workday for the project is between 8:00 AM and 5:00 PM local time where the team is working, Monday through Friday, except for scheduled holidays. The project schedule will be constructed using a standard 8-hour day for both Customer and Quisitive resources.",
      "In performing services under this SOW, Quisitive will rely upon any instructions, authorizations, approvals, or other information provided by Customer's Project Manager or personnel duly designated by Customer's Project Manager. All estimates regarding fees, timelines, and our detailed solution are based on information provided by Customer to date.",
      "Quisitive's resources may perform services remotely or on-site from Quisitive facilities, Customer facilities, or Quisitive partner's facilities, as mutually agreed by the parties.",
      "Quisitive may make changes in staffing including but not limited to number of resources, individuals, project roles etc. with a 2-week notice to Customer; provided that no such staffing changes shall extend the duration of the Project or result in additional fees.",
      "If the project schedule requires Quisitive's resources to perform dedicated services at Customer's site on a weekly basis, Quisitive resources will typically be on-site for 3 nights/4 days; arriving on Monday morning and leaving on Thursday afternoons. The consultants will work off-site on Fridays.",
      "Quisitive consultants will work on-site as described in Assumption #6 above, for three weeks, then one-week off-site each month.",
      "Process engineering, or re-engineering activities are assumed to be completed by Customer, and/or Customer's representative.",
      "Customer is responsible for all organization change management.",
      "Failure to complete any required site readiness activities described herein for Quisitive to deliver its services according to the agreed upon Project schedule may result in Project delays requiring a Change Request to this SOW as well as additional Project costs.",
      "Project work will be performed in the relevant “Trident Maritime Systems” Locations, including headquarters. Individuals or teams may be required to make specific site visits during the project. They will be handled on an as-needed basis and requested or approved via project governance process.",
      "All Project communications will be in English, local language support and translations will be provided by Customer.",
      "Customer resources will be available to perform their roles on the project team at appropriate times and for the required duration.",
      "Key Customer technical resources will be available throughout the engagement to assist the Quisitive project team.",
      "Project management from Customer is assumed to be a key responsibility for the assigned Trident Maritime Systems resource.",
      "All work is to be contiguously scheduled; any breaks in the engagement calendar will be mutually agreed.",
      "If the scope listed in this SOW changes significantly due solely to Customer's fault there will need to be effort expended on re-estimation and on adjusting the scope and/or project timelines; this will lead to additional costs and possible Change Requests.",
      "Quisitive will provide all document deliverables based on Quisitive documentation standards.",
      "All project document deliverables will be in English only.",
      "The application will be accessible over the corporate intranet of Customer. It is Customer's responsibility to configure secure extranet infrastructure if required.",
      "Customer will provide 24x7 access to its development and testing environments to onsite and offsite consultants to carry out work on the project.",
      "Any bugs arising in any third-party tools are the responsibility of each vendor and will not be fixed by Quisitive."
    ]
  },
  {
    "type":"heading",
    "level":5,
    "text":"Infrastructure Assumptions"
  },
  {
    "type":"list",
    "title":"",
    "data":[
      "Existing systems or programs upon which the project deliverables depend are stable and will not change during the term of this project.",
      "Where applicable, Customer will provide servers with the base Windows operating system, latest patches installed, and other required software, such as Antivirus protection, installed.",
      "Timely availability of subscription(s), hardware, software, and physical space for the solution environments is essential. Failure to complete all site readiness activities that are required for Quisitive to deliver its services according to the agreed-upon project schedule may result in project delays requiring Change Orders to this SOW as well as additional project costs.",
      "The proposed solution assumes Customer already has Microsoft Active Directory set up and active with access to the required infrastructure, including Azure Active Directory that will be used by this solution through correct security zones and firewall control.",
      "Customer is responsible for or the activation of the Dynamics 365 for Finance & Operations Enterprise Edition Online subscription.",
      "A Microsoft Azure data center based in the United States or Canada will be utilized for D365 instances deployed in this project. The cloud deployment approach for production deployment of the D365 solution will not be determined in this project."
    ]
  },
  {
    "type":"heading",
    "level":5,
    "text":"Other Assumptions"
  },
  {
    "type":"list",
    "title":"",
    "data":[
      "The fit/gap process will likely reveal some functional gaps. Handling of additional gaps will be addressed via the Change Review Board process.",
      "Workflow requirements presented by Customer shall be documented in Azure DevOps and included in fit/gap disposition. However, gap requirements will not be analyzed by Quisitive. It is assumed that Customer will address workflow gaps.",
      "Customer will provide Work stream Leads and SMEs needed to support each work stream and maintain Weekly Solution Modelling Schedule and Deliverables, described in section 3.1.",
      "Customer work stream resource plan will support the proposed work stream workshops. These resources will be empowered to lead and manage process alignment, change management, and decision-making.",
      "Azure DevOps will be utilized throughout the project to document, organize, and maintain many activities and artifacts, including requirements, action items, issues log, and risk log. To support this, Quisitive provides a standard DevOps template. DevOps administration and operation will be managed jointly by Customer and Quisitive Project Managers/Architects. If extensive, development of DevOps forms, queries/reports, and dashboards requested by Customer will be subject to the Change Management process.",
      "Upon completion of the initial installation and setup of D365 instances by Quisitive Technical Infrastructure Consultant, it is assumed Customer will maintain the D365 technical environment.",
      "Customer's core project team members will be trained on D365 prior to the start of implementation. This includes functional training relevant to their project work stream."
    ]
  }
]

fees_signing=[
  {
    "type":"paragraph",
    "title":"",
    "text":"By signing below the parties acknowledge and agree to be bound to the terms of the agreement and this Statement of Work."
  },
  {
    "type":"table",
    "title":"",
    "data":[
      ["#companyname#","Quisitive Inc."],
      ["Signature","Signature"],
      ["Name of person signing (Please Print)","Name of person signing (Please Print)"],
      ["Title of person signing (Please Print)","Title of person signing (Please Print)"],
      ["Signature date","Signature date"]
    ]
  }
  # },
  # {
  #   "type":"heading",
  #   "level":5,
  #   "text":"END OF SOW"
  # }
]