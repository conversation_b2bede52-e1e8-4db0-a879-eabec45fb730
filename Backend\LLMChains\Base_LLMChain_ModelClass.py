from langchain_core.prompts import ChatPromptTemplate
from Config.azureAI import llm

class BaseLLMChainClass:

    def __init__(self, system_prompt: str, schema_model=False):
        self.system_prompt = system_prompt
        self.schema_model = schema_model
        self.prompt_template = self.create_prompt_template()

    def create_prompt_template(self):

        if self.schema_model == False:
            return ChatPromptTemplate.from_messages(
                [
                    ("system", self.system_prompt),
                    ("human", "Context: '''{context}'''")
                ]
            )
        
        else:
            return ChatPromptTemplate.from_messages(
                [
                    ("system", self.system_prompt),
                    ("human", "{input}")
                ]
            )

    def create_chain(self):

        if self.schema_model == False:
            return self.prompt_template | llm
        else:
            return self.prompt_template | llm.with_structured_output(self.schema_model)
    
    def structure(self, input_text):

        chain = self.create_chain()
        response = chain.invoke(input_text)

        if self.schema_model == False:
            return response
        
        else:
            # chain = self.create_chain()
            # response = chain.invoke({"input": input_text})
            return dict(response)
        
    def invoke(self, input_text):

        chain = self.create_chain()
        response = chain.invoke(input_text)

        if self.schema_model == False:
            return response
        
        else:
            # chain = self.create_chain()
            # response = chain.invoke({"input": input_text})
            return dict(response)
