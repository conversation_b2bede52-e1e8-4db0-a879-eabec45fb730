import pandas as pd
df = pd.read_csv(r"./Data/Integrations.csv", encoding='utf-8')

def get_integrations(isv_list: list):
    
    # Initialize the result list
    result = []
    
    # Iterate over each row in the dataframe
    for _, row in df.iterrows():
        # Check if the current ID is in the isv list
        is_checked = 'true' if row['IsvID'] in isv_list else 'false'
        
        print("Here is row for testing purpose",row)

        # Create the object with the required structure
        isv_object = {
            "type": "checkboxWithTextGrouping",
            "label": row['SubGroup'],
            "inputPlaceHolders": row['ISVName'],
            "id": row['IsvID'],
            "group": row['Group'],
            "isChecked":is_checked
        }
        
        # Append to the result list
        result.append(isv_object)
    
    return result

