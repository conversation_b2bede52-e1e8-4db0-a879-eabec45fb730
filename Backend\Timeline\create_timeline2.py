import pandas as pd
import math
import numpy as np
import io

def create_resource_timeline(build_hours, resources):
    # Initialize DataFrame with resource roles
    df = pd.DataFrame(index=[r['role'] for r in resources], columns=['Role'])
    
    # Fill Role column
    for resource in resources:
        df.at[resource['role'], 'Role'] = resource['role']
    
    # Separate resources into build allocation true and false
    build_allocation_resources = []
    non_build_allocation_resources = []
    
    for resource in resources:
        # Find build phase hours for each resource
        build_phase = next(phase for phase in resource['phases'] if phase['name'] == 'Build')
        resource['weekly_hours'] = build_phase['hours_allocation'] * 40
        
        # Separate resources based on build_allocation
        if resource['build_allocation'].lower() == 'true':
            build_allocation_resources.append(resource)
        else:
            non_build_allocation_resources.append(resource)
    
    # Initialize remaining hours and week counter
    remaining_hours = build_hours
    current_week = 0
    week_columns = []
    
    # First, handle resources with build_allocation = true
    while remaining_hours > 0:
        week_col = f'W_{current_week + 1}'
        week_columns.append(week_col)
        
        # Add new week column
        df[week_col] = 0
        
        # Calculate total available weekly hours for build allocation resources
        total_weekly_hours = sum(resource['weekly_hours'] for resource in build_allocation_resources)
        
        # If remaining hours are less than total weekly capacity
        if remaining_hours < total_weekly_hours:
            for resource in build_allocation_resources:
                df.at[resource['role'], week_col] = resource['weekly_hours']
            remaining_hours = 0
        else:
            # Allocate full weekly hours to each resource
            for resource in build_allocation_resources:
                df.at[resource['role'], week_col] = resource['weekly_hours']
                remaining_hours -= resource['weekly_hours']
        
        current_week += 1
    
    # Now handle resources with build_allocation = false
    # They get their hours filled across all weeks without affecting build_hours
    for resource in non_build_allocation_resources:
        for week_col in week_columns:
            df.at[resource['role'], week_col] = resource['weekly_hours']
    
    # Add 'Build' as the first row
    build_row = pd.Series(['Build'] + ['Build'] * len(week_columns), index=['Role'] + week_columns)
    df = pd.concat([pd.DataFrame([build_row]), df])
    
    return df, current_week


def create_df_from_phase(phase_name, weeks, resources):
    # Create DataFrame
    df = pd.DataFrame(index=[r['role'] for r in resources], columns=['Role'])
    
    # Fill Role column
    for resource in resources:
        # Find the specific phase in the resource's phases
        phase = next((p for p in resource['phases'] if p['name'] == phase_name), None)
        df.at[resource['role'], 'Role'] = resource['role']
        # Calculate weekly hours for the phase
        if phase:
            resource['weekly_hours'] = phase['hours_allocation'] * 40
        else:
            resource['weekly_hours'] = 0

    week_columns = []

    # Allocate hours week by week
    for week in range(weeks):
        week_col = f'Week {week + 1}'
        week_columns.append(week_col)
        
        # Add new week column
        df[week_col] = 0
        
        for resource in resources:
            # Calculate hours for this week
            hours_to_allocate = max(resource['weekly_hours'], 0)
            df.at[resource['role'], week_col] = hours_to_allocate

    # Add phase name as the first row
    phase_row = pd.Series([''] + [phase_name] * len(week_columns), index=['Role'] + week_columns)
    df = pd.concat([pd.DataFrame([phase_row]), df])
    
    df = df.drop(columns=['Role'])
        
    return df

def create_df(build_hours , resources):
    df, build_weeks = create_resource_timeline(build_hours, resources)
    timeline_df = df

    #calulate weeks for each phase
    initialization_week = 1
    solution_modeling_week = math.ceil(0.2*build_weeks)
    solution_testing_week = math.ceil(0.3*build_weeks)
    deployment_week = math.ceil(0.1*build_weeks)
    support_week = 2 

    # create object for each phase
    phases = [
        {
            "name": "Initialization",
            "weeks": initialization_week,
        },
        {
            "name": "Solution Modeling",
            "weeks": solution_modeling_week,
        },
        {
            "name": "Solution Testing",
            "weeks": solution_testing_week,
        },
        {
            "name": "Deployment",
            "weeks": deployment_week,
        },
        {
            "name": "Support",
            "weeks": support_week,
        },
    ]


    # Create DF for each phase
    dfs = []
    for phase in phases:
        df = create_df_from_phase(phase['name'], phase['weeks'], resources)
        dfs.append({
            "phase": phase['name'],
            "df": df,
        })

    timeline_df.insert(0, 'Phase', 'Build')
    role_column = timeline_df.pop('Role')
    timeline_df = timeline_df.drop(columns=['Phase'])
    dfs.append({
        "phase": "Build",
        "df": timeline_df,
    })

    # Merge all according to the flow
    merged_flow = ["Initialization", "Solution Modeling", "Build", "Solution Testing", "Deployment", "Support"]
    merged_df = pd.DataFrame()
    for phase in merged_flow:
        for df in dfs:
            if df['phase'] == phase:
                merged_df = pd.concat([merged_df, df['df']], axis=1)

    # Add role column
    roles = [r['role'] for r in resources]
    merged_df.insert(0, 'Role', [''] + roles)  # Empty string for the phase row

    # Rename columns to W1, W2, etc.
    headers = ["Role"] + [f'W{i}' for i in range(1, len(merged_df.columns))]
    merged_df.columns = headers

        # Convert all columns except 'Role' to numeric, coerce errors to NaN
    merged_df.iloc[1:, 1:] = merged_df.iloc[1:, 1:].apply(pd.to_numeric, errors='coerce')

        # add a column named 'Rate' and fill it with the rate of each role from resources object having key 'rate'
    merged_df['Rate'] = 0
    for resource in resources:
        merged_df.loc[merged_df['Role'] == resource['role'], 'Rate'] = resource['rate']

    merged_df.loc[0, 'Rate'] = pd.NA
    # add a column named 'Total Cost' and fill it with the product of 'Rate' and 'Total Hours'
    merged_df['Total Cost'] = merged_df['Rate'] * merged_df.filter(like='W').sum(axis=1)

    # Convert 'Total Cost' column to numeric
    merged_df['Total Cost'] = pd.to_numeric(merged_df['Total Cost'], errors='coerce')


    week_columns = [col for col in merged_df.columns if col.startswith('W')]

    merged_df['Total Hours'] = merged_df[week_columns].iloc[1:].sum(axis=1)

    # Add empty value for the first row to match the phase row
    merged_df.loc[0, 'Total Hours'] = ''

    # Move 'Total Hours' column to the 2nd position
    cols = merged_df.columns.tolist()
    cols.insert(1, cols.pop(cols.index('Total Hours')))
    merged_df = merged_df[cols]

    # move 'Rate' column to the 3rd position
    cols = merged_df.columns.tolist()
    cols.insert(2, cols.pop(cols.index('Rate')))
    merged_df = merged_df[cols]

    # move 'Total Cost' column to the 4th position
    cols = merged_df.columns.tolist()
    cols.insert(3, cols.pop(cols.index('Total Cost')))
    merged_df = merged_df[cols]

    return merged_df