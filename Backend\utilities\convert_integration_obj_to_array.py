def convert_integration_obj_to_array(integration_data):
    """
    Convert the integration object to an array
    """
    reformed_table = []
            
    # Create header row with desired column names and order
    reformed_table.append(["Integration Point", "Integration Solution", "Integration Description"])
    
    # Create data rows in desired order using the correct field mappings
    for item in integration_data:
        row = [
            item.get("Integration Point", ""),           # "Integration Point"
            item.get("Value", ""),                       # "Integration Solution"
            item.get("Integration  Description", "")     # Note the two spaces in "Integration  Description"
        ]
    
        reformed_table.append(row)
    
    return reformed_table