from pydantic import BaseModel, Field
from typing import List, Optional

class BPStructureModel(BaseModel):
    """It will use to extract Business IDs"""

    bp_id: List[str] = Field(description="List of Business Process IDs e.g. ['BP001', 'BP002',..]")
    reasoning: str = Field(description="Short 1 line description for Reasoning behind the suggested business processes. Just for the sake of understanding. Do not mention business process IDs in this field.")
    