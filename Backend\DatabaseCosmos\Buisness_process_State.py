import logging
import random
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from DatabaseCosmos.UpdatingCheckboxAndCheckboxWithGrouping import upsertUserSelectedData
from Config.cosmosdb import database
from utilities.CreateGuid import generate_guide
# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="BP_State",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)

""" - Parameters
chat_id = 123(guid),
bp_list = []
user_id:123(guid)
"""
# def insert_BP_State(chat_id:str, bp_list:list, bp_object:dict, user_id:str):
def insert_BP_State(chat_id:str, bp_list:list, description:str, user_id:str):
    logging.info("Inserting row in BP_State Container")
    print("Inserting row in BP_State Container")
    try:
        
        guid=generate_guide()
        item = {
                'id': guid,
                'chat_id':chat_id,
                'user_id': user_id,
                'createdAt': datetime.utcnow().isoformat(),
                'items':bp_list,
                # "object":bp_object,
                'description':description,
            }
        container.create_item(body=item)
        print(f'Created new item with Id {guid}')
        
        return item
        
    except Exception as e:
        print("Error" ,e)
        
        print("Read Failed!")
        return None       



def read_BPState(chat_id):
    print("reading Buisness Process")
    try:
        logging.info('Applying query.')
        query = f"SELECT * FROM c WHERE c.chat_id = '{chat_id}'"
        results = []
        for item in container.query_items(query = query, enable_cross_partition_query = True):
                    results.append({
                        'id': item['id'],
                        'user_id': item['user_id'],
                        'createdAt':item.get('createdAt'),
                        'items':item.get('items'),
                        # 'object':item.get('object')
                        'description':item.get('description')
                    })
        logging.info(f"Retrieved (query) .")
        print(f"Retrieved (query) .",results)

        return results
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
    
        return None
    

def callUpdateBp(chat_id,selected_list):
    response=upsertUserSelectedData(chat_id,selected_list,container) 

    if response:
         print(response)
         return True
    else:
         return False
