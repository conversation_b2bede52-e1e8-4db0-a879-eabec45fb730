from pydantic import BaseModel, Field
from typing import List

class DmModel(BaseModel):
    type: str = Field(default="checkboxWithTextGrouping")
    label: str = Field(description="Suggested entities")
    inputPlaceHolders: str = Field(description="Placeholders")
    isChecked: bool= Field(default=False)
    description: str = Field(description="Description of the Data migration")
    id: str = Field(description="ID for the Data migration. eg:1")
    group: str =Field(description = "Data Migration Categories")
    

class DmStructureModel(BaseModel):
    """It will use to extract Data migration"""

    dm_list: List[DmModel] = Field(description="List of Data migration")
    reasoning: str = Field(description="Short 1 or 2 line description for Reasoning behind the suggested Data Migration. Just for the sake of understanding. Dont mention any Data Migration in this field.")


    