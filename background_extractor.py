# from pptx import Presentation
# import os

# def extract_background_images(ppt_file, output_dir):
#     # Load the PowerPoint file
#     presentation = Presentation(ppt_file)

#     # Create the output directory if it doesn't exist
#     if not os.path.exists(output_dir):
#         os.makedirs(output_dir)

#     image_count = 0

#     # Iterate through slides in the presentation
#     for idx, slide in enumerate(presentation.slides, start=1):
#         background = slide.background
#         if background and background.fill:
#             fill = background.fill
#             # Check if the background is a picture fill
#             if fill.type == 'picture':
#                 image = fill.picture
#                 if image and hasattr(image, 'blob'):
#                     image_count += 1
#                     image_data = image.blob
#                     image_ext = image.content_type.split('/')[-1]
#                     image_path = os.path.join(output_dir, f"background_slide_{idx}.{image_ext}")
#                     with open(image_path, "wb") as img_file:
#                         img_file.write(image_data)
#                     print(f"Extracted background image from slide {idx}: {image_path}")

#     if image_count == 0:
#         print("No background images found.")
#     else:
#         print(f"Extracted {image_count} background images to {output_dir}")

"""
# from pptx import Presentation
# import os
# from PIL import Image
# from io import BytesIO

# def extract_backgrounds(ppt_file, output_dir):
#     # Load the PowerPoint file
#     presentation = Presentation(ppt_file)

#     # Create the output directory if it doesn't exist
#     if not os.path.exists(output_dir):
#         os.makedirs(output_dir)

#     background_count = 0

#     # Iterate through slides
#     for idx, slide in enumerate(presentation.slides, start=1):
#         slide_id = f"slide_{idx}"

#         # Check for images in slide background
#         background = slide.background
#         if background and background.fill and background.fill.type == 'picture':
#             print("\n\n in background")
#             image = background.fill.picture
#             if image and hasattr(image, 'blob'):
#                 print("\n\n in if")
#                 image_data = image.blob
#                 image_ext = image.content_type.split('/')[-1]
#                 output_path = os.path.join(output_dir, f"{slide_id}_background.{image_ext}")
#                 with open(output_path, "wb") as img_file:
#                     img_file.write(image_data)
#                 print(f"Extracted image background from slide {idx}: {output_path}")
#                 background_count += 1

#         # Check for solid color background
#         elif background and background.fill.type == 'solid':
#             color = background.fill.fore_color.rgb
#             color_image = Image.new("RGB", (1920, 1080), color)
#             output_path = os.path.join(output_dir, f"{slide_id}_solid.png")
#             color_image.save(output_path)
#             print(f"Extracted solid color background from slide {idx}: {output_path}")
#             background_count += 1

#     # Check for master slide themes or images
#     for master_idx, master in enumerate(presentation.slide_masters, start=1):
#         for idx, slide_layout in enumerate(master.slide_layouts, start=1):
#             if slide_layout.background and slide_layout.background.fill.type == 'picture':
#                 image = slide_layout.background.fill.picture
#                 if image and hasattr(image, 'blob'):
#                     image_data = image.blob
#                     image_ext = image.content_type.split('/')[-1]
#                     output_path = os.path.join(output_dir, f"master_{master_idx}_layout_{idx}_background.{image_ext}")
#                     with open(output_path, "wb") as img_file:
#                         img_file.write(image_data)
#                     print(f"Extracted master slide image: {output_path}")
#                     background_count += 1

#     if background_count == 0:
#         print("No backgrounds extracted from the presentation.")
#     else:
#         print(f"Extracted {background_count} backgrounds to {output_dir}")

    """
    
    
""" working code for extracting images from pptx file

import os
from pptx import Presentation
from PIL import Image, ImageDraw
from io import BytesIO

def render_slides_as_images(ppt_file, output_dir, image_size=(1920, 1080)):
    # Load the PowerPoint presentation
    presentation = Presentation(ppt_file)
    
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Slide dimensions in pixels
    width_px, height_px = image_size

    for idx, slide in enumerate(presentation.slides, start=1):
        # Create a blank white image to represent the slide
        slide_image = Image.new("RGB", (width_px, height_px), "white")
        draw = ImageDraw.Draw(slide_image)
        
        # Extract all shapes and draw placeholders (optional visualization)
        for shape in slide.shapes:
            if shape.shape_type == 13:  # MSO_SHAPE_TYPE.PICTURE
                image_data = shape.image.blob
                image = Image.open(BytesIO(image_data))
                left = int(shape.left * width_px / presentation.slide_width)
                top = int(shape.top * height_px / presentation.slide_height)
                img_width = int(shape.width * width_px / presentation.slide_width)
                img_height = int(shape.height * height_px / presentation.slide_height)
                
                # Paste the image onto the slide
                image_resized = image.resize((img_width, img_height), Image.Resampling.LANCZOS)
                slide_image.paste(image_resized, (left, top))

        # Save the rendered slide
        slide_output_path = os.path.join(output_dir, f"slide_{idx}.png")
        slide_image.save(slide_output_path)
        print(f"Rendered slide {idx} to {slide_output_path}")
    
    print(f"Rendered all slides to images in {output_dir}.")


ppt_file_path = "./Data/mazik_ppt.pptx"  # Replace with your uploaded PPT path
# ppt_file_path="./Data/SampledesignforthemePPT.pptx"
output_directory = "./Data/ppt_background_images"
render_slides_as_images(ppt_file_path, output_directory)

"""



from spire.presentation.common import *
from spire.presentation import *
# Create a Presentation instance
def extract_backgrounds():
    ppt = Presentation()

    try:
        # Load a PowerPoint document
        ppt.LoadFromFile("./Data/SampledesignforthemePPT.pptx")
        # Iterate through all images in the document

        for i, image in enumerate(ppt.Images):
            # Extract the images
            ImageName = "./Data/ppt_background_images/Images_"+str(i)+".png"
            image.Image.Save(ImageName)

        ppt.Dispose()

    except Exception as e:
        print("\n\n\n error:  ",e)
        
from pptx import Presentation
def ppt_gen():
    # Load the existing PowerPoint presentation
    ppt_path = "./Data/SampledesignforthemePPT.pptx"
    presentation = Presentation(ppt_path)

    # Select a slide layout (0: Title Slide, 1: Title and Content, etc.)
    slide_layout = presentation.slide_layouts[1]

    # Add a new slide using the selected layout
    slide = presentation.slides.add_slide(slide_layout)

    # Add content to the slide
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "New Slide Title"
    content.text = "This is the appended content following the presentation theme."

    # Save the modified presentation
    presentation.save("./Data/updated_presentation.pptx")



ppt_gen()
