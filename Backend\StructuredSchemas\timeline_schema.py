from pydantic import BaseModel, Field
from typing import List

class TimelineModel(BaseModel):
    """Represents a single task in a project timeline."""
    week: str = Field(description="Name of week in the project timeline for example: 1 or 1-2")
    phase: str = Field(description="Name of the phase in the project timeline")
    task: str = Field(description="Task mentioned in the project timeline")
    resources: str = Field(description="Name of resources mentioned in the project timeline")
    allocatedhours: int = Field(description="Number of hours a resource will be allocated to the task.")

class TimelineStructureModel(BaseModel):
    """Represents the entire project timeline."""
    tasks: List[TimelineModel] = Field(description="List of tasks in the project timeline")


