from pydantic import BaseModel, Field
from typing import List, Optional
from typing import Union

class ParagraphModel(BaseModel):
    """It will use to generate title and paragraph for each presentation slide"""

    title: str = Field(description="Title for the PPT Slide, keep it to the point") 
    type: str = Field(default="paragraph")
    paragraph: str = Field(description="Paragraph of the slide, keep it precise, presentable and professional")

class TableModel(BaseModel):
    """It will use to generate title and Table for each presentation slide"""

    title: str = Field(description="Title for the PPT Slide with respect to table, keep it to the point") 
    type: str = Field(default="table")
    table: List[List[str]] = Field(description="2d array of list for the table, first row would be the headings")

class SlideContentTableModel(BaseModel):
    """It will use to generate a list of Only Table content objects"""
    slideObjects: List[TableModel] = Field(description="list of objects")

class SlideContentParagraphModel(BaseModel):
    """It will use to generate a list of Paragraph content objects"""
    slideObjects: List[ParagraphModel] = Field(description="list of objects")