from json import tool
from Agents.proposal_Agent import proposal_agent_llm_with_tools, proposal_agent_tools  # use your refactored proposal agent here
from Config.azureAI import llm
from copy import deepcopy
from langchain_core.runnables import chain
import json
from DatabaseCosmos.UserCurrentChat import upsert_userConversation, read_userConversation
from utilities.utils import convert_to_langchain_format

def executeProposalAgent(query: str, user_id: str, id: str):
    response = {
        "input": query,
        "id": id,
        "user_id": user_id,
        "response": []
    }

    # Prepare message format for agent (no history)
    messages = convert_to_langchain_format([{"role": "user", "content": query}])
    print("This is input message for agent: ", messages)

    # Inject user_id and id into tool calls
    @chain
    def inject_user_id(ai_msg):
        tool_calls = []
        for tool_call in ai_msg.tool_calls:
            tool_call_copy = deepcopy(tool_call)
            tool_call_copy["args"]["user_id"] = user_id
            tool_call_copy["args"]["id"] = id
            tool_calls.append(tool_call_copy)
        return tool_calls

    # Map tool calls to actual tools
    tool_map = {tool.name: tool for tool in proposal_agent_tools}

    @chain
    def tool_router(tool_call):
        return tool_map[tool_call["name"]]

    # Build proposal agent tool chain
    proposalToolChain = proposal_agent_llm_with_tools | inject_user_id | tool_router.map()

    # Run agent with current user query
    tool_msg = proposalToolChain.invoke(messages)

    print(f"message from Proposal Agent: {tool_msg}.")

    if len(tool_msg) <= 0:
        print("normal conversation (fallback)")

        system_prompt = """
Instructions:
- You are a Proposal Agent for Microsoft Dynamics 365 implementations.
- Help the user with any proposal-related queries like modules, migrations, integrations, or business processes.
- If user's input is ambiguous or unrelated to proposals, politely ask them to clarify or route them to the correct department.
"""

        messages = convert_to_langchain_format([{"role": "system", "content": system_prompt}, {"role": "user", "content": query}])
        ai_msg = llm.invoke(messages)

        response['response'].append(
            {
                "viewType": "simplechat",
                "content": str(ai_msg.content),
            }
        )

    else:
        print("tool call conversation")
        print("Tool", tool)
        for tool_response in tool_msg:
            tool_response = json.loads(tool_response.content)
            print("tool call content", tool_response)

            response['response'].append(tool_response)

    return json.dumps(response)