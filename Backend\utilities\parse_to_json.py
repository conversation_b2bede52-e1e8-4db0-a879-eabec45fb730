import json
import re
import json

def parse_to_json_function(response):
    
    # Extract the JSON part from the response
    start_index = response.find("```json") + len("```json")
    end_index = response.find("```", start_index)
    json_string = response[start_index:end_index].strip()
    

    # Parse the JSON string
    try:       
        parsed_data = json.loads(json_string)
        print(json.dumps(parsed_data, indent=4))  # Pretty-print the parsed JSON
    except json.JSONDecodeError as e:
        error = f"Failed to parse JSON: {e}"
        print(error)

    return parsed_data




# def parse_to_json_function_doc(response):
    
#     # Extract the JSON part from the response
#     start_index = response.find("```json") + len("```json")
#     end_index = response.find("```", start_index)
#     res = response[start_index:end_index].strip() 
    
#     json_string = clean_json_string(res)
#     # print("------- json_string -------",json_string)
    
#     # file_name = "debug_data.json"
     
#     # with open(file_name, "w", encoding="utf-8") as f:
#     #     json.dump(json_string, f, indent=4, ensure_ascii=False)
#     #     print(f"Data successfully written to {file_name}")
    
#     # Parse the JSON string
#     try:
#         parsed_data = json.loads(json_string)
#         print(json.dumps(parsed_data, indent=4)) # Pretty-print the parsed JSON
        

#     except json.JSONDecodeError as e:
#         # print("--------- in except block")
#         json_string = f"[{clean_json_string(res)}]"
#         parsed_data = json.loads(json_string)
        
#         error = f"Failed to parse JSON: {e}"
#         print(json.dumps(parsed_data, indent=4)) # Pretty-print the parsed JSON
        

#     return parsed_data




# def clean_json_string(json_string):
#     """Clean the JSON string by fixing common issues."""
#     # Remove trailing commas
#     json_string = re.sub(r",\s*([\]}])", r"\1", json_string)
#     # Remove newlines in keys or values (if LLM accidentally adds them)
#     json_string = re.sub(r"\n", r" ", json_string)
#     # Ensure proper quoting for keys
#     json_string = re.sub(r"(\w+):", r'"\1":', json_string)
#     return json_string






# def parse_to_json_function_doc(response):
#     # Look for JSON content more reliably
#     try:
#         # First try to find JSON between "json" and next newline sequence
#         if "json" in response:
#             start_index = response.find("json") + len("json")
#             # Skip any whitespace after "json"
#             while start_index < len(response) and response[start_index].isspace():
#                 start_index += 1
            
#             # Now try to parse the remaining text as JSON
#             json_text = response[start_index:].strip()
            
#             # Try to parse directly first
#             try:
#                 return json.loads(json_text)
#             except json.JSONDecodeError:
#                 # If that fails, try with cleaning
#                 cleaned_json = clean_json_string(json_text)
#                 return json.loads(cleaned_json)
        
#         # If "json" marker not found, try to parse the entire response
#         return json.loads(clean_json_string(response))
        
#     except json.JSONDecodeError as e:
#         # Last resort: try to wrap in array brackets if it might be an object
#         try:
#             if response.strip().startswith('{'):
#                 return [json.loads(clean_json_string(response))]
#             elif not (response.strip().startswith('[') and response.strip().endswith(']')):
#                 return json.loads(f"[{clean_json_string(response)}]")
#             else:
#                 # If we get here, re-raise the original error
#                 raise e
#         except json.JSONDecodeError:
#             print(f"Failed to parse JSON: {e}")
#             print(f"Problematic JSON string: {response[:100]}...")  # Print first 100 chars for debugging
#             return []  # Return empty list as fallback

# def clean_json_string(json_string):
#     """Clean the JSON string by fixing common issues."""
#     # Remove timestamps and logging artifacts that appear in your example
#     json_string = re.sub(r'\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z\]', '', json_string)
    
#     # Remove trailing commas
#     json_string = re.sub(r",\s*([\]}])", r"\1", json_string)
    
#     # Remove newlines in keys or values
#     json_string = re.sub(r"\n", " ", json_string)
    
#     # Fix unquoted keys (but be careful not to double-quote)
#     json_string = re.sub(r'([\{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', json_string)
    
#     # Remove any extra whitespace
#     json_string = json_string.strip()
    
#     # Handle the case where there are multiple JSON arrays
#     if json_string.count('[') > 1 and json_string.count(']') > 1:
#         # Try to find the outermost array
#         first_bracket = json_string.find('[')
#         last_bracket = json_string.rfind(']')
#         if first_bracket != -1 and last_bracket != -1:
#             json_string = json_string[first_bracket:last_bracket+1]
    
#     return json_string



def parse_to_json_function_doc(response):
    try:
        # Handle case where response might be wrapped in markdown code blocks
        if "```json" in response:
            json_content = response.split("```json")[1].split("```")[0].strip()
            response_json = json.loads(json_content)
            return response_json
        else:
            response_json = json.loads(response)
            return response_json
        
        # Now response_json is a proper JSON object
        
    except json.JSONDecodeError:
        print("Failed to parse response as JSON")
