import logging
import random
import os
from azure.cosmos import CosmosClient, Partition<PERSON>ey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from Config.cosmosdb import database
from utilities.CreateGuid import generate_guide
from Config.blobService import blob_service_client

# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="TemplateState",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)

def insert_Template_state(doc_state,doc_type):    
        try:
            guid=generate_guide()
            item = {
                'id': guid,
                'user_id': "admin_id",
                'state':doc_state,
                'doc_type': doc_type,
                'createdAt': datetime.utcnow().isoformat()
        }
            container.create_item(body=item)
            return item
        except Exception as e:
         print("Error" ,e)
        
         return None  



def read_Template_State(doc_type):
    print("reading TemplateState")
    try:
        logging.info('Applying query.')
        query = f"SELECT top 1 * FROM c WHERE c.doc_type = '{doc_type}' order by c.createdAt desc"
        
        item= container.query_items(query = query, enable_cross_partition_query = True)
        
        print(f"Retrieved (query) .",item)
        

        return list(item)[0]
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
    
        return None