from pydantic import BaseModel, Field
from typing import List, Optional

class DocumentStructureModel(BaseModel):
    """"Statement of work document prompt"""

    Document_Title: str = Field(description = "Provide document title with name of <PERSON><PERSON>or(Your company) and Customer")
    Customer_Segment: str = Field(description = "Provide me the customer name with business segmentation in following format [Customer Name]")
    Vendor_Segment : str = Field(description = "Provide me the customer name with business segmentation in following format  [Vendor Name], [Vendor Company Name]")
    Agreement_Date : str = Field(description = "Date of today or when agreement is being set. ")
    Introduction : str = Field(description = "Provide brief information about the customer company, and how vendor will be delivering the D365FO modules.")
    Purpose : str = Field(description = "Outline the purpose of the document, for example, to define the scope and estimates for adding new functionality to an existing system, such as modyfing Sales order form.")
    Inscope_work : str = Field(description = "Give me a brief and long information in paragraph followed by bullet points, List the features or deliverables. For example: Program logic to create work headers and work lines based on item locations. New fields or menu items for tracking Last Counted Date and other relevant information.")
    Outscope_Work : str = Field(description = "Identify any areas that are not covered, such as unrelated system functionalities.")    
    Project_Duration : str = Field(description = "Give me a project duration in weeks and total number of hours along with the breakdown of requirement gathering, solution development, testing, deployment, and user training in bullets points")
    Resource_Costing : str = Field(description = "Provide a detailed breakdown of resource allocation and cost per role in bullets points, for example: Project Manager: $[Hourly Rate] x [Hours] = $[Total Cost], Soultion Architect: $[Hourly Rate] x [Hours] = $[Total Cost], Developer: $[Hourly Rate] x [Hours] = $[Total Cost], Functional consultant: $[Hourly Rate] x [Hours] = $[Total Cost], Trainer: $[Hourly Rate] x [Hours] = $[Total Cost], Also give me Total estimated cost: $[Total project cost] ")
    Deliverable_Acceptance_Process : str = Field(description = "Outline the acceptance and rejection process for the deliverables, detailing how the Customer will review and approve the work.")
    Signature_Block : str = Field(description = "Include signature lines for both parties, such as: Customer Name: [Customer Representative Name], [Title] & Vendor Name: [Vendor Representative Name], [Title]")
  