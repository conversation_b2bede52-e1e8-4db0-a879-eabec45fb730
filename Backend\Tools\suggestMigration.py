import os
from dotenv import load_dotenv
import pandas as pd
# from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
import json

from LLMChains.Base_LLMChain_ModelClass import Base<PERSON><PERSON><PERSON>nC<PERSON>
from StructuredSchemas.business_process_extraction_schema import BPStructureModel
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated

from utilities.get_business_processes import get_business_processes

from Config.azureAI import llm
from DatabaseCosmos.Data_Migration_State import insert_DM_State

from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from DatabaseCosmos.StatesChecklist import updateDMState
from Tools.suggestPills import getSuggestedPills
from DatabaseCosmos.Company_info_State import read_CompanyInfo

# Load environment variables from .env file
load_dotenv()


@tool(return_direct=True)
def suggest_Data_migrations(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is use to recommend or suggest the Data Migration"""

    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "errorkey": "companyNameNotFound"
        })
    dm_list= [
        {
            "label": "Customer Data",
            "id": "1",
            "inputPlaceholder":"approx 8000",
            
            "group": "MasterData",
            "type": "CheckboxwithText",
            "isChecked":False
        },
        {
            "label": "Vendor Supplier Data",
            "id": "2",
            "inputPlaceholder":"approx 8000",
            
            "type": "CheckboxwithText",
            "group": "MasterData",
            "isChecked":False
        },
        {
            "label": "Product / Items Master",
            "id": "3",
            "inputPlaceholder":"approx 100",
            "type": "CheckboxwithText",
            "group": "MasterData",
            "isChecked":False
        },
        {
            "label": "Chart of Accounts",
            "id": "4",
            "inputPlaceholder":"",
            "type": "CheckboxwithText",
            "group": "MasterData",
            "isChecked":True
        },
        {
            "label": "Open Sales Order",
            "id": "5",
            "inputPlaceholder":"",
            "group": "OpenTransactions",
            "type": "CheckboxwithText",
            "isChecked":False
        },
        {
            "label": "Open Purchase Order",
            "id": "6",
            "inputPlaceholder":"",
            "group": "OpenTransactions",
            "type": "CheckboxwithText",
            "isChecked":False
        },
        {
            "label": "Open Invoices",
            "id": "7",
            "inputPlaceholder":"",
            "group": "OpenTransactions",
            "type": "CheckboxwithText",
            "isChecked":False
        },
        {
            "label": "Inventory Balance",
            "id": "8",
            "inputPlaceholder":"",
            "group": "OpenTransactions",
            "type": "CheckboxwithText",
            "isChecked":False
        }
    ]

    insert=insert_DM_State(chat_id=id,user_id=user_id,dm_list=dm_list)
    print("database insertion on Data Migration state completed")
    print(insert)
    
    
    #Intitializing Company Search State in DB True
    print("updating BP state to true in db -----------------------------------")
    updateDMState(user_id=user_id,value="True")
    
    ### chat history
    # reading current chat from db
    UserChatHistory = read_userConversation(id)
    print("This is current user chat history :-------------------,",UserChatHistory['History'])
    # #upserting assistance response into db
    # UserChatHistory['History'].append({"role": "assistant", "content": company_data})
    UserChatHistory['History'].append({"role": "tool", "content": "data_migration"})
    upsert_userConversation(UserChatHistory)
    
    # get suggested pills 
    suggested_pills= getSuggestedPills(user_id)
    
    
    
    return json.dumps({
                "id": id,
                "user_id": user_id,
                "viewType": "datamigration",
                "title": "Dynamics 365 - Data Migration",
                "type": "CheckboxWithText",
                "description": "The Data Migration process is critical to ensure that all necessary data  from existing system is accurately transfered to Dynamics 365 ,enabling  seamless buisness operation post - implementation.Below are the type of data migration  that may  be needed!",
                "fields": dm_list,
                
                "pills": suggested_pills
            })


# # # Call the function and print the result
# result = suggest_business_processes(company_info)
# print(result)
