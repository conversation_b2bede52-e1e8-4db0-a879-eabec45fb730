import requests
import os
from langchain_core.tools import tool
from dotenv import load_dotenv

load_dotenv() 

@tool
def companySearchApolloTool(company_name:str) -> str:
    """Search company using Apollo. Give details like What is the company type, its major clients, product type, email id, phone number, Number of employees, headquarters address, company revenue, main products, buisness module, headquarters address, subsidiaries, industry name and type, SIC_Code, its linkedin account link, website link and any other information avaiable."""

    url = "https://api.apollo.io/api/v1/mixed_companies/search"
    data = {
        "page":1,
        "per_page":10,  
        # "organization_num_employees_ranges":["1,100", "1,1000"],
        # "organization_locations":["United States"],
        # "q_organization_keyword_tags":["sales strategy", "lead"],
        "q_organization_name":company_name
    }
    headers = {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'X-Api-Key': os.getenv("APOLLO_API_KEY")
    }
    response = requests.request("POST", url, headers=headers, json=data)
    print(response.text)

    return response.text
