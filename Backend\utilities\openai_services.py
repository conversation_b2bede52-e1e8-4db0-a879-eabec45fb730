import uuid
from DatabaseCosmos.UserCurrentChat import read_userConversation,create_userConversation,upsert_userConversation

class OpenAIService:
    @staticmethod
    def handle_request(id: str,user_id: str, user_input: str, client,conversation_histories: dict):
        """
        Handle user requests, retrieve or create chat sessions, and generate assistant responses.
        
        :param chat_id: The current chat session ID.
        :param user_id: The ID of the user.
        :param user_input: The input provided by the user.
        :param client: The OpenAI API client.
        :param conversation_histories: The conversation history to update.
        :return: A dictionary containing the assistant response and updated conversation history.
        """
        print("inopen ai service")

        if not id:
            print(id)
            result: dict = create_userConversation(user_id,conversation_histories)
        else:
            result: dict = read_userConversation(id)

        result['History'].append({"role": "user", "content": user_input})
        
        # Generate assistant response
        assistant_response = OpenAIService.get_assistant_response(client, result.get('History', []))
        print("openai:"+assistant_response)
        result['History'].append({"role": "assistant", "content": assistant_response})
        upsert_userConversation(result)

        return {
            "response": assistant_response,
            "history": result
        }

       
    @staticmethod
    def get_assistant_response(client, conversation_history):
        """
        Generate the assistant's response using OpenAI's API.
        
        :param client: The OpenAI API client.
        :param conversation_history: The conversation history for generating the response.
        :return: The assistant's response as a string.
        """
        print("OpenAI calling ")
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=conversation_history,
                max_tokens=800,
                temperature=0.7,
                top_p=0.95,
                frequency_penalty=0,
                presence_penalty=0
            )
            print("response")
            return response.choices[0].message.content.encode('ascii', errors='ignore').decode('ascii')
        except Exception as e:
            raise Exception(f"OpenAI API request failed: {str(e)}")
   
    