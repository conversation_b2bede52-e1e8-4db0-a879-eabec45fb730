from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_core.prompts import ChatPromptTemplate
from AzureAi import llm
from Tools.company_tools import company_tools

from langchain.prompts import PromptTemplate

company_prompt = PromptTemplate.from_template("""
You are a Company Agent. Answer company related queries only.

User query: {input}

{agent_scratchpad}
""")


company_agent = create_openai_functions_agent(llm, company_tools, prompt=company_prompt)
company_agent_executor = AgentExecutor(agent=company_agent, tools=company_tools, verbose=True)
