from pydantic import BaseModel, Field
from typing import List

class ISVModel(BaseModel):
    type: str = Field(default="checkboxWithTextGrouping")
    integration_name: str = Field(description="name of integration/ISV eg: Warehouse management")
    placeholder_suggested_value: str = Field(description="name of Suggested/placeholder value eg: Blue Yonder")
    isChecked: bool= Field(False)
    description: str = Field(description="description of the integration/ISV")
    id: str = Field(description="ID for the ISV. eg:1")
    group: str = Field(description = "Category of ISV/Integration")
    

class ISVStructureModel(BaseModel):
    isv_list: List[ISVModel] = Field(description="List of ISVModels")
    reasoning: str = Field(description="Short 1 or 2 line description for Reasoning behind the suggested ISV. Just for the sake of understanding. Dont mention any ISV in this field.")

    