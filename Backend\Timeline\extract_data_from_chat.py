from pprint import pprint
from utilities.fetchChat import fetchChatSummary
def extract_data(chat_id):

    chat_history = fetchChatSummary(
        id=chat_id,
        getChatHistory=False,
        returnRaw=True
    )
    keys_to_extract = {"modules", "company", "datamigration" ,"erp", "integration"}
    company_keys = {"companyinformation", "companyname", "industry"}

    # Filter dictionary
    filtered_dict = {key: value for key, value in chat_history.items() if key in keys_to_extract}
    if "company" in filtered_dict:
            filtered_dict["company"] = {
                key: value for key, value in filtered_dict["company"].items() if key in company_keys
            }
    # Print the result
    pprint(filtered_dict)
    return filtered_dict