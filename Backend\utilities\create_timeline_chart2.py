import pandas as pd
import matplotlib.pyplot as plt
from io import BytesIO
import matplotlib.patches as mpatches
import math
import matplotlib.ticker as ticker


def create_chart(tasks: str):
    # Prepare Gantt chart data
    # Create a DataFrame for easier handling  
    tasks_df = pd.DataFrame(tasks)  
    
    # Aggregate the tasks by phase to avoid duplication  
    agg_tasks_df = tasks_df.groupby('phase').agg({'week': lambda x: ','.join(x)}).reset_index()  
    
    # Get unique phases dynamically
    unique_phases = agg_tasks_df['phase'].unique()
    
    # Assign the same color to each phase
    colors = {phase: '#00B2E1' for phase in unique_phases}
    
    # Sort the data according to the weeks
    agg_tasks_df['start_week'] = agg_tasks_df['week'].apply(lambda x: int(x.split(',')[0].split('-')[0]) if '-' in x else int(x))
    ordered_agg_tasks_df = agg_tasks_df.sort_values(by='start_week').reset_index(drop=True)
    
    _, ax = plt.subplots(figsize=(12, 6))  
    
    # Determine the total number of weeks dynamically  
    total_weeks = get_total_weeks(tasks)  
    month_ranges = calculate_months(total_weeks)  
    
    # Plot the phases as colorful bars  
    y_positions = range(len(ordered_agg_tasks_df))  # Y positions for phases  
    for idx, entry in ordered_agg_tasks_df.iterrows():  
        phase = entry['phase']  
        weeks = entry['week'].split(',')
        for week_range in weeks:
            start_week, end_week = parse_weeks(week_range.strip())
            ax.barh(y=idx, width=end_week - start_week + 1, left=start_week, height=0.3, color=colors[phase])  
    
    # Draw grid lines for weeks  
    for week in range(1, total_weeks + 1):  # Lines for each week  
        ax.axvline(week, color='gray', linestyle='--', lw=0.5)  
    
    # Add solid lines for month separators  
    for start, end in month_ranges:  
        ax.axvline(start, color='black', linestyle='-', lw=1)  
    
    # Customize Y-axis with phase names  
    ax.set_yticks(y_positions)  
    ax.set_yticklabels(ordered_agg_tasks_df['phase'])  
    ax.invert_yaxis()  
    
    # Add week labels at the bottom  
    ax.xaxis.set_major_locator(ticker.MultipleLocator(1))  
    ax.xaxis.set_major_formatter(ticker.FuncFormatter(lambda x, _: f"W{x:.0f}" if x > 0 else ""))  

    # Move the week labels to the top
    ax.xaxis.set_ticks_position('top')
    ax.tick_params(axis='x', which='both', length=0)  # Remove ticks on the bottom
    
    # Add month labels at the top  
    for idx, (start, end) in enumerate(month_ranges):  
        mid = (start + end) / 2  
        plt.text(mid, len(ordered_agg_tasks_df) + 0.1, f"M{idx + 1}", ha='center', va='center', fontsize=10, fontweight='bold')  
    
    # Set plot limits and labels  
    ax.set_xlim(1, total_weeks + 1)  
    ax.set_xlabel("Weeks")  
    # ax.set_title("Phases and Timeline")  
    plt.tight_layout()  
    
    # Display the chart  
    # plt.show()  
    
    # Save the plot as a PNG image for docs and frontend

    img_stream = BytesIO()
    plt.savefig(img_stream, format='png') 
    img_stream.seek(0)  

    #How to retrieve image_stream as bytes
    img_bytes = img_stream.getvalue()
    
    return img_bytes


# Function to convert week string to start and end week  
def parse_weeks(week_range):
    if '-' in week_range:
        start, end = map(int, week_range.split('-'))
    else:
        start = end = int(week_range)  # Single week
    return start, end

def calculate_months(total_weeks):  
    month_ranges = []  
    week_per_month = 4  # You can change this based on your needs  
    current_week = 1  
    for _ in range(1, (total_weeks // week_per_month) + 1):  
        start = current_week  
        end = min(current_week + week_per_month - 1, total_weeks)  
        month_ranges.append((start, end))  
        current_week += week_per_month  
    return month_ranges

def get_total_weeks(tasks):  
    max_week = 0  
    for task in tasks:  
        _, end_week = parse_weeks(task['week'])  
        max_week = max(max_week, end_week)  
    return max_week

def createBarChart(data):
    df = pd.DataFrame(data)
    df = df.assign(resources=df['resources'].str.split(', ')).explode('resources')
    
    resource_allocation = df.groupby(['resources', 'week'])['allocatedhours'].sum().unstack(fill_value=0).T
    
    # Sort weeks in the required order
    sorted_weeks = sorted(resource_allocation.index, key=lambda x: (int(x.split('-')[0]), int(x.split('-')[-1])))
    resource_allocation = resource_allocation.loc[sorted_weeks]
    
    total_hours_per_resource = resource_allocation.sum()
    total_hours_all_resources = total_hours_per_resource.sum()
    
    colors = [  
    '#1b9e77', '#d95f02', '#7570b3', '#e7298a', '#66a61e',   
    '#e6ab02', '#a6761d', '#666666', '#377eb8', '#ff7f00',  
    '#4daf4a', '#984ea3', '#ffff33', '#a65628', '#f781bf',   
    '#999999', '#8dd3c7', '#ffffb3', '#bebada', '#fb8072',  
    '#80b1d3', '#fdb462', '#b3de69'  
    ]  
    
    ax = resource_allocation.plot(kind='bar', stacked=True, figsize=(14, 8), color=colors, width=0.4)  
    legend_texts = [f"{resource} - {total_hours} hrs" for resource, total_hours in total_hours_per_resource.items()]  
    ax.legend(legend_texts, title=f"Resources (Total: {total_hours_all_resources} hrs)", bbox_to_anchor=(1.05, 1), loc='upper left')  

    plt.title('Resource Allocation per Week')  
    plt.xlabel('Week')  
    plt.ylabel('Allocated Hours')  
    plt.tight_layout()  
    plt.show()  








