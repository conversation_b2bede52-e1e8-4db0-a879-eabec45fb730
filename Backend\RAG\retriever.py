from Config.azureAI import client
from Config.azureEmbeddings import embeddings
from Config.azureIndexes import search_client2
from azure.search.documents.models import VectorizedQuery

def summary_retriever(content , top_k):
    """
    Retrieve the most relevant document summary based on the provided content.

    Args:
        content (str): The content to be used for the search query.

    Returns:
        str: The file ID of the retrieved document.
    """
    query_embedding = embeddings.embed_query(content)  # Embed the query content

    results = search_client2.search(
        search_text=None,
        vector_queries=[
            VectorizedQuery(
                vector=query_embedding,
                fields="contentVector"
            )
        ],
        select=["title", "content", "file_id"],
        top=top_k
    )
    
    file_ids = [result['file_id'] for result in results]
    print(file_ids)
    return file_ids

def chunk_retriever(file_id , query):
    """
    Retrieve and display content related to a specific file ID from chunked document data.

    Args:
        file_id (str): The unique identifier of the document to be retrieved.
    """
    user_query = query
    retrieval_completion = client.chat.completions.create(
        model='gpt-4o',
        messages=[
            {
                "role": "system",
                "content": "You are an AI assistant that reads documents and answers questions based on the information in the documents."
            },
            {"role": "user", "content": user_query}
        ],
        max_tokens=800,
        temperature=0.3,
        top_p=0.7,
        frequency_penalty=0,
        presence_penalty=0,
        stream=False,
        extra_body={
            "data_sources": [{
                "type": "azure_search",
                "parameters": {
                    "endpoint": search_client2._endpoint,
                    "index_name": search_client2._index_name,
                    "query_type": "vector_simple_hybrid", 
                    "fields_mapping": {
                        "content_fields_separator": "\n",
                        "content_fields": ["content"],
                        "title_field": "title",
                        "vector_fields": ["contentVector"]
                    },
                    "filter": f"file_id eq '{file_id}'",
                    "top_n_documents": 3,
                    "authentication": {
                        "type": "api_key",
                        "key": search_client2._credential.key
                    },
                    "embedding_dependency": {
                        "type": "deployment_name",
                        "deployment_name": "text-embedding-3-small"
                    }
                }
            }]
        }
    )
    retrieved_info = retrieval_completion.choices[0].message.content
    return retrieved_info
