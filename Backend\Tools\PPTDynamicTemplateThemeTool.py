from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import io

from DatabaseCosmos.Timeline import GetTimeline
from utilities.create_timeline_chart2 import create_chart
from utilities.create_timeline_chart3 import create_gantt_chart, get_ganttchart_data

from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.UserCurrentChat import read_userConversation, upsert_userConversation
from utilities.upload_file import upload_file_to_blob
from utilities.fetchChat import fetchChatSummary
from utilities.ppt_generation_chains import implementation_methodology_chain, introduction_chain, project_scope_chain
from langchain_core.runnables import RunnableParallel

from utilities.convert_csv_to_array import csv_to_array
from utilities.convert_obj_to_array import object_to_2d_array
from utilities.convert_integration_obj_to_array import convert_integration_obj_to_array

from DocumentGeneration.Presentation import PresentationGenerator
from utilities.parse_to_json import parse_to_json_function
from Tools.suggestPills import getSuggestedPills
from DatabaseCosmos.ProjectEstimation import read_ProjectEstimation_State

from pptx import Presentation
from pptx.util import Pt
from pptx.util import Inches
from pptx.dml.color import RGBColor

from DocumentGeneration.Document import DocumentGenerator_base_template , DocumentGenerator_user_template
import os
from DatabaseCosmos.Theme_template_upload import read_Template_FileName
from Config.blobService import blob_service_client
from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from datetime import datetime, timedelta
import pandas as pd
theme_path = "./DocumentGeneration/Presentation/Themes/Quisitive/theme.json"
static_csv_paths = {
    "generalProjectScope": "./Data/GeneralProjectScope.csv",
    "trainingInScope": "./Data/TrainingInScope.csv",
    "DMResponsibilities": "./Data/DMResponsibilities.csv",
    "outOfScope": "./Data/OutOfScope.csv",
}

def content_creator(company_info=None, id=None, user_id=None):
    
    
    if company_info:
        document_type = company_info['companyname']
    else:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name":"createDynamicFlowPPT_Tool",
            "errorkey": "companyNameNotFound"
        })
        
    msg_summary = fetchChatSummary(id=id)
    if msg_summary == "":
        msg_summary = "Want to create a SOW for the company: "

    chat_history = fetchChatSummary(
        id=id,
        getChatHistory=False,
        returnRaw=True
    )
    
    response_list = [
        {
            "type": "title",
            "title": document_type,
            "subtitle": "Microsoft Dynamics 365 - Finance & Operations, Budgetary Price Estimate"
        },
        {
            "type": "image",
            "title": "Quisitive at a Glance",
            "imagePath": "./Data/QuisitiveGlance2.png",
            "left": 0.3,
            "top": 2,
            "height": 5
        },
        {
            "type": "image",
            "title": "Why Quisitive?",
            "imagePath": "./Data/WhyQuisitive.png",
            "left": 0.3,
            "top": 2,
            "height": 5
        },
        {
            "type": "image",
            "title": "Why Microsoft Partnership Matters",
            "imagePath": "./Data/Partnership.png",
            "left": 0.3,
            "top": 2,
            "height": 5
        },
        {
            "type": "image",
            "title": "Innovation Across Industries",
            "imagePath": "./Data/Industries.png",
            "left": 0.3,
            "top": 2,
            "height": 5
        }
        
    ]

    
    
    #parallel execution chain
    parallel_chain = RunnableParallel(
        introduction=introduction_chain
        )
    
    response = parallel_chain.invoke({"context": msg_summary})

    for chain_type, chain_content in response.items():

        content = chain_content.content
        response_list.extend(parse_to_json_function(content))

    # static content starting from here
    response_list.append(
        {
            "type":"table",
            "title": "General Project Scope",
            "table": csv_to_array(static_csv_paths["generalProjectScope"])
        },
    )

    try:
        response_list.append(
            {
                "type":"table",
                "title": "Process In Scope",
                "table": object_to_2d_array(chat_history["businessprocesses"])
            },
        )
        
    except Exception as e:
        pass

    try:
        response_list.append(
            {
            "type": "table",
            "title": "Application Modules",
            "table": object_to_2d_array(chat_history["modules"])
            },
        )
    except Exception as e:
        pass

    response_list.append(
        {
            "type":"table",
            "title": "Training In Scope",
            "table": csv_to_array(static_csv_paths["trainingInScope"])
        },
    )

    try:
        response_list.extend([
            {
            "type": "table",
            "title": "Data Migration In Scope",
            "table": object_to_2d_array(chat_history["datamigration"])
            },
            {
                "type":"table",
                "title": "Data Migration Responsibilities",
                "table": csv_to_array(static_csv_paths["DMResponsibilities"])
            },  
        ])
    except Exception as e:
        pass

    try:
        response_list.append(
            {
            "type":"table",
            "title": "Integrations",
            "table": convert_integration_obj_to_array(chat_history["integration"])
            },    
        )
    except Exception as e:
        pass

    response_list.extend([
        {
        "type":"table",
        "title": "Out of Scope",
        "table": csv_to_array(static_csv_paths["outOfScope"])
        },
        {
            "type": "title",
            "title": "Implementation Approach",
            "subtitle": "",
        },
        {
            "type": "paragraph",
            "title": "What is Microsoft Dynamics Sure Step?",
            "paragraph": "A proven methodology with Integrated tools and project management for smooth Implementation. Sure Step helps us deliver a predictable, on-time and on-budget Microsoft Dynamics 365 implementation that can provide you a quick return on investment and meet your business requirements."
        },
        {
            "type": "image",
            "title": "Implementation Methodology",
            "imagePath": "./Data/ImplementationApproach.png",
            "left": 1,
            "top": 2,
            "height": 4
        },

        {
            "type": "image",
            "title": "Quisitive & Microsoft - Integrated Delievery Framework",
            "imagePath": "./Data/Framework.png",
            "left": 0.8,
            "top": 2,
            "height": 5
        },
        {
            "type": "image",
            "title": "Project Team Organization",
            "imagePath": "./Data/Flow.png",
            "left": 0.7,
            "top": 2,
            "height": 5
        },
        {
            "type": "image",
            "title": "Project Team Organization",
            "imagePath": "./Data/TeamOrganization.png",
            "left": 0.7,
            "top": 2,
            "height": 5
        },
        {
            "type": "image",
            "title": "Quisitive Project Leadership Team",
            "imagePath": "./Data/LeadershipTeam.png",
            "left": 0.4,
            "top": 2,
            "height": 4
        },
        {
            "type": "image",
            "title": "Quisitive Project Delievery Team",
            "imagePath": "./Data/DelieveryTeam2.png",
            "left": 0.5,
            "top": 2,
            "height": 3
        }
    ])

    try:
        # # get image
        # timelinedata = GetTimeline(id=id)
        
        # image = create_chart(timelinedata['timeline'])
        # image_stream = io.BytesIO(image)

        # projectestimation_data = read_ProjectEstimation_State(id)
        timelinedata = GetTimeline(id=id)

        timeline_dict = timelinedata['timeline']
        timeline_df = pd.DataFrame.from_dict(timeline_dict['data'])

        ganttchart_data = get_ganttchart_data(timeline_df)
        image = create_gantt_chart(ganttchart_data)

        image_stream = io.BytesIO(image)
        projectestimation_data = read_ProjectEstimation_State(id)


        response_list.extend([
                {
                    "type": "title",
                    "title": "Proposal",
                    "subtitle": "Timeline & Cost Estimates"
                    },
                {
                    "type": "image",
                    "title": "Proposal | Project Timeline",
                    "imagePath": image_stream,
                    "left": 1.5,
                    "top": 2,
                    "height": 5
                },
                {
                    "type": "costing",
                    "title": "Proposal | Professional Services",
                    "duration": str(projectestimation_data['months']),
                    "budget": f"{projectestimation_data['Budget']}" if not projectestimation_data['Budget']=="" else "N/A",
                    "estimatedHours": str(projectestimation_data['Estimated Hours']),  
                    "totalTeamMembers": str(len(projectestimation_data['Resources'])),
                    },
            ])
    except Exception as e:
        pass

    response_list.append(
        {
            "type": "title",
            "title": "Questions",
            "subtitle": "",
        }
    )
    
    return response_list


def add_table_to_slides(presentation, data, title_text, max_rows_per_slide=5):
    """
    Adds a table to the presentation, splitting it across slides if necessary.
    Removes slide background and unnecessary placeholders.
    """
    # Define table position and dimensions
    left = Inches(1)
    top = Inches(2)
    width = Inches(11)
    height = Inches(2.5)
    font_size = 16

    # Determine the total number of rows and columns
    rows, cols = len(data), len(data[0])
    current_row = 1  # Start from the first row of data (header row is separate)

    while current_row < rows:
        # Add a new slide for the table
        slide = presentation.slides.add_slide(presentation.slide_layouts[1])  # Title and Content layout

        # Remove the slide background
        slide.background.fill.solid()
        slide.background.fill.fore_color.rgb = RGBColor(255, 255, 255)  # White background

        # Remove other placeholders except the title
        for shape in slide.shapes:
            if shape.is_placeholder and shape != slide.shapes.title:
                sp = shape._element
                sp.getparent().remove(sp)

        # Add title to the slide
        title = slide.shapes.title
        title.text = title_text

        # Determine rows to add to this slide
        start_row = current_row
        end_row = min(current_row + max_rows_per_slide, rows)

        # Add table
        table = slide.shapes.add_table(end_row - start_row + 1, cols, left, top, width, height).table

        # Add the header row (always the first row of the data)
        for col_idx, header in enumerate(data[0]):
            cell = table.cell(0, col_idx)
            cell.text = str(header)
            # Adjust font size
            for paragraph in cell.text_frame.paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(font_size)

        # Add the data rows
        for table_row, data_row in enumerate(data[start_row:end_row], start=1):
            for col_idx, cell_value in enumerate(data_row):
                cell = table.cell(table_row, col_idx)
                cell.text = str(cell_value)
                # Adjust font size
                for paragraph in cell.text_frame.paragraphs:
                    for run in paragraph.runs:
                        run.font.size = Pt(font_size)

        # Update current_row for the next iteration
        current_row = end_row

def add_image_to_slide(slide, image_stream, title_text , left=0.5, top=2, height=5):
    """Adds an image to the slide and removes unnecessary placeholders."""
    # Set the slide title
    title = slide.shapes.title
    if title:
        title.text = title_text

    # Remove other placeholders (e.g., subtitle or content placeholders)
    for shape in slide.shapes:
        if shape.is_placeholder and shape != title:  # Keep the title, remove others
            sp = shape._element
            sp.getparent().remove(sp)

    # Define image position and size
    left = Inches(left)
    top = Inches(top)
    height = Inches(height)
    
    # Add image
    slide.shapes.add_picture(image_stream, left, top, height=height)


def add_paragraph_to_slide(slide, title_text, paragraph_text):
    """Adds a paragraph to the slide."""
    title = slide.shapes.title
    title.text = title_text

    # Add the paragraph to the content placeholder
    content = slide.placeholders[1]
    content.text = paragraph_text



def get_sas_url_for_document(file_name):
    
    blob_name=f"{file_name}"

    ################################33 Remember to add check here for file upload completion
    
    container_name = os.getenv("Blob_output_container_name")

    # Generate the SAS token for secure access to the blob
    sas_token = generate_blob_sas(
        account_name = blob_service_client.account_name,
        container_name = container_name,
        blob_name = blob_name,
        account_key = os.environ['BLOB_ACCOUNT_KEY'],  # Use the account key to sign the SAS token
        permission = BlobSasPermissions(read = True),  # Allow read access only
        expiry = datetime.now() + timedelta(hours = 400),  # Token expires in 4 hour
        protocol = 'https',  # Enforce HTTPS for secure access
        version = '2022-11-02'
    )

    # Construct the full URL for accessing the blob with the SAS token
    blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{container_name}/{blob_name}"
    sas_url = f"{blob_url}?{sas_token}"
    return sas_url

  
import requests

#@tool(return_direct=True)
def createDynamicFlowPPT_Tool2(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is use to generate Presentation and wil return you the url to dowload the Presentation, Call this tool when User asked to create Presentation or ppt"""

    ppt_url = ""
    docx_url = ""
    
    document_type = "company_name"
    company_info = read_CompanyInfo(id)
    if company_info:
        document_type = company_info['companyname']
    else:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name":"createDynamicFlowPPT_Tool",
            "errorkey": "companyNameNotFound"
        })
        
    msg_summary = fetchChatSummary(id=id)
    if msg_summary == "":
        msg_summary = "Want to create a SOW for the company: "

    chat_history = fetchChatSummary(
        id=id,
        getChatHistory=False,
        returnRaw=True
    )
    
    
    response_list = content_creator(company_info, id, user_id)
    
    
    # read user template file 
   
    file_name = read_Template_FileName("ppt")
    folder_path = f"ThemeTemplate/pptFile/"
    blob_file_path = f"{folder_path}{file_name['blob_name']}"

    file_path = get_sas_url_for_document(blob_file_path)
    user_template= file_path
    
    # Send a GET request to download the file
    response = requests.get(user_template)
    response.raise_for_status()  # Raise an HTTPError for bad responses (4xx and 5xx)

    # Read the file into a BytesIO stream
    ppt_stream = io.BytesIO(response.content)

    # Load the PowerPoint presentation
    presentation = Presentation(ppt_stream)
    
    
   
    for item in response_list:
        # Handle title slides
        if item["type"] == "title":
            slide = presentation.slides.add_slide(presentation.slide_layouts[0])  # Title Slide layout
            title = slide.shapes.title
            subtitle = slide.placeholders[1]
            title.text = item["title"]
            subtitle.text = item.get("subtitle", "")
        
          # Handle paragraph slides
        elif item["type"] == "paragraph":
            slide = presentation.slides.add_slide(presentation.slide_layouts[1])  # Title and Content layout
            add_paragraph_to_slide(slide, item["title"], item["paragraph"])


        elif item["type"] == "table":
            
            add_table_to_slides(presentation, item["table"], item["title"], max_rows_per_slide=5)

        # Handle image slides
        elif item["type"] == "image":
            slide = presentation.slides.add_slide(presentation.slide_layouts[1])  # Title and Content layout
            add_image_to_slide(slide, item["imagePath"], item["title"], item["left"], item["top"], item["height"])

        # Handle costing slides
        elif item["type"] == "costing":
            slide = presentation.slides.add_slide(presentation.slide_layouts[1])  # Title and Content layout
            title = slide.shapes.title
            content = slide.placeholders[1]
            title.text = item["title"]
            costing_details = (
                f"Duration: {item['duration']} months\n"
                f"Budget: {item['budget']}\n"
                f"Estimated Hours: {item['estimatedHours']}\n"
                f"Total Team Members: {item['totalTeamMembers']}"
            )
            content.text = costing_details
            

    # Save the document
    file_path = f"{user_id}/{id}/{document_type}_presentation.pptx"

    # Save the rendered document to an in-memory stream
    output_stream = io.BytesIO()
    presentation.save(output_stream)
    output_stream.seek(0)  # Reset stream position for uploading

    #generating sas url
    print("generating PPT URL....")
    ppt_url = upload_file_to_blob(
                                file_name=file_path,
                                output_stream=output_stream,
                                user_id=user_id,
                                id=id
                                )

    # get suggested pills 
    suggested_pills = getSuggestedPills(user_id)

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "documentcreation",
                        "tool_name":"createDynamicFlowPPT_Tool",
                        "url": {
                            "ppt_url" : ppt_url,
                            "docx_url": docx_url,
                        },
                        "pills": suggested_pills
                    }
                )