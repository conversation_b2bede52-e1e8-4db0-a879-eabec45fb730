# Imports
from langchain_core.prompts import Chat<PERSON>romptTemplate
from Config.azureAI import llm

# Proposal tools
from Tools.suggestBusinessProcess import suggest_business_processes
from Tools.suggestModules import suggest_modules_tool
from Tools.suggestERP import suggest_ERP_platform
from Tools.suggestIntegrationWithLLM import suggest_integrations
from Tools.suggestMigartionWIthLLM import suggest_Data_migrations
from Tools.PPTCreationDynamicFlowTool2 import createDynamicFlowPPT_Tool
# Define the Proposal Agent system prompt
PROPOSAL_AGENT_SYSTEM_PROMPT = """
You are a Proposal Agent responsible for handling ONLY proposal-related tasks for Microsoft Dynamics 365 (D365) implementations.

You have the following tools available to assist you:

- **suggest_business_processes**
- **suggest_modules_tool**
- **suggest_ERP_platform**
- **suggest_integrations**
- **suggest_Data_migrations**
- **createDynamicFlowPPT_Tool**

Use these tools to:
- Suggest ERP modules for implementations
- Recommend data migration approaches
- Explain or define business processes
- Suggest integrations
- Generate dynamic implementation proposal PowerPoint presentations

**Do not respond to company listing or unrelated queries.**

Use this space for reasoning and tool routing notes:
"""

# Define the Proposal Agent prompt in message-based structure
proposal_agent_prompt = ChatPromptTemplate.from_messages([
    ("system", PROPOSAL_AGENT_SYSTEM_PROMPT),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# List of Proposal Agent tools
proposal_agent_tools = [
    suggest_business_processes,
    suggest_modules_tool,
    suggest_ERP_platform,
    suggest_integrations,
    suggest_Data_migrations,
    createDynamicFlowPPT_Tool
]

# Bind LLM with tools and prompt
proposal_agent_llm_with_tools = proposal_agent_prompt | llm.bind_tools(
    proposal_agent_tools,
    parallel_tool_calls=False  # Set to True if you want multi-tool calls in parallel
)
