import os
from dotenv import load_dotenv
import pandas as pd
# from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
import json

from LLMChains.Base_LLMChain_ModelClass import Base<PERSON><PERSON>hainClass
from StructuredSchemas.business_process_extraction_schema import BPStructureModel
from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.Buisness_process_State import insert_BP_State,read_BPState

from utilities.get_business_processes import get_business_processes
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated

from DatabaseCosmos.StatesChecklist import updateBPState
from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from DatabaseCosmos.StatesChecklist import updateERPState, validation, States, updateAppModState
from Tools.suggestPills import getSuggestedPills

from Config.azureAI import llm

# Load environment variables from .env file
load_dotenv()

# llm = AzureChatOpenAI(
#     azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
#     azure_deployment=os.getenv("OPEN_AI_MODEL_NAME"),
#     openai_api_version=os.getenv("AZURE_API_VERSION"),
# )
# print("AzureChatOpenAI Initilized...")

# Load the local CSV file with ID
business_processes_df = pd.read_csv(r"./Data/BusinessProcesses.csv", encoding='utf-8')
print("pandas readed")

business_processs_extraction_system_prompt = f"""
                You are provided a company's data
                And also provided some Microsoft Dynamics Business Modules given in 'Business Processes'

                Suggest the Business Process according to the company, in a sequence of implementation interms of what should be implemented first.
                Read the given company data precisely, then suggest some business processes.

                Remeber to return just the business process IDs, in a sequence for implementation. Note! the return list shouldn't be empty
                Transform the given suggestion into the structured output provided.

                Here is a list of available business processes with their purposes and IDs:
                Business Processes :  {business_processes_df.to_string(index=False)}
                """
business_processs_extraction_chain = BaseLLMChainClass(
                                            business_processs_extraction_system_prompt,
                                            BPStructureModel
                                            )

@tool(return_direct=True)
def suggest_business_processes(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is used to select / add / recommend / suggest / scope of the business process"""
    print("i am in bp")
    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "buisness_process",
            "errorkey": "companyNameNotFound"
        })
    
    updateAppModState(user_id=user_id, value="False")
    bp_info = read_BPState(id)
    if  bp_info:
        suggested_pills= getSuggestedPills(user_id)
        # bp_info[0]["items"].sort(key=lambda x: not x["isChecked"])
        print("here is business process info", bp_info)
        #return json.dumps(bp_info[0]["object"])
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "businessprocesses",
            "tool_name": "buisness_process",
            "title": f"{company_info['companyname']} -> Business Processes" if company_info else "Business Processes",
            "type": "Checkbox",
            "description": bp_info[0]["description"] if bp_info[0]["description"] else "Business Processes you have selected",
            "fields": bp_info[0]["items"],
            "pills":suggested_pills
        })
    else:
        # id = "20241106190337-be0e9567ef2d"
        print("here is the id", id)

        if company_info:

            company_input = f"""
                            Company Name: {company_info['companyname']}
                            Company Information: {company_info['companyinformation']}
                            Number of Employees: {company_info['employees']}
                            Headquarters Location: {company_info['headquarters_address']}
                            Industry: {company_info['industry']}
                        """

            # Invoke the LLM with the prompt
            response = business_processs_extraction_chain.structure({"input": company_input})
            print(response)
            bp_list = get_business_processes(response["bp_id"])
            reasoning = response["reasoning"]
            # bp_list.sort(key=lambda x: not x["isChecked"])

        else:
            # Handle case where company_info is None or empty
            bp_list = get_business_processes([])
            reasoning = "No company information provided."
            # bp_list.sort(key=lambda x: not x["isChecked"])

        # # Insert into the database
        # insert = insert_BP_State(chat_id=id, bp_list=bp_list, user_id=user_id)
        # print("insertion into db is completed")
        # print(insert)
    
    #Intitializing Company Search State in DB True
    print("updating BP state to true in db -----------------------------------")
    updateBPState(user_id=user_id,value="True")

    #### 

    # get suggested pills 
    suggested_pills= getSuggestedPills(user_id)

    object = {
        "id": id,
        "user_id": user_id,
        "viewType": "businessprocesses",
        "tool_name": "buisness_process",
        "title": f"{company_info['companyname']} -> Business Processes" if company_info else "Business Processes",
        "type": "Checkbox",
        "description": reasoning,
        "fields": bp_list,
        "pills" : suggested_pills
    }

    # Insert into the database
    # insert = insert_BP_State(chat_id=id, bp_list=bp_list, bp_object=object, user_id=user_id)
    insert = insert_BP_State(chat_id=id, bp_list=bp_list, description=reasoning, user_id=user_id)
    print("insertion into db is completed")
    print(insert)
    
    # Prepare and return the JSON response
    return json.dumps(object)


# # # Call the function and print the result
# result = suggest_business_processes(company_info)
# print(result)