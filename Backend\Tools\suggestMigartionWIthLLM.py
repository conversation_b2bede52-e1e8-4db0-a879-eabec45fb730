import os
from dotenv import load_dotenv
import pandas as pd
# from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.tools import tool
import json

from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.App_module_State import read_AppModule_State
from DatabaseCosmos.Buisness_process_State import read_BPState
from DatabaseCosmos.ERP_State import read_ERPState
from DatabaseCosmos.Data_Migration_State import insert_DM_State,read_DMState
from LLMChains.Base_LLMChain_ModelClass import BaseLLMChainClass
from StructuredSchemas.migration_extraction_schema import DmStructureModel
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
from Tools.suggestPills import getSuggestedPills
from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from DatabaseCosmos.StatesChecklist import updateDMState
from DatabaseCosmos.Company_info_State import read_CompanyInfo


# Load environment variables from .env file
load_dotenv()


#    """This tool is use to add scope of Data Migration"""
@tool(return_direct=True)
def suggest_Data_migrations(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is used to select / add / recommend / suggest / scope of the Data Migration"""

    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "data_migration",
            "errorkey": "companyNameNotFound"
        })
    dm_data = read_DMState(id)
    # get suggested pills 
    suggested_pills= getSuggestedPills(user_id)
    print("dm data readed")
    if dm_data:
        # print("migration data already exists")
        # return json.dumps(dm_data[0]["object"])
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "datamigration",
            "tool_name": "data_migration",
            "title": f"{company_info['companyname']} -> Data Migration" if company_info else "Data Migration",
            "type": "checkboxWithTextGrouping",
            "description": dm_data[0]["description"] if dm_data[0]["description"] else "Data Migration",
            "fields": dm_data[0]["items"],
            "pills": suggested_pills
        })
    else:
        print("migration data does not exists")
        modules_selected = read_AppModule_State(id)
        unique_checked_labels = []
        if modules_selected:

            checked_labels = set()

            # Loop through each item and add group to set if isChecked is True and group exists
            for entry in modules_selected:
                for item in entry["items"]:
                    if item.get("isChecked") and item.get("label"):
                        checked_labels.add(item["label"])

            # Convert set to list if needed
            unique_checked_labels = list(checked_labels)

        if company_info:
            company_details = f"""
                            Company Name: {company_info['companyname']}
                            Company Information: {company_info['companyinformation']}
                            Number of Employees: {company_info['employees']}
                            Headquarters Location: {company_info['headquarters_address']}
                            Industry: {company_info['industry']}
                        """
        
        dm_extraction_system_prompt = f"""
I have the following application modules: {unique_checked_labels}. 
Based on these modules, generate a structured list of relevant data entities grouped into categories such as Master Data, Transactions, Legal Entities,Employees, Main account,Planning and Budgeting and Integration and Other Entities. 
For each entity, provide input placeholder examples to estimate the data volume (e.g., 'Over 1,000 customers', '150 accounts across multiple financial dimensions').

If no application modules are provided, use generic modules such as General Ledger, Accounts Receivable, Accounts Payable, Budgeting, and Project Management to create the list. 
Ensure the entities are relevant to the modules and avoid repetition.

(Optional) Company Details:
{company_details}
"""
        

        dm_extraction_chain = BaseLLMChainClass(dm_extraction_system_prompt, DmStructureModel)
        
        response = dm_extraction_chain.structure({"input": "Give me scope of Data migration" })
        print(response)
        dm_list = response["dm_list"]
        reasoning = response["reasoning"]

        dm_list_only = [
        {
            "type": item.type,
            "label": item.label,
            "inputPlaceHolders": item.inputPlaceHolders,
            "isChecked": item.isChecked,
            "id": item.id,
            "description": item.description,
            "group": item.group
        }
        for item in dm_list
        ]

        dm_object = {
            "id": id,
            "user_id": user_id,
            "viewType": "datamigration",
            "tool_name": "data_migration",
            "title": f"{company_info['companyname']} -> Data Migration" if company_info else "Data Migration",
            "type": "checkboxWithTextGrouping",
            "description": reasoning,
            "fields": dm_list_only,
            "pills": suggested_pills
            }
        
        # insert= insert_DM_State(chat_id=id, dm_list=dm_list_only, dm_object=dm_object, user_id=user_id)
        insert= insert_DM_State(chat_id=id, dm_list=dm_list_only, description=reasoning, user_id=user_id)
        print(insert)
        updateDMState(user_id=user_id,value="True")
        
        
        return json.dumps(dm_object)
