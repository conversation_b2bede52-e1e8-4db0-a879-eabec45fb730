from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import io

from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.Buisness_process_State import read_BPState
from DatabaseCosmos.App_module_State import read_AppModule_State
from DatabaseCosmos.ERP_State import read_ERPState
from DatabaseCosmos.UserCurrentChat import read_userConversation, upsert_userConversation
from utilities.upload_file import upload_file_to_blob

# from langchain.memory import ConversationBufferMemory
from Config.azureAI import llm
from langchain_core.prompts import ChatPromptTemplate
from docx import Document
from docx.shared import Pt
from docx.oxml.ns import qn
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import re
import datetime

# Set up a template to guide the language model
template = """
Todays Date: {date}
Document is generating by Quisitive
Generate a SOW document for {document_type} based on the App suggesting Microsoft 365 suggestions for the company.
Don't mention numbers, when generating list
following are the details:

{content}
"""

# # Set up a template to guide the language model
# template = """
# Todays Date: {date}
# Document is generating by Quisitive
# Generate a SOW document in HTML for {document_type} based on the App suggesting Microsoft 365 suggestions for the company.
# Remember to create the document in HTML format, start from <body> tag and add ul, headings, subheadings, tables, and etc for easy parsing.
# following are the details:

# {content}
# """

prompt = ChatPromptTemplate.from_messages(
            [
                ("system", template),
                ("human", "{input}")
            ]
        )

# Create a LangChain chain to use the template
document_chain = prompt | llm

# """This tool is use to generate the document and wil return you the url to dowload the document"""
@tool(return_direct=True)
def generate_document_tool(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is use to generate the document, use it when user specifically asks to generate a document."""
    # return json.dumps({
    #     "id": id,
    #     "user_id": user_id,
    #     "viewType": "simplechat",
    #     "content": "document generated unfortunately.",
    # })

    
    document_type = f"company_name"
    msg_summary = f"Want to create a SOW for the company: "
    ppt_url = ""
    docx_url = ""

    company_info = read_CompanyInfo(id)
    if company_info:
        company_input = f"""
                        Compnay Information:

                        Company Name: {company_info['companyname']}
                        Company Information: {company_info['companyinformation']}
                        Number of Employees: {company_info['employees']}
                        Headquarters Location: {company_info['headquarters_address']}
                        Industry: {company_info['industry']}
                    """
        document_type = company_info['companyname']
        msg_summary += company_input
    
    bp_state = read_BPState(id)
    if bp_state:
        # print("bp state", bp_state)
        bp_state_info = f"""
                        Business Processes the company choosed:
                        """
        for each_object in bp_state[0]['items']:
            if each_object['isChecked'] == True:
                bp_str = f"""

                        Label = {each_object['label']}
                        description = {each_object['description']}

                         """
                bp_state_info += bp_str

        msg_summary += bp_state_info
    
    module_state = read_AppModule_State(id)
    if module_state:
        # print("module state", module_state)
        module_state_info = f"""
                        App modules the company choosed:
                        """
        for each_object in module_state[0]['items']:
            if each_object['isChecked'] == True:
                module_str = f"""

                        Label = {each_object['label']}
                        description = {each_object['description']}

                         """
                module_state_info += module_str

        msg_summary += module_state_info

    erp_state = read_ERPState(id)
    if erp_state:
        # print("ERP state", erp_state)
        erp_state_info = f"""
                        ERP modules the company choosed:
                        """
        for each_object in erp_state[0]['items']:
            if each_object['isChecked'] == True:
                erp_str = f"""

                        Label = {each_object['label']}
                        description = {each_object['description']}

                         """
                erp_state_info += erp_str

        msg_summary += erp_state_info

    UserChatHistory = read_userConversation(id)
    print("History...",UserChatHistory['History'])
    chat_history = f"""
                    Here is the Chat History,
                    Also generate the document the user have asked for

                    Here is the chat history: {str(UserChatHistory['History'])}
                    """
    msg_summary += chat_history
    # print("msg Summary:" , msg_summary)
    


    # Get response from the model
    response = document_chain.invoke({
                    "document_type": document_type, 
                    "content": msg_summary, 
                    "date": str(datetime.datetime.now()),
                    "input": "create document"
                    })
    print("Response...", response)

    # Create a new Word document
    doc = Document()

    # Add content to the document
    doc.add_heading(f"{document_type} Document", level=1)
    
    # Parse and apply Markdown formatting to each line
    for line in str(response.content).splitlines():
        add_md_paragraph(doc, line)

    # Save the document
    file_path = f"{document_type}_document.docx"

    # # Save the rendered document to an in-memory stream
    # output_stream = io.BytesIO()
    # doc.save(output_stream)
    # output_stream.seek(0)  # Reset stream position for uploading
    # # doc.save(file_path)

    # #generating sas url
    # # print("generating SAS URL....")
    # sas_url = upload_file_to_blob(
    #                             blob_name=file_path,
    #                             output_stream=output_stream
    #                             )

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "documentcreation",
                        "url": {
                            "ppt_url" : ppt_url,
                            "docx_url": docx_url,
                        }
                    }
                )


def add_md_paragraph(doc, text):
    """Add a paragraph with Markdown interpretation for headings, bold, italics, lists, and tables."""
    if text.startswith("# "):  # Heading level 1
        doc.add_heading(text[2:].strip(), level=1)
    elif text.startswith("## "):  # Heading level 2
        doc.add_heading(text[3:].strip(), level=2)
    elif text.startswith("### "):  # Heading level 3
        doc.add_heading(text[4:].strip(), level=3)
    elif text.startswith("- "):  # Bullet point
        doc.add_paragraph(text[2:].strip(), style='ListBullet')
    elif text.startswith("* "):  # Sub-bullet point
        doc.add_paragraph(text[2:].strip(), style='ListBullet2')
    elif text.startswith("|") and "|" in text:  # Table row
        add_table_row(doc, text)
    else:
        # Regular paragraph with bold and italic handling
        paragraph = doc.add_paragraph()
        parts = re.split(r"(\*\*|\*|__|)", text)  # Split by Markdown syntax for bold/italic
        bold, italic = False, False
        for part in parts:
            if part in ("**", "__"):
                bold = not bold
            elif part == "*":
                italic = not italic
            else:
                run = paragraph.add_run(part)
                run.bold = bold
                run.italic = italic

def add_table_row(doc, row_text):
    """Create or append to a table in the document based on row text in Markdown format."""
    # Split the row text by '|' to create individual cells
    cells = [cell.strip() for cell in row_text.strip('|').split('|')]
    if 'table' not in doc.__dict__:
        # Create a new table if it doesn't exist yet
        doc.table = doc.add_table(rows=1, cols=len(cells))
        doc.table.style = 'Table Grid'
        for i, cell_text in enumerate(cells):
            doc.table.cell(0, i).text = cell_text
    else:
        # Add a row to an existing table
        row = doc.table.add_row().cells
        for i, cell_text in enumerate(cells):
            row[i].text = cell_text
    
   