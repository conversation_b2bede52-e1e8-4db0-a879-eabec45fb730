from DatabaseCosmos.Company_info_State import updateCompanyInfo, read_CompanyInfo
from langchain_core.tools import tool

from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
from LLMChains.Base_LLMChain_ModelClass import BaseLLMChainClass
# from SchemaModels.companySearchSchema import CompanyInfo
from StructuredSchemas.company_extraction_schema import CompanyStructureModel
from Tools.suggestPills import getSuggestedPills
from DatabaseCosmos.StatesChecklist import updateCompanyInfoState,validation,States



import json


@tool(return_direct=True)
def comapny_data_update_tool(user_query:str, user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    
    """ It will use to update company information. When user changes any company information data againts the existing 'chat_id', 
    it will update the information in database."""

    try:
        
        company_extraction_system_prompt = f"""
                    You will be provided user query : '{user_query}' , this contain data that need to be updated in company info database
                    You are also provided the company info object.
                    you have to change the data that user asked to change in the company info object
                    
                    Read the given query and company info object precisely.
                    Transform the given output into the structured output provided.
                    """
        company_extraction_chain = BaseLLMChainClass(
                                                company_extraction_system_prompt, 
                                                CompanyStructureModel
                                                )
        

        company_info_object = read_CompanyInfo(id)
        response = company_extraction_chain.structure({"input": str(company_info_object) })
        

        
        response["industry"] = {
            "name": response["industry_name"],
            "SIC_Code": response["SIC_Code"],
            "type_of_manufacturing": response["type_of_manufacturing"]
        }

        response["corporate_structure"] = {
            "subsidiary_of": response["corporate_structure_subsidiary_of"],
            "ownership": response["corporate_structure_ownership"]
        }

        response["employees"] = str(response["employees"])

        
        del response["industry_name"]
        del response["SIC_Code"]
        del response["type_of_manufacturing"]
        del response["corporate_structure_subsidiary_of"]
        del response["corporate_structure_ownership"]

        

        company_name = response["company_name"]
        company_information = response["company_information"]
        industry = response["industry"]["name"]
        headquarters_address = response["headquarters_address"]
        employees = response["employees"]
        corporate_structure = response["corporate_structure"]
        sic_code=response["industry"]["SIC_Code"]
        type_of_manufac=response["industry"]["type_of_manufacturing"]
        subsidiaries_and_brands = response["subsidiaries_and_brands"]

        result = updateCompanyInfo(
            chat_id=id,
            user_id=user_id,
            company_name=company_name,
            company_information=company_information,
            industry=industry,
            SIC_code=sic_code,
            type_of_manufacturing=type_of_manufac,
            headquarters_address=headquarters_address,
            employees=employees,
            corporate_structure=corporate_structure,
            subsidiaries_and_brands=subsidiaries_and_brands)
        
        updateCompanyInfoState(user_id=user_id,value="True")
        
        # get suggested pills 
        suggested_pills= getSuggestedPills(user_id)
        response= {
                        "id": id,
                        "user_id": user_id,
                        "viewType": "companyinfo",
                        "tool_name":"company_data_update",
                        "title": company_name,
                        "content": company_information,
                        "industry": industry,
                        "SIC_Code": sic_code,
                        "type_of_manufacturing":type_of_manufac,
                        "headquarters_address":headquarters_address,
                        "employees":employees,
                        "corporate_structure":response["corporate_structure"]["ownership"],
                        "subsidiaries_and_brands": subsidiaries_and_brands,
                        "pills": suggested_pills
                    }
        
        return json.dumps(response)
    
    except Exception as e:
        return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "simplechat",
                        "tool_name": "company_data_update",
                        "content": "Data not avaiable for the require company.",
                    })
        
    
    
   
    
    