import math
from Timeline.create_timeline2 import create_df
from Timeline.color_excel import color_excel
from DatabaseCosmos.resource_state import getResources, initialize_resource, updateResources
from Tools.timelineTool4 import createEstimationObject
import json
from DatabaseCosmos.Timeline import updateTimeline , insert_Timeline, GetTimeline
import io
from utilities.upload_file import upload_file_to_blob
from Tools.suggestPills import getSuggestedPills
from Timeline.resources_assignment import calculate_required_developers , generate_resource_schema, count_of_resources


def get_phase_template(role, is_first_developer=False):
    """
    Get phase template based on role
    
    Args:
        role: Role of the resource
        is_first_developer: <PERSON><PERSON><PERSON> indicating if this is the first developer
    """
    phase_templates = {
        "Project Manager": [
            {"name": phase, "hours_allocation": 0.5, "weeks_allocation": 1, 
             "order": 1, "total_hours": 0, "total_cost": 0, "total_weeks": 0}
            for phase in ["Initialization", "Solution Modeling", "Build", 
                         "Solution Testing", "Deployment", "Support"]
        ],
        "Solution Architect": [
            {
                "name": phase,
                "hours_allocation": 1.0 if phase in ["Initialization", "Solution Modeling"] else 0.5,
                "weeks_allocation": 1,
                "order": 1,
                "total_hours": 0,
                "total_cost": 0,
                "total_weeks": 0
            }
            for phase in ["Initialization", "Solution Modeling", "Build", 
                         "Solution Testing", "Deployment", "Support"]
        ],
        "Functional Consultant": [
            {"name": phase, "hours_allocation": 1.0, "weeks_allocation": 1,
             "order": 1, "total_hours": 0, "total_cost": 0, "total_weeks": 0}
            for phase in ["Initialization", "Solution Modeling", "Build", 
                         "Solution Testing", "Deployment", "Support"]
        ],
        "Infrastructure Consultant": [
            {"name": phase, "hours_allocation": 1.0, "weeks_allocation": 1,
             "order": 1, "total_hours": 0, "total_cost": 0, "total_weeks": 0}
            for phase in ["Initialization", "Solution Modeling", "Build", 
                         "Solution Testing", "Deployment", "Support"]
        ]
    }
    
    if role == "Developer":
        phases = ["Initialization", "Solution Modeling", "Build", 
                 "Solution Testing", "Deployment", "Support"]
        if is_first_developer:
            return [
                {
                    "name": phase,
                    "hours_allocation": 1.0 if phase in ["Initialization", "Solution Modeling", "Build", "Deployment"]
                                      else 0.5 if phase in ["Solution Testing", "Support"]
                                      else 0.0,
                    "weeks_allocation": 1,
                    "order": 1,
                    "total_hours": 0,
                    "total_cost": 0,
                    "total_weeks": 0
                }
                for phase in phases
            ]
        else:
            return [
                {
                    "name": phase,
                    "hours_allocation": 1.0 if phase == "Build"
                                      else 0.5 if phase in ["Solution Testing", "Support"]
                                      else 0.0,
                    "weeks_allocation": 1,
                    "order": 1,
                    "total_hours": 0,
                    "total_cost": 0,
                    "total_weeks": 0
                }
                for phase in phases
            ]
    
    return phase_templates[role]


def transform_resources(resource_counts):
    """
    Transform resource counts into detailed resource allocation format
    """
    resources = []
    role_counters = {}  # Track how many of each role we've seen
    
    for resource in resource_counts:
        resource_type = resource["type"]
        count = resource["count"]
        rate = resource["rate"]
        
        # Initialize the counter for this resource type if not exists
        if resource_type not in role_counters:
            role_counters[resource_type] = 0
        
        # Create multiple entries for this resource type
        for i in range(count):
            role_counters[resource_type] += 1
            role_number = role_counters[resource_type]
            
            # Create the numbered role name
            numbered_role = f"{resource_type} {role_number}"
            
            # Check if it's a developer
            is_developer = resource_type == "Developer"
            is_first_developer = is_developer and role_number == 1
            
            resource_id = f"{1001 + len(resources)}"
            
            # Get phases based on role
            if is_developer:
                phases = get_phase_template("Developer", is_first_developer)
            else:
                phases = get_phase_template(resource_type)
            
            # Determine build allocation
            build_allocation = "true" if resource_type in ["Developer", "Functional Consultant"] else "false"
            
            resource_entry = {
                "id": resource_id,
                "name": f"Resource {len(resources) + 1}",
                "role": numbered_role,
                "build_allocation": build_allocation,
                "rate": rate,
                "phases": phases
            }
            
            resources.append(resource_entry)
    
    return resources


def create_timeline(build_hours, resources , user_id, id):

    updated_schema = transform_resources(resources)
    print("Updated Schema: ", updated_schema)

    # resources = count_of_resources(updated_schema)

    # update resource in db
    updateResources(id, updated_schema)

    df = create_df(build_hours, updated_schema)
    print("Dataframe" , df)
    createEstimationObject(df , user_id , id)
    # create json for frontend
    json_df = df.to_json(orient='table')
    tabular_data = json.loads(json_df)

    items = GetTimeline(id)
    if items:
        updateTimeline(id, tabular_data , build_hours)
    else:  
        insert_Timeline(tabular_data, user_id,id)

    # convert the dataframe into colored excel
    excel_file = color_excel(df)

    # Save the document
    file_path = f"{user_id}/{id}/timeline.xlsx"

    # Save the rendered document to an in-memory stream
    output_stream = io.BytesIO()
    excel_file.save(output_stream)
    output_stream.seek(0)  # Reset stream position for uploading

    #generating sas url
    print("generating Excel URL....")
    excel_url = upload_file_to_blob(
                                file_name=file_path,
                                output_stream=output_stream,
                                user_id=user_id,
                                id=id
                                )
    

    suggested_pills= getSuggestedPills(user_id)

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "timeline",
                        "tool_name": "timeline",
                        "pills":suggested_pills,
                        "table": tabular_data,
                        "url": {
                            "excel_url" : excel_url,
                        },
                        "count_of_resources": resources
                    }
                )
