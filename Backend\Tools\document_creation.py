from datetime import datetime, <PERSON><PERSON><PERSON>
# from llm_chains import file_Template
from docxtpl import DocxTemplate
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
import io
import os
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, <PERSON>
from typing import List, Optional
from langchain_openai import AzureChatOpenAI
import json

from Config.azureAI import llm
from langchain_core.tools import tool
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import os
from dotenv import load_dotenv
load_dotenv()

os.environ["AZURE_OPENAI_API_KEY"] = os.getenv("AZURE_OPENAI_API_KEY")


# llm = AzureChatOpenAI(
#     azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
#     azure_deployment=os.getenv("OPEN_AI_MODEL_NAME"),
#     openai_api_version=os.getenv("AZURE_API_VERSION"),
# )
# print("AzureChatOpenAI Initilized...")


# Pydantic
class fileFormat(BaseModel):
    """"Statement of work document prompt"""

    Document_Title: str = Field(description = "Provide document title with name of Vendor(Your company) and Customer")
    Customer_Segment: str = Field(description = "Provide me the customer name with business segmentation in following format [Customer Name]")
    Vendor_Segment : str = Field(description = "Provide me the customer name with business segmentation in following format  [Vendor Name], [Vendor Company Name]")
    Agreement_Date : str = Field(description = "Date of today or when agreement is being set. ")
    Introduction : str = Field(description = "Provide brief information about the customer company, and how vendor will be delivering the D365FO modules.")
    Purpose : str = Field(description = "Outline the purpose of the document, for example, to define the scope and estimates for adding new functionality to an existing system, such as modyfing Sales order form.")
    Inscope_work : str = Field(description = "Give me a brief and long information in paragraph followed by bullet points, List the features or deliverables. For example: Program logic to create work headers and work lines based on item locations. New fields or menu items for tracking Last Counted Date and other relevant information.")
    Outscope_Work : str = Field(description = "Identify any areas that are not covered, such as unrelated system functionalities.")    
    Project_Duration : str = Field(description = "Give me a project duration in weeks and total number of hours along with the breakdown of requirement gathering, solution development, testing, deployment, and user training in bullets points")
    Resource_Costing : str = Field(description = "Provide a detailed breakdown of resource allocation and cost per role in bullets points, for example: Project Manager: $[Hourly Rate] x [Hours] = $[Total Cost], Soultion Architect: $[Hourly Rate] x [Hours] = $[Total Cost], Developer: $[Hourly Rate] x [Hours] = $[Total Cost], Functional consultant: $[Hourly Rate] x [Hours] = $[Total Cost], Trainer: $[Hourly Rate] x [Hours] = $[Total Cost], Also give me Total estimated cost: $[Total project cost] ")
    Deliverable_Acceptance_Process : str = Field(description = "Outline the acceptance and rejection process for the deliverables, detailing how the Customer will review and approve the work.")
    Signature_Block : str = Field(description = "Include signature lines for both parties, such as: Customer Name: [Customer Representative Name], [Title] & Vendor Name: [Vendor Representative Name], [Title]")
    

file_Template = ChatPromptTemplate.from_messages(
    [
        ("system", "You are a D365 FO solution architect at Quisitive.inc, based on the provided information you need to provide me data that will help me creating the contract agreement and statement of work that will be sent to customer for the implementation sales pitch for the modules asked by the customer."),
        ("human", "{input}")
    ]
)

file_Template = file_Template | llm.with_structured_output(fileFormat)

dictionary = {
    "company_name": "Tesla, Inc.",
    "company_specialization": "Electric vehicles and sustainable energy solutions",
    "company_location": "Palo Alto, California",
    "founded_date": "2003",
    "founder_name": [
        "Elon Musk",
        "JB Straubel",
        "Martin Eberhard",
        "Marc Tarpenning",
        "Ian Wright"
    ],
    "headquarters_location": "Palo Alto, California",
    "products_or_services": [
        "Electric Vehicles (Model S, Model 3, Model X, Model Y, Roadster, Cybertruck)",
        "Energy Storage Solutions (Powerwall, Powerpack, Megapack)",
        "Solar Products (Solar Roof, Solar Panels)"
    ],
    "industry": "Automotive and Energy",
    "specialization": "Sustainable energy and electric vehicles",
    "parent_company": "",
    "subsidiaries": [],
    "business_units": "",
    "key_milestones": [
        "2003: Founded",
        "2008: Launch of the first Roadster",
        "2012: Launch of Model S",
        "2015: Introduction of Powerwall",
        "2017: Launch of Model 3",
        "2019: Announcement of Cybertruck"
    ]
}

str_services = str(", " .join(dictionary["products_or_services"]))

str_prompt = f"We need to write an SOW document for the company {dictionary['company_name']}, and the company has services in {str_services}, Customer wants to implement D365 Finance and operations for AP, AR and HR modules."

@tool(return_direct=True)
def create_document(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]) -> str:
    """This tool is use to generate the document and wil return you the url to dowload the document"""
    # print(str_prompt)
    response = file_Template.invoke({
                                "input": str_prompt
                            })
    response = dict(response)

    print(response)

    blob_service_client = BlobServiceClient.from_connection_string(os.getenv("Blob_connection_string"))

    # Step 1: Download the DOCX template from Blob Storage
    input_blob_client = blob_service_client.get_blob_client(container = os.getenv("Blob_input_container_name"), blob = "template.docx")
    download_stream = input_blob_client.download_blob()
    template_bytes = download_stream.readall()

    # Load the downloaded template into DocxTemplate
    doc = DocxTemplate(io.BytesIO(template_bytes))
    doc.render(response)  # Render the data into the document

    # Save the rendered document to an in-memory stream
    output_stream = io.BytesIO()
    doc.save(output_stream)
    output_stream.seek(0)  # Reset stream position for uploading

    # Step 2: Upload the generated file back to Blob Storage
    output_blob_client = blob_service_client.get_blob_client(container = os.getenv("Blob_output_container_name"), blob = f"{dictionary['company_name']}-SOW.docx")
    output_blob_client.upload_blob(output_stream, overwrite = True)

    print("Template processed and uploaded successfully.")

    container_name = os.getenv("Blob_output_container_name")
    blob_name      = f"{dictionary['company_name']}-SOW.docx"  # Construct the full path for the blob in the storage container
    print(f"Generating SAS URL for {blob_name}...")

    # Create a new BlobServiceClient
    blob_service_client = BlobServiceClient.from_connection_string(os.getenv("Blob_connection_string"))

    # Generate the SAS token for secure access to the blob
    sas_token = generate_blob_sas(
        account_name = blob_service_client.account_name,
        container_name = container_name,
        blob_name = blob_name,
        account_key = os.environ['BLOB_ACCOUNT_KEY'],  # Use the account key to sign the SAS token
        permission = BlobSasPermissions(read = True),  # Allow read access only
        expiry = datetime.now() + timedelta(hours = 200),  # Token expires in 2 hour
        protocol = 'https',  # Enforce HTTPS for secure access
        version = '2022-11-02'
    )

    # Construct the full URL for accessing the blob with the SAS token
    blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{container_name}/{blob_name}"
    sas_url = f"{blob_url}?{sas_token}"

    print(sas_url)
    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "documentcreation",
                        "url": sas_url
                        }
                    )