import streamlit as st
import streamlit.components.v1 as components
from streamlit_extras.app_logo import add_logo

import requests


st.set_page_config(page_title="🦙💬 Prompt Edge Assistant ")

components.html(f'<div style="color:#3399FF;font-size:36px; display:flex; gap:1rem"><h1 style="font-size:4rem;color:rgb(190, 106, 212)">Prompt Edge Assistant </h1></div>', width=None, height=227, scrolling=False)


def clear_chat_history():
    st.session_state.messages = [{"role": "assistant", "content": "How may I assist you today?"}]

if "messages" not in st.session_state.keys():
    st.session_state.messages = [{"role": "assistant", "content": "How may I assist you today?"}]

def display_chat_messages():
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.write(message["content"])



def get_chat_response(user_input):
    # Example of sending a POST request to the API with user input
    try:
        response = requests.post(
            url = "http://localhost:7071/api/conversationStart",
            headers = {'user_id': "7777"},
            json={"user_id": "testingUser123",  "user_input": user_input},  # Adjust payload as needed
            timeout=120  # Optional: set a timeout for the request
        )
        
        response.raise_for_status()  # Raises an error if the request failed
        
        # Assuming API returns a JSON object with the response text in 'message' key

        return response.json()
    except requests.RequestException as e:
        st.error(f"Error: {e}")
        return "Sorry, I'm having trouble connecting to the server."



def main(initial):
    with st.sidebar:
        st.sidebar.image(".\images\promt-edge-logo.png", use_column_width=True)   
   
    prompt = st.chat_input("Type your message here")
    # user_input = (f"{prompt}. Give details like What is the company type, its major clients, product type, email id, phone number, Number of employees, company location and address, company revenue, main products, buisness module, headquarters address, subsidiaries, industry name and type, SIC_Code, its linkedin account link, website link and any other information avaiable.")

    if prompt:
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                
                output = get_chat_response(prompt)

                placeholder = st.empty()
                full_response = ''

                full_response = output
                  
                placeholder.markdown(full_response)
        # st.session_state.messages.append({"role": "user", "content": prompt})
        message = {"role": "assistant", "content": full_response}
        st.session_state.messages.append(message)
             
    display_chat_messages()


if __name__ == "__main__":
    main(0)