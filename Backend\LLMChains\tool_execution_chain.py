from copy import deepcopy
from langchain_core.runnables import chain
from Agents.proposalCreation_Agent2 import proposalCreation_llm_with_tools, proposalCreation_tools

user_id, id = '', ''

@chain
def inject_user_id(ai_msg):
    tool_calls = []
    for tool_call in ai_msg.tool_calls:
        tool_call_copy = deepcopy(tool_call)
        tool_call_copy["args"]["user_id"] = user_id
        tool_call_copy["args"]["id"] = id
        tool_calls.append(tool_call_copy)
    return tool_calls

tool_map = {tool.name: tool for tool in proposalCreation_tools}


@chain
def tool_router(tool_call):
    return tool_map[tool_call["name"]]


proposalCreationToolChain = proposalCreation_llm_with_tools | inject_user_id | tool_router.map()