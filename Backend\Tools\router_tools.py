from langchain_core.tools import tool
from langchain_core.tools import InjectedToolArg
from Agents.proposal_Agent import proposal_agent_llm_with_tools
from Orchestration.ProposalAgentOrchestration import executeProposalAgent
from typing_extensions import Annotated
@tool(return_direct = True)
def route_to_company_agent(user_query:str, user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """Route the query to the Company Agent."""
    response = "I am Company"
    #response = company_agent_executor.invoke({"input": user_query})
    print(response)
    return response

@tool(return_direct = True)
def route_to_proposal_agent(user_query:str, user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """Route the query to the Proposal Agent."""
    print("i am in proposal")
    response = executeProposalAgent(query=user_query, id=id, user_id=user_id)
    #response = proposal_agent_executor.invoke({"input": user_query})
    print("i am out proposal")
    return response
    

router_tools = [route_to_proposal_agent]
