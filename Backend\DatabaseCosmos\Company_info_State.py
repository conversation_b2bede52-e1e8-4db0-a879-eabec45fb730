import logging
import random
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from Config.cosmosdb import database
from utilities.CreateGuid import generate_guide
# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="CompanyInfoState",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)  

def insert_Companyinfo_State(chat_id,user_id, company_name,company_information,industry,SIC_code,type_of_manufacturing,headquarters_address,employees,corporate_structure,subsidiaries_and_brands):
    
        try:
            guid=generate_guide()
            item = {
                'id': guid,
                'user_id': user_id,
                'chat_id':chat_id,
                'createdAt': datetime.utcnow().isoformat(),
                'companyname':company_name,
                'companyinformation': company_information,
                'SIC_Code':SIC_code,
                'industry': industry,
                'type_of_manufacturing':type_of_manufacturing,
                'headquarters_address': headquarters_address,
                'employees': employees,
                'corporate_structure': corporate_structure,
                'subsidiaries_and_brands': subsidiaries_and_brands
            
        }
            container.create_item(body=item)
            return item
        except Exception as e:
         print("Error" ,e)
        
         return None  

def read_CompanyInfo(chat_id):
    print("reading companyINFO")
    try:
        logging.info('Applying query.')
        query = f"SELECT * FROM c WHERE c.chat_id = '{chat_id}'"
        
        item= container.query_items(query = query, enable_cross_partition_query = True)
        
        print(f"Retrieved (query) .",item)

        return list(item)[0]
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
    
        return None

def getAllByUserID(user_id):
    query = f"SELECT * FROM c WHERE c.user_id = '{user_id}'"
        
    items = list(container.query_items(query=query, enable_cross_partition_query=True))

    return items
        
        



def updateCompanyInfo(chat_id,user_id, company_name,company_information,industry,SIC_code,type_of_manufacturing,headquarters_address,employees,corporate_structure,subsidiaries_and_brands):
    
        print("checking row exist or not!")
        query = f"SELECT * FROM c WHERE c.chat_id = '{chat_id}'"
        
        items = list(container.query_items(query=query, enable_cross_partition_query=True))
        
        
        if items:
           
            existing_item = items[0]
            
            existing_item.update({
            'user_id': user_id,
            'chat_id': chat_id,
            'createdAt': datetime.utcnow().isoformat(),
            'companyname': company_name,
            'companyinformation': company_information,
            'SIC_Code': SIC_code,
            'industry': industry,
            'type_of_manufacturing': type_of_manufacturing,
            'headquarters_address': headquarters_address,
            'employees': employees,
            'corporate_structure': corporate_structure,
            'subsidiaries_and_brands': subsidiaries_and_brands
            })

            #Replace the item in the container with the updated fields
            
            print("data updated")
            print(items)
            print(container)
            
            response=container.replace_item(item=existing_item, body=existing_item)
            if response:
                return existing_item

        else:
            
            return (f"No Company record found for the given chat ID: {chat_id}")
    