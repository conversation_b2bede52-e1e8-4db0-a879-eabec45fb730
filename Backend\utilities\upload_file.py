import os

from Config.blobService import blob_service_client
from azure.storage.blob import generate_blob_sas, BlobSasPermissions

from datetime import datetime, timedelta
from dotenv import load_dotenv
load_dotenv()


container_name = os.getenv("Blob_output_container_name")


def upload_file_to_blob(file_name:str, output_stream,user_id:str, id:str):
    
    blob_name=f"{user_id}/{id}/{file_name}"

    blob_client = blob_service_client.get_blob_client(
                                                    container = container_name, 
                                                    blob = blob_name
                                                )
    output = blob_client.upload_blob(output_stream, overwrite = True)
    print(f"File {blob_name} uploaded...", output)

    ################################33 Remember to add check here for file upload completion

    # Generate the SAS token for secure access to the blob
    sas_token = generate_blob_sas(
        account_name = blob_service_client.account_name,
        container_name = container_name,
        blob_name = blob_name,
        account_key = os.environ['BLOB_ACCOUNT_KEY'],  # Use the account key to sign the SAS token
        permission = BlobSasPermissions(read = True),  # Allow read access only
        expiry = datetime.now() + timedelta(hours = 400),  # Token expires in 4 hour
        protocol = 'https',  # Enforce HTTPS for secure access
        version = '2022-11-02'
    )

    # Construct the full URL for accessing the blob with the SAS token
    blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{container_name}/{blob_name}"
    sas_url = f"{blob_url}?{sas_token}"

    return sas_url


def generate_file(user_id, id, file_name):
    
    try:
        
        blob_name = f"{user_id}/{id}/{file_name}"
        
        blob_client = blob_service_client.get_blob_client(
                                                container = container_name, 
                                                blob = blob_name
                                            )
        
    
        sas_token = generate_blob_sas(
            account_name = blob_client.account_name,
            container_name = container_name,
            blob_name = blob_name,
            account_key = os.environ['BLOB_ACCOUNT_KEY'],  # Use the account key to sign the SAS token
            permission = BlobSasPermissions(read = True),  # Allow read access only
            expiry = datetime.now() + timedelta(hours = 400),  # Token expires in 4 hour
            protocol = 'https',  # Enforce HTTPS for secure access
            version = '2022-11-02'
        )

        # Full SAS URL
        blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{container_name}/{blob_name}"
        sas_url = f"{blob_url}?{sas_token}"

        if(sas_url):
            print("SAS URL:", sas_url)
            return sas_url
        else:
            return None
    except:
        None
    
    
    return sas_url