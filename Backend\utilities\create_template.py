from Config.azureAI import llm
from langchain_core.prompts import Chat<PERSON>romptTemplate

from pptx import Presentation
from pptx.util import Pt, Inches
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.text import MSO_ANCHOR

from utilities.download_template import download_template_from_blob
from DatabaseCosmos.Template import insert_Template


context_extraction_template = """
I am providing you an extracting text template from a PPT as 'context'
You have to make a system prompt that I would pass to another LLM and that LLM will create the text according to the flow given in the PPT.
Take the context just as a template, don't mention the details in it
Remember to generate Title of the Slide according to Template
Generate 'Content Overview' as template like Table, Text for each slide, don't mention Image

You have to analyze the given 'context' and Remember to create a precise and to the point system prompt from it, as a PPT generation template

for example: ''' 
Phase 1 : Project Overview
Title: Microsoft Dynamics 365 Finance and Operations Implementation
Content Overview: Brief summary of project goals, objectives, and expected outcomes.

Phase 2 : Executive Summary
Title: Executive Summary
Content Overview: Brief Executive summary of the company.

continue...
'''
"""

context_extraction_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", context_extraction_template),
                ("human", "Context: '''{context}'''")
            ]
        )

# Create a LangChain chain to use the template
context_extraction_chain = context_extraction_prompt | llm

def createPPTtemplate(blob_name:str, user_id:str):

    print("Downloading the template....")
    template_bytes = download_template_from_blob(blob_name=blob_name)
    presentation = Presentation(template_bytes)

    print("Extracting Details from PPT....")
    slides_data = extract_slides_data(presentation)
    slide_details_text = format_slides_data(slides_data)

    print("Generating System Prompt for PPT....")
    ppt_system_prompt = context_extraction_chain.invoke({
                    "context": slide_details_text,
                    })
    
    db_response = insert_Template(
        user_id=user_id,
        template=str(ppt_system_prompt.content)
    )
    
    # print("PPT System propmpt...",ppt_system_prompt.content)
    return db_response



######################################### Data Extraction Functions.....

## Function to get RGB color as a hex string
def get_rgb_color(color):
    if color and hasattr(color, "rgb") and isinstance(color.rgb, RGBColor):
        return f"#{color.rgb[0]:02X}{color.rgb[1]:02X}{color.rgb[2]:02X}"
    return "N/A"

# Function to determine slide type
def determine_slide_type(slide):
    slide_type = []
    if any(shape for shape in slide.shapes if shape.is_placeholder and shape.placeholder_format.type == 1):
        slide_type.append("Title Slide")
    if any(shape for shape in slide.shapes if shape.has_text_frame):
        slide_type.append("Text Slide")
    if any(shape for shape in slide.shapes if shape.shape_type == 13):  # Picture shape type
        slide_type.append("Image Slide")
    if any(shape for shape in slide.shapes if shape.has_table):
        slide_type.append("Table Slide")
    return slide_type

def extract_slides_data(presentation):
    # Extract detailed information from each slide
    slides_data = []
    for slide_idx, slide in enumerate(presentation.slides, start=1):
        slide_info = {
            "SlideNumber": slide_idx,
            "SlideType": determine_slide_type(slide),
            "TextContent": [],
            "Summary": "",
        }
        text_content = []
        for shape in slide.shapes:
            if shape.has_text_frame:
                for paragraph in shape.text_frame.paragraphs:
                    # paragraph_data = []
                    paragraph_data = ""
                    for run in paragraph.runs:
                        paragraph_data = {
                            "Text": run.text,
                            "FontSize": run.font.size.pt if run.font.size else "Default",
                            "FontColor": get_rgb_color(run.font.color),
                            "Bold": run.font.bold if run.font.bold is not None else "Default",
                            "Italic": run.font.italic if run.font.italic is not None else "Default",
                        }
                        # paragraph_data.append(run_data)
                        # paragraph_data = run_data
                    if paragraph_data:
                        text_content.append(paragraph_data)
    
        slide_info["TextContent"] = text_content
        slide_info["Summary"] = (
            f"Slide {slide_idx} is a '{', '.join(slide_info['SlideType'])}' with {len(text_content)} paragraphs."
        )
        slides_data.append(slide_info)
        
    return slides_data

def format_slides_data(slides_data) -> str:
    slide_details_text = ""
    for slide_data_object in slides_data:
        text = f"""

                Slide# {slide_data_object['SlideNumber']}:
                Summary: {slide_data_object['Summary']}

                Texts includes:
                """
        for obj in slide_data_object['TextContent']:
            text += f"\n - {obj['Text']}"
            text = text.replace("  ", "")

        slide_details_text += text
    return slide_details_text