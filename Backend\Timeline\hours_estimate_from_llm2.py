import json
import pandas as pd
from typing import Dict, List, Tuple, Any
from openai import OpenAI
from Config.azureAI import client
from utilities.fetchChat import fetchChatSummary
from Config.blobService import blob_service_client
import io
import os
# Define system prompts as constants
INTEGRATION_SYSTEM_PROMPT = """You are a Dynamics 365 Integration expert. Your task is to provide the build hours of every single Integration Point from the user's list.

You will receive two lists of dictionaries:
1. 'Integration from user': Contains dictionaries with 'Integration Point', 'Integration Description', and 'Value' keys.
2. 'Integration from benchmark': Contains dictionaries with 'Hours' (in ranges like "120 - 180") and 'Integration' keys.

Instructions:
- For each Integration Point in the user list, find the most appropriate match in the benchmark list.
- Consider both the Integration Point name AND the Value (vendor/system name) when finding matches.
- When a match is found, convert the hour range (e.g., "120 - 180") to a single integer. Use the middle of the range when possible.
- The match doesn't need to be exact - use your expertise to find the closest relevant match based on functionality and complexity.
- Your response must be a valid JSON object with Integration Points as keys and hour estimates as integer values.
- If an Integration Point has no reasonable match in the benchmark, assign it 0 hours.

Example input:
User: [{"Integration Point": "Warehouse Management", "Value": "Blue Yonder"}]
Benchmark: [{"Hours": "150 - 200", "Integration": "Warehouse Management Systems (Blue Yonder, Manhattan)"}]

Example response:
{"Warehouse Management": 175}
"""

MIGRATION_SYSTEM_PROMPT = """You are a Dynamics 365 Data Migration expert. Your task is to provide the build hours for each Migration Category based on both the category type and volume/complexity.

You will receive two lists of dictionaries:
1. 'Migration from user': Contains dictionaries with 'Category', 'Description', and 'Value' keys, where 'Value' typically indicates volume or complexity.
2. 'Migration from benchmark': Contains dictionaries with 'Entity', 'Hours' (in ranges like "08 to 16"), and 'Volume' keys.

Instructions:
- For each Category in the user list, find the most appropriate match in the benchmark list.
- You MUST consider BOTH the Category name AND the Value (volume/complexity) when determining the appropriate hours.
- First match the Category with the Entity in the benchmark.
- Then check if the Value (volume) from the user falls within the Volume range in the benchmark.
- When a match is found, convert the hour range (e.g., "08 to 16") to a single integer. Use the middle of the range when possible.
- For volume ranges, use your expertise to determine where the user's volume falls.
- Your response must be a valid JSON object with Categories as keys and hour estimates as integer values.
- If a Category has no reasonable match in the benchmark, assign it 0 hours.

Example input:
User: [{"Category": "Customer Master Data", "Value": "Over 1,000 customers"}]
Benchmark: [{"Entity": "Customers", "Hours": "08 to 16", "Volume": "< 10,000"}]

Example response:
{"Customer Master Data": 12}
"""

MODULE_SYSTEM_PROMPT = """You are a Dynamics 365 Module Implementation expert. Your task is to provide the build hours for each Module Category in the user's list.

You will receive two lists of dictionaries and a dictionary:
1. 'Modules from user': Contains dictionaries with 'Category' and 'Training Description' keys.
2. 'Modules from benchmark': Contains dictionaries with 'Modules', 'Complexity' and 'hours' keys.
3. 'Company': Contains the company information which you can utilize to suggest complexity for the modules by utilizing information provided.

Instructions:
- For each category in the user list, assign it a Complexity (Low, Medium, High) based on the training description, and also consider the company, industry it belongs to as well as info related to it.   
- For each Category in the user's list and the newly assigned complexity, find the most appropriate match in the benchmark list.
- Match based on the module name and complexity.
- When a match is found, get the hours of that.
- The match doesn't need to be exact - use your expertise with Dynamics 365 to find the closest relevant match based on module functionality.
- Your response must be a valid JSON object with the exact Category names as keys and hour estimates as integer values.
- If a Category has no reasonable match in the benchmark, as per your judged complexity of module from user assign in this manner(Low:200 , Medium:400 , High:600).

Example input:
User: [{"Category": "Accounts receivable", "Training Description": "Manages money owed by customers."}]
Benchmark: [{"Modules": "Accounts Receivable", "Complexity":"Low", "hours": "150 - 200"}]
Company: {"companyinformation": "Quisitive", "companyname": "Quisitive", "industry": "IT Services"}

Example response:
{"Accounts receivable": 175 }
"""


class D365EstimationService:
    """
    Service for estimating Dynamics 365 implementation hours based on benchmarks.
    """
    
    def __init__(self):
        """
        Initialize the service with an API client for model access and optionally load benchmark data.
        
        Args:
            benchmark_file_path: Optional path to the Excel file containing benchmark data
        """
        self.client = client
        
    
    def load_benchmark_data(self) :
        """
        Load benchmark data from Excel file.
        
        Args:
            file_path: Path to the Excel file containing benchmark data
        """
        container_name = os.getenv("Blob_input_container_name")
        container_client = blob_service_client.get_container_client(container_name)
        folder_path = "Hours Benchmarks/admin/" 

        blobs = list(container_client.list_blobs(name_starts_with=folder_path))
        file = blobs[0]
        blob_client = container_client.get_blob_client(file.name)
        blob_data = blob_client.download_blob().readall()
        # Read the Excel file
        xls = pd.ExcelFile(io.BytesIO(blob_data), engine="openpyxl")

        # Read specific sheets
        df_modules = pd.read_excel(xls, sheet_name='Modules')
        df_integration = pd.read_excel(xls, sheet_name='Integration')
        df_data_migration = pd.read_excel(xls, sheet_name='DataMigration')
        
        # Clean up integration data if needed
        if 'Entity Examples' in df_integration.columns and 'Complexity' in df_integration.columns:
            df_integration = df_integration.drop(['Entity Examples', 'Complexity'], axis=1)
        
        # Convert dataframes to dictionaries
        modules_dict = df_modules.to_dict(orient='records')
        dataMigration_dict = df_data_migration.to_dict(orient='records')
        integration_dict = df_integration.to_dict(orient='records')
        
        return modules_dict, dataMigration_dict, integration_dict
       
    
    def extract_data(self, chat_id: str) -> Tuple[Dict, List, List, List]:
        """
        Extract data from chat history using the chat ID.
        
        Args:
            chat_id: The ID of the chat to extract data from
            
        Returns:
            Tuple of (company_info, data_migration, integration, modules)
        """
        chat_history = fetchChatSummary(
            id=chat_id,
            getChatHistory=False,
            returnRaw=True
        )
        
        # Initialize dictionaries and lists
       
        data_migration = []
        integration = []
        modules = []
        
        
        # Extract data migration information
        if "datamigration" in chat_history:
            data_migration = chat_history["datamigration"]
            print("Data migration" , data_migration)
        
        # Extract integration information
        if "integration" in chat_history:
            integration = chat_history["integration"]
            print("Integration" , integration)
        
        # Extract modules information
        if "modules" in chat_history:
            modules = chat_history["modules"]
            print("Modules" , modules)
        
        return data_migration, integration, modules
    
    def _process_json_data(self, data: Dict) -> Dict:
        """
        Process JSON data to calculate total hours and average, 
        then assign the average (as integer) to keys with zero values.
        
        Args:
            data (dict): JSON data with hour values
            
        Returns:
            dict: Updated data with averages assigned to zero values
        """
        # Create a copy of the input data to avoid modifying the original
        result = data.copy()
        
        # Initialize counters
        total_hours = 0
        count = 0
        keys_with_zero = []
        
        # Calculate total hours and count valid entries
        for key, value in data.items():
            if isinstance(value, (int, float)):
                if value == 0:
                    keys_with_zero.append(key)
                else:
                    total_hours += value
                    count += 1
        
        # Calculate average if there are valid entries
        if count > 0:
            # Calculate integer average (rounded down)
            average_hours = int(total_hours / count)
            
            # Assign average to keys with zero values
            for key in keys_with_zero:
                result[key] = average_hours
        
        return result
    
    def _get_estimate_from_model(self, system_prompt: str, user_data: List[Dict], benchmark_data: List[Dict], company: Dict, prompt_format: str) -> Dict:
        """
        Generic method to get estimates from the language model.
        
        Args:
            system_prompt: The system prompt for the specific estimation type
            user_data: The user's data for estimation
            benchmark_data: The benchmark data for comparison
            prompt_format: The format string for the human prompt
            
        Returns:
            Dictionary with estimated hours
        """
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt_format.format(
                json.dumps(user_data), 
                json.dumps(benchmark_data),
                json.dumps(company)
            )}
        ]
        
        response = self.client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            temperature=0.1,
            max_tokens=300,
            response_format={"type": "json_object"}
        )
        
        content = response.choices[0].message.content.strip()
        return json.loads(content)

    def get_integration_hours(self, user_integrations: List[Dict], benchmark_integrations: List[Dict] , company: Dict) -> Dict:
        """
        Get estimated hours for integration points.
        
        Args:
            user_integrations: User's integration data
            benchmark_integrations: Optional benchmark integration data. 
                If None, uses the class's stored benchmark data.
            
        Returns:
            Dictionary with integration points as keys and hour estimates as values
        """
        
            
        hours = self._get_estimate_from_model(
            INTEGRATION_SYSTEM_PROMPT,
            user_integrations,
            benchmark_integrations,
            company,
            "Integration from user: {} Integration from benchmark: {}"
        )
        
        # Process the data to handle zero values
        processed_hours = self._process_json_data(hours)
        return processed_hours
    
    def get_module_hours(self, user_modules: List[Dict], benchmark_modules: List[Dict] = None, company: List[Dict] = None) -> Dict:
        """
        Get estimated hours for modules.
        
        Args:
            user_modules: User's module data
            benchmark_modules: Optional benchmark module data.
                If None, uses the class's stored benchmark data.
            
        Returns:
            Dictionary with module categories as keys and hour estimates as values
        """
        
            
        hours = self._get_estimate_from_model(
            MODULE_SYSTEM_PROMPT,
            user_modules,
            benchmark_modules,
            company,
            "Modules from user: {} Modules from benchmark: {}, company information: {}"
        )
        
        # Process the data to handle zero values
        processed_hours = self._process_json_data(hours)
        return processed_hours
    
    def get_migration_hours(self, user_migrations: List[Dict], benchmark_migrations: List[Dict], company: Dict) -> Dict:
        """
        Get estimated hours for data migration.
        
        Args:
            user_migrations: User's migration data
            benchmark_migrations: Optional benchmark migration data.
                If None, uses the class's stored benchmark data.
            
        Returns:
            Dictionary with migration categories as keys and hour estimates as values
        """
        
            
        hours = self._get_estimate_from_model(
            MIGRATION_SYSTEM_PROMPT,
            user_migrations,
            benchmark_migrations,
            company,
            "Migration from user: {} Migration from benchmark: {}"
        )
        
        # Process the data to handle zero values
        processed_hours = self._process_json_data(hours)
        return processed_hours

    def calculate_total_hours(self, integration_hours: Dict, module_hours: Dict, migration_hours: Dict) -> Dict:
        """
        Calculate total estimated hours across all categories.
        
        Args:
            integration_hours: Dictionary of integration hours
            module_hours: Dictionary of module hours
            migration_hours: Dictionary of migration hours
            
        Returns:
            Dictionary with total hours and breakdown
        """
        integration_total = sum(value for key, value in integration_hours.items())
        module_total = sum(value for key, value in module_hours.items())
        migration_total = sum(value for key, value in migration_hours.items())
        
        total = integration_total + module_total + migration_total
        print("Calculated Hours: ")
        print({
            "total_hours": total,
            "breakdown": {
                "integration": integration_total,
                "modules": module_total,
                "data_migration": migration_total
            },
            "details": {
                "integration": integration_hours,
                "modules": module_hours,
                "data_migration": migration_hours
            }
        })
        return total

    
    def estimate_from_chat_id(self , modules , integration , data_migration, company):
        """
        Estimate D365 implementation hours from direct data inputs.
        
        Args:
            data_migration: List of data migration items
            integration: List of integration items
            modules: List of module items
            
        Returns:
            Dictionary with total hours and breakdown
        """
        # Ensure benchmark data is loaded
        #data_migration , integration , modules = self.extract_data(chat_id)
        modules_dict , dataMigration_dict , integration_dict = self.load_benchmark_data()
        # Get hour estimates
        # Initialize dictionaries with default empty values
        integration_hours = {}
        module_hours = {}
        migration_hours = {}
        
        # Only call estimation functions if corresponding data exists
        if integration and integration_dict:
            integration_hours = self.get_integration_hours(integration, integration_dict, {})
        
        if modules and modules_dict:
            module_hours = self.get_module_hours(modules, modules_dict , company)
        
        if data_migration and dataMigration_dict:
            migration_hours = self.get_migration_hours(data_migration, dataMigration_dict, {})
        
        # Calculate total hours
        build_hours = self.calculate_total_hours(integration_hours, module_hours, migration_hours)
        return build_hours
    

