
from DocumentGeneration.Document import DocumentGenerator_base_template , DocumentGenerator_user_template
import os
from DatabaseCosmos.Theme_template_upload import read_Template_FileName
from Config.blobService import blob_service_client
from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from datetime import datetime, timedelta

from static_data import project_scope,environments_and_installation,out_of_scope,key_project_servie_deliverables, project_governace,fees_signing
from utilities.parse_to_json import parse_to_json_function,parse_to_json_function_doc
from LLMChains.document_static_data_prompts import introduction_data,project_scope_data1,executive_summary ,project_approch_data,fees_table_data, project_timeline_data #,project_scope_data2

from langchain_core.tools import tool
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated

from utilities.fetchChat import fetchChatSummary
from DatabaseCosmos.Company_info_State import read_CompanyInfo
from utilities.upload_file import upload_file_to_blob
from Tools.suggestPills import getSuggestedPills
from DatabaseCosmos.ProjectEstimation import read_ProjectEstimation_State
from DatabaseCosmos.Timeline import GetTimeline
from utilities.create_timeline_chart2 import create_chart

from docx.shared import Inches
import io
import json
from datetime import date
import pandas as pd
from PIL import Image

def get_sas_url_for_document(file_name):
    
    blob_name=f"{file_name}"

    ################################33 Remember to add check here for file upload completion
    
    container_name = os.getenv("Blob_output_container_name")

    # Generate the SAS token for secure access to the blob
    sas_token = generate_blob_sas(
        account_name = blob_service_client.account_name,
        container_name = container_name,
        blob_name = blob_name,
        account_key = os.environ['BLOB_ACCOUNT_KEY'],  # Use the account key to sign the SAS token
        permission = BlobSasPermissions(read = True),  # Allow read access only
        expiry = datetime.now() + timedelta(hours = 400),  # Token expires in 4 hour
        protocol = 'https',  # Enforce HTTPS for secure access
        version = '2022-11-02'
    )

    # Construct the full URL for accessing the blob with the SAS token
    blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{container_name}/{blob_name}"
    sas_url = f"{blob_url}?{sas_token}"
    return sas_url

  
def fees_rates_per_hour_estimation(user_id,id):
    
    container_name = os.getenv("Blob_output_container_name")
    container_client = blob_service_client.get_container_client(container_name)
    folder_path = f"Resource_costing/"
    blob_list = container_client.list_blobs(name_starts_with=folder_path)
    blobs = list(blob_list)
    if not blobs:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "costEstimation",
            "errorkey": "ResourceCostingNotFound"
        })
   
    file_blob = blobs[0]
    blob_client = container_client.get_blob_client(file_blob.name)
    download_stream = blob_client.download_blob()
    file_content = download_stream.readall()
    resource_costing_df = pd.read_csv(io.BytesIO(file_content))
    
    return resource_costing_df


def add_image_to_doc(image_data, width_in_inches, height_in_inches, dpi=150):
    """
    Adjust the image size based on given dimensions (in inches) and return the resized image as a BytesIO stream.

    :param image_data: The image in bytes format.
    :param width_in_inches: Desired width in inches.
    :param height_in_inches: Desired height in inches.
    :param dpi: The DPI (dots per inch) setting for the image (default: 300).
    :return: BytesIO stream of the resized image.
    """
    # Load the image from the bytes data
    image_stream = io.BytesIO(image_data)
    image = Image.open(image_stream)

    # Calculate pixel dimensions based on inches and DPI
    width_in_pixels = int(width_in_inches * dpi)
    height_in_pixels = int(height_in_inches * dpi)

    # Resize the image
    resized_image = image.resize((width_in_pixels, height_in_pixels), Image.Resampling.LANCZOS)

    # Save the resized image to a BytesIO stream
    resized_stream = io.BytesIO()
    resized_image.save(resized_stream, format=image.format)
    resized_stream.seek(0)  # Reset stream position

    return resized_stream


def content_data_creation(intro_msg_summary, chat_summary,timeline_data,fees_estimation_data):

    content =[]

    # add introduction data in content
    introduction = introduction_data(intro_msg_summary)
    content.extend((parse_to_json_function_doc(introduction)))

    # # add executive summary data in content
    print("----------------------------- intro_msg_summary ", intro_msg_summary)
    executiveSummary = executive_summary(intro_msg_summary)
    content.extend((parse_to_json_function_doc(executiveSummary)))

    # add project scope data in content
    content.extend(project_scope)
    
    projectScope = project_scope_data1(chat_summary)
    scope_data = (parse_to_json_function_doc(projectScope))
    content.extend(scope_data)
    
    # projectScope2 = project_scope_data2(chat_summary)
    # scope_data2 = (parse_to_json_function_doc(projectScope2))
    # content.extend(scope_data2)
    
    content.extend(environments_and_installation)
    content.extend(out_of_scope)
    
    # # add project approach data in content
    projectApproch= project_approch_data(chat_summary)
    content.extend((parse_to_json_function_doc(projectApproch))) 
    
    # add timeline data in content
    if timeline_data:  
        projectTimeline = project_timeline_data(timeline_data['timeline'])
        content.extend((parse_to_json_function(projectTimeline)))
        image = create_chart(timeline_data['timeline'])
        
        timeline_image = add_image_to_doc(image, 3, 2.5)
        
        # add timeline image data in content
        content.extend([{
            "type": "image",
            "title": "Timeline",
            "url": timeline_image}])

    content.extend(key_project_servie_deliverables)
    content.extend(project_governace)
    feesTable_data = fees_table_data(fees_estimation_data)
    content.extend((parse_to_json_function_doc(feesTable_data)))
    content.extend(fees_signing)
    
    return content

def adding_image_data_to_doc(doc, placeholder, image, width=None, height=None):
    """
    Replace a placeholder in the document with an image.
    :param doc: The `Document` object.
    :param placeholder: The placeholder text to replace.
    :param image_path: The path to the image file.
    :param width: Optional width of the image (in inches).
    :param height: Optional height of the image (in inches).
    """
    
    for paragraph in doc.paragraphs:
        if placeholder in paragraph.text:
            # Clear the placeholder text
            paragraph.text = ""

            # Add the image to the same paragraph
            run = paragraph.add_run()
            
            # image = io.BytesIO(image_data)
            
            if width and height:
                run.add_picture(image, width=Inches(width), height=Inches(height))
            elif width:
                run.add_picture(image, width=Inches(width))
            elif height:
                run.add_picture(image, height=Inches(height))
            else:
                run.add_picture(image)
            break  # Exit after replacing the first matching placeholder


# Updated `createDynamicFlowPPT_Tool` function
@tool(return_direct=True)
def createDynamicFlowDocument_Tool(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool generates a detailed document when user asked to create or draft a document or word document and returns the document's URL."""
    
    # Fetch the chat summary
    intro_msg_summary = fetchChatSummary(id=id, getBP=False, getModules=False, getERP=False, getIntegration=False, getDM=False, getChatHistory=False)

    if intro_msg_summary == "":
        intro_msg_summary = "Want to create a detailed document for the company: "
    document_type = "company_name"
    company_info = read_CompanyInfo(id)
    if company_info:
        document_type = company_info['companyname']
    else:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name":"documentcreation",
            "errorkey": "companyNameNotFound"
        })
    
        
    print("Conversational History...", intro_msg_summary)



    msg_summary = fetchChatSummary(id=id)
    
    if msg_summary == "":
        msg_summary = "Want to create a detailed document for the company: "
        
    print("Conversational History...", msg_summary)
    
    # fetch timeline data
    
    # get timeline data and create image
    timelinedata = ""
    timelinedata = GetTimeline(id)
    
    #get project estimation data i.e costing.
    projectestimation_data = read_ProjectEstimation_State(id)
    
    rates_per_hour = "TBD"
    try:
        rates_per_hour = fees_rates_per_hour_estimation(user_id, id)
    except Exception as e:
        print(f"error in reading the resource costing file {e}")
        
    finally:
 
        fees_estimation_data = {
            "projectestimation": projectestimation_data,
            "rateperhour": rates_per_hour
        }
    

    document_data = content_data_creation(intro_msg_summary,msg_summary, timelinedata,fees_estimation_data)

    # theme_path = ".\\DocumentGeneration\\Document\\Themes\\Quisitive\\theme.json"
    theme_path = "./DocumentGeneration/Document/Themes/Quisitive/theme.json"
    file_name = read_Template_FileName("document")
    folder_path = f"ThemeTemplate/docFile/"
    blob_file_path = f"{folder_path}{file_name['blob_name']}"

    # default theme path. if no theme template is uploaded
    path = ".\\base\\base_templates_modified\\Mazik - TMS -  SOW Microsoft Dynamics 365 Field Service -Final.docx"

    # get the sas url for the document
    file_path = get_sas_url_for_document(blob_file_path)
    user_template= file_path
    
    content_values ={
        "Title": "Statement of Work (SOW)",
        "Subtitle": " ",
        "Abstract": document_type,
        "Date": str(date.today()),
        "Author": "John Doe",
        "Company": document_type,
        "Address": "",
        "Email": ""
    }       #".\\DocumentGeneration\\Document\\titlesDefination.json"

    if user_template != "":
        document_generator = DocumentGenerator_user_template(theme_path,content_values)
        doc = document_generator.generate_document_from_user_template(document_data , user_template)
    else:
        document_generator = DocumentGenerator_base_template(theme_path,content_values)
        document_generator.generate_document_from_base_template(document_data, path)
        

    # placeholder data containes the placeholder name, from title page which we need to replace with the dynamic data.
    username = "John"
    placeholder_data = {
        "#companyname#": document_type,
        "#username#": username,
        "#date#": str(date.today()),
    }
    
    # Replace placeholders in the document
    for paragraph in doc.paragraphs:
        for placeholder, replacement in placeholder_data.items():
            if placeholder in paragraph.text:
                paragraph.text = paragraph.text.replace(placeholder, replacement)
                
        
    for table in doc.tables:
            # Iterate over all rows in the table
            for row in table.rows:
                # Iterate over all cells in the row
                for cell in row.cells:
                    # Replace #companyname# with the actual company name in each cell
                    if '#companyname#' in cell.text:
                        cell.text = cell.text.replace('#companyname#', document_type)
                        
    projectapproch_image = "./Data/projectApproachImage.png"                    
    adding_image_data_to_doc(doc, "#ProjectApproachImagePlaceholder#", projectapproch_image)


        
        
    # # Save the document
    docx_file_path = f"{user_id}/{id}/{document_type}_document.docx"

    # Save the rendered document to an in-memory stream
    output_stream = io.BytesIO()
    doc.save(output_stream)
    output_stream.seek(0)  # Reset stream position for uploading
    # doc.save(file_path)

    #generating sas url
    print("generating SAS URL....")
    docx_url = upload_file_to_blob(
                                file_name=docx_file_path,
                                output_stream=output_stream,
                                user_id=user_id,
                                id= id            
                                )
    
    ppt_url = ""
    # get suggested pills 
    suggested_pills = getSuggestedPills(user_id)

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "documentcreation",
                        "tool_name":"documentcreation",
                        "url": {
                            "ppt_url" : ppt_url,
                            "docx_url": docx_url,
                        },
                        "pills" : suggested_pills
                    }
                )