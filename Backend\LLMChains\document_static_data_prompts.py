from Config.azureAI import llm
from langchain_core.prompts import ChatPromptTemplate
import datetime

from tenacity import retry, stop_after_attempt, wait_exponential


@retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=4, max=10))
def invoke_llm_with_retry(chain, context, input_text):
    """
    Invokes the LLM chain with retry logic to handle rate limits.
    """
    response = chain.invoke({
        "context": context,
        "date": str(datetime.datetime.now()),
        "input": input_text,
        "temperature": 0
    })
    return response.content




def introduction_data(msg_summary):

    intro_prompt = """
    You are the Microsoft Dynamics Finance and operation implementation expert with the role of solution architect You do pre sales work, 
    you understand the client requirement and scope and add it to the documentation, you are expert in planning and estimating the project 
    timeline based on the best practices of Microsoft Dynamics Finance and operation implementation. 
    
    Based on the information provided in the following message history, craft a concise and formal introduction section for the 
    Statement of Work (SOW) document. Ensure the tone is professional and suitable for inclusion in business documents. 
    
    company details:
    {context}
    

    Guidelines:
    1. Describe the specific SOW document, includes its relationship to any referenced agreements or parties involved.
    2. The company that is generating this SOW document is Quisitive and the company for whom the document is generated is client company, provided in context.
    do not add placeholder like ("Client Company") or ("Source Company") 
    3. Remeber to keep the introduction consice and no more than 100 - 150 words
    
    the output should be a JSON object with the following format:
        1) "type": "heading", "level": level of the heading 1, "text": "Introduction"
        2) "type": "paragraph", "title":"", "text": <text>
        
    Always enclose the whole JSON object in [] brackets and ensure the proper JSON structure output.
    
    """


    # Run LLM
    # intro = llm.invoke(prompt.format())
    intro_creation_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", intro_prompt),
                ("human", "{input}")
            ]
        )

    intro_chain = intro_creation_prompt | llm
    
    # intro = intro_chain.invoke({
    #     "context": msg_summary,
    #     "date": str(datetime.datetime.now()),
    #     "input": "write introduction of SOW document"
    # })
    # intro = intro.content
    
    intro = invoke_llm_with_retry(intro_chain,msg_summary,"Write introduction of SOW document")

    
    return intro
    
    
def executive_summary(msg_summary):
    #----------------------- Executive summary:  ---------------- 
    
    
    executive_summary_prompt= """
        You are the Microsoft Dynamics Finance and operation implementation expert with the role of solution architect You do pre sales work, 
        you understand the client requirement and scope and add it to the documentation, you are expert in planning and estimating the project 
        timeline based on the best practices of Microsoft Dynamics Finance and operation implementation. 
        
        Message History:
        {context}
        
        
        Generate a professional Executive Summary for a Statement of Work (SOW) document. 
        Focus on summarizing the collaboration, proposed approach, and objectives concisely
        
        Guidelines:
        1. donot repeat information. write concise executive summary no more than 100-150 words. 
        2. The company that is generating this SOW document is Quisitive and the company for whom the document is generated is client company, provided in context.
        do not add placeholder like ("Client Company") or ("Source Company")
        3. starts with "In collaboration with ("Client_Company_Name"), Quisitive is pleased to present this Statement of Work (SOW)" 
        4. follow the define structure. include Heading. 
        
        the output should be a JSON object with the following format:
        1) "type": "heading", "level": level of the heading 1 , "text": "Executive Summary"
        2) "type": "paragraph", "title":"", "text": <text>
    
        # Executive Summary
        
        Always enclose the whole JSON object in [] brackets and ensure the proper JSON structure output.
        
        """

    # Run LLM
    executive_summary_creation_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", executive_summary_prompt),
                ("human", "{input}")
            ]
        )

    executive_summary_chain = executive_summary_creation_prompt | llm
    
    executive_summary = invoke_llm_with_retry(executive_summary_chain, msg_summary, "write executive summary for the project based on the provided data from Microsoft Dynamics Finance and operation implementation company.")
    
    return executive_summary
    

    
software_products_required = """Product/Technology , Item Version,  Required by
Microsoft Azure Subscription,	Required for any environment deployments on the Azure platform. (Consumption pricing must be reviewed by a Microsoft Services Architect), Prior to project initiation phase start date
Data Migration Tools,	Excel, 	Prior to project initiation phase start date
Reporting Tools	Microsoft, Dynamics 365 Reporting Tools	Prior to project initiation phase start date
Office Automation,	Microsoft Office 365,	Prior to project initiation phase start date
Programming Tools,		,Prior to Design phase start date
Version Control Software,	Microsoft DevOps,	Prior to Analysis phase start date
Design Tools,	Microsoft Office Visio 2016 Customer or Viewer,	Prior to project initiation phase start date
Documentation Tools,	Microsoft Office 365, 	Prior to project initiation phase start date
Project Planning Tools,	Microsoft Office Project 2010 or PDF Viewer,	Prior to project initiation phase start date """

data_migration_responsibilities="""
Task	Consultant,	Customer,	Required By
Data Migration Strategy,	X,		,End of Analysis
Extraction from legacy system,		,X	,End of Analysis
Data cleansing,		,X,	Begin Design
Design of tools and templates,	X,		,End of Design
Data mapping (legacy to new),	X,		,End of Design
Develop templates and the format data should take,	X,		,End of Design
Place data in template format, staging table or form,		,X 	,No later than the start of Process Testing
Loading data into new system,	X,		,No later than the end of Process Testing
Testing and validating (reconciliation) migrated data,		,X	,No later than the end of Process Testing
Manual data migrations (if any),		,X,	No later than the end of Process Testing
"""

custom_coding_responsibilities="""
Task|	Consultant|	Customer|	Required By
Provide Software Requirements|		| X | 	End of Analysis
Schedule and conduct design workshops|	X |		| End of Design
Create Functional Design Document (FDD)| X |		|End of Design
Create Technical Design Document (TDD)| X |		| End of Design
Development of Custom Code|	X |		| End of Dev
Define Unit Test Scripts|	X |		| End of Dev
Execute Unit Test Scripts|	X |		| End of Dev
All development tasks, including FDD, TDD, development and testing, for systems outside of Dynamics 365|		| X	|End of Dev


"""

integration_interfaces_in_scope="""
Seq No. |	Functional Area	Integration Type	| Scope & Assumptions
II1     |	NA		                            | 1.	
"""
  
 
    
def project_scope_data1(msg_summary):
        
    project_scope_prompt= f"""
        You are the Microsoft Dynamics Finance and operation implementation expert with the role of solution architect You do pre sales work, 
        you understand the client requirement and scope and add it to the documentation, you are expert in planning and estimating the project 
        timeline based on the best practices of Microsoft Dynamics Finance and operation implementation. 
         
        Follow the sample structure and include clear details about each heading and subheadings. Create tables where mentioned. 

        Data Provided:
        {msg_summary}

        Sample Structure:Table for the following. 
        Create Table for Services in Scope. 
        Table 2 Services in Scope
            Column 1: Solution Component - High-level component name (e.g: Legal Entities, Languages, Sites/Locations, Dynamics 365 Production Instances, Business Processes , Customizations,Interfaces,Reports, ISV etc.).
            Column 2: Description - Brief, clear description of the component or feature.(for example Single Legal Entity., US English, <Headquarters Location>, Single Instance, Dynamics 365 supports numerous business processes. However, this SOW incorporates only those processes agreed upon with the Customer to be implemented in the scope of the work., Functional 'gaps' that are considered customizations/enhancements are extensions or modifications to the Dynamics 365 functionality and logic. ('gaps' may also be filled by ISVs), Interfaces interchange data between (to/from) Dynamics 365 and other business systems. Interfaces typically exchange data on a periodic or scheduled basis (batch) but can also be a real-time interchange.,Reports are operational reports or creation of files that can be loaded into another data store.
            Microsoft provides a  library of reports that can be leveraged out of the box for this purpose., ISVs are Independent Software Vendors who have developed applications that extend the Dynamics 365 functionality.  ISVs are typically used to provide functionality that might otherwise need to be custom developed.)
            Column 3: Key Scope Assumptions - Specific assumptions, limitations, or conditions relevant to the component. (e.g Single Dynamics 365 business unit will be configured to support solution modelling and functional testing activities throughout the project.Base currency US Dollars  Base country localizations US ,Applies to both data elements and system application labels.,All project activities will be conducted online. Attendees from all locations will participate via web and tele-conferencing., Microsoft Dynamics 365 Field Service is in scope., The processes assumed at the start of the project will be validated throughout the project. Any changes to the specific process or number of processes will be subject to the change management process.,The number, type and complexity of Customizations may change as a result of the Analysis activities and therefore impact cost and schedule. Any changes to the number of customizations is subject to the change management process. , The interfaces assumed at the start of the project will be validated throughout the project. Any change to the number of the interfaces will be subject to the change management process and may therefore impact cost and schedule.,A list of available Out of the Box Dynamics reports can be found in Microsoft Customer Source which can be accessed by the customer once software licenses are purchased.
            Reporting requirements will be documented throughout the project.  However, these requirement gaps will be sized (S, M, L), but not analysed further during the conceptual design stage.,A list of available Out of the Box Dynamics reports can be found in Microsoft Customer Source which can be accessed by the customer once software licenses are purchased.
            The ISVs assumed at the start of the project will be validated throughout the project. Any changes will be subject to the change management process and may therefore impact cost and schedule.)            
            
            Ensure the table design is clear and professional, suitable for inclusion in an SOW document.
            
        ### Software Products/Technologies
        Write about Software Products and Technologies for microsoft dynamics Finance and Operation implementation on provided context. 
        Add the tools and technology for the project considering Microsoft dynamics Finance and Operation implementation. 
        Also create Table for it accordingly
        
        Table 3 Software Products/Technologies Required
            Data:
            {software_products_required}
            
            Generate a professional and structured table for inclusion in an SOW document, 
           
        ### Application Modules/Components In-Scope
        Use ERP or ERP platform data from context for this section.
        Write about Application Modules and Components In-Scope on provided context for Microsoft dynamics Finance and Operation 
        also create table for it accordingly
        
        Table 4 In-Scope Application Modules/Components  
        Create a table for Microsoft 365 dynamic In-Scope Application Modules or components or feature set i.e Customer Services, Sales, Marketing e.t.c
        
        
        ### Processes In-Scope
        Generate a professional and structured description for the 'Processes In-Scope' section of an SOW document. The content should include the following:

        A concise introductory statement identifying the total number of high-level business processes in scope for the project.
        
        Table 5 In-Scope Processes

            Create a table with the following columns:

            Work Stream: Name of the business process or workstream.
            Description: Briefly explain the process and its purpose in the project context.
            Assumptions: List key assumptions for each process, such as whether it leverages standard out-of-the-box configurations or requires customizations.
            A concluding statement that specifies:

        The assumption that in-scope business processes will use out-of-the-box configurations unless explicitly stated otherwise.
        Any customizations or additional requirements not listed will be managed through a formal change request process.
        Ensure the content is adaptable and relevant to the project details and provided data, with clear examples for each process.

         Project Scope - Additional  - This should be heading level 2
           Integration and Interfaces - This should be heading level 3 
            Create a professional and structured description for the 'Integration and Interfaces' section of an SOW document. The content should include the following elements:

            An introductory paragraph that:

            Highlights the purpose of the section, specifying the scope of integration and interface analysis.
            States that high-level requirements will be gathered to develop the Integration Strategy document.
            Notes that any additional integrations outside the current scope will be managed via the change management process.
            
            
            Table 6 Integration Interfaces in Scope
            
        
            create a table for Integration Interfaces in Scope with the following columns and the data you will get from the context provided for Integration:
            Seq No. |	Functional Area	| Integration Type	| Scope & Assumptions

            You will get the data from the context for this table. 
            Seq No. II1 , II2 , II3, ...
            Functional Area: This you will get from the context with the "Category" key defined in the context.
            Integration Type: This you will get from the context with the "Value" key defined in the context.
            Scope & Assumptions: This you will get have to define with respect to context it has to be short.
            
            Table 7 Integration Interfaces Responsibilities

            Task: Lists specific tasks related to integrations/interfaces for Microsoft dynamics 365. i.e Schedule and conduct design workshops ,Develop D365 interfaces ,Develop Interfaces - non-D365 portion of interfaces, D365 interface code unit testing
            e.t.c add more task as per microsoft 365 dynamic implementation in project
            Consultant: Indicates if the consultant is responsible for the task.
            Customer: Indicates if the customer is responsible for the task.
            Required By: Specifies when the task needs to be completed (e.g. Design, Development, No later than Code Complete for respective release).
            A concluding paragraph:

            States any pre-defined hours or efforts included in the project estimate for integrations.
            Mentions that additional effort, if required, will follow the change management process.
            Ensure the content is adaptable, relevant to the provided data, and presented clearly and professionally
       
        Data Conversion This is heading level 3
        
        Generate content for the 'Data Migration' section of an SOW document. Include the following

        Specify allocated hours for data migration based on discussions with the customer.
        Mention consultant services data mapping, loading (up to three iterations), and validation.
        State that additional charges may apply for extra data loads due to customer's failure to meet responsibilities.

        
        Table 8 Data Migration Scope
        
        create a table for Data Migration Scope with the following columns and the data you will get from the context provided for Data Migration:
        Sequence No. | Data Source/Entity| Functional Area	| Data Type	| Scope & Assumptions

        You will get the data from the context for this table. 
        Sequence No. DM01 , DM02 , DM03, ...
        Data Source/Entity: This you will get from the context with the "Category" key defined in the context.
        Functional Area: This you will have to define with respect to context it has to be short like finance,sales and others with respect to microsoft 365 dynamic implementation.,
        Data Type i.e Master, and
        Scope & Assumptions: This you will have to define with respect to context it has to be short.
            
        
        Table 9 Data Migration Responsibilities
        {data_migration_responsibilities}
        

        Modification/Enhancement Scope - This is heading level 3
        Generate content for the 'Modification and Enhancement Tool' section of an SOW document. Include the following


        State that customizations and modifications to Dynamics 365 are limited, with customer adopting standard processes or workarounds where possible.
        Mention that custom design (FDD/TDD), development (coding), and testing are required for in-scope customizations.
        Specify that custom coding activities will be re-estimated after the Functional Design Document (FDD) is completed.
        
        Table 10 Custom Code Scope

        Columns: 
        Sequence No: like CC-1., 
        Process: i.e TBD, 
        Gap Description: i.e TBD, and 
        Estimated Hours: i.e 0.

        
        Table 11 Custom Coding Responsibilities
        {custom_coding_responsibilities}
        
        

        Guidelines:
        1. Start with Table 2 Services in Scope Table. Do not add any heading or content or anything before this table. 
        2. Provide concise and detailed content under each subheading.
        3. Ensure the content is aligned with the data provided for relevant headings and fits the structure. 
        4. Use relevant data from context for relevant headings and sub headings. e.g use ERP-platform or ERP data for Application modulues or use Data migration data for data migration section.
        5. Use a professional and formal tone throughout.
        6. Create the tables where mentioned with the help of the provided data for relevant heading.
        7. Do not use any : in the content.
        
        
        Remember to strictly follow the JSON schema mentioned below to create output and include the tables where mentioned. Do not include any extra characters or encode any text in quotes ". create a proper JSON object with the following format:
        1) "type": "heading", "level": level of the heading in numbers (integer) i.e 2 or 3 according to the numbers of # in heading text, "text": <text>
        2) "type": "paragraph", "title":"", "text": <text>
        3) "type": "table", "title": <text>, "data": <2D array of string>
        4) "type": "list", "title": <text>, "data": <2D array of string>

        Generate the Project Objectives and Scope section following the JSON format given. Always output JSON adhering to the mentioned schema and always enclose the whole JSON object in [] brackets and ensure the proper JSON structure output.

    """

    # Run LLM
    project_scope_creation_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", project_scope_prompt),
                ("human", "{input}")
            ]
        )

    project_scope_chain = project_scope_creation_prompt | llm
    
    project_scope = invoke_llm_with_retry(project_scope_chain,msg_summary,"Write Project objective and scope for Microsoft dynamics Finance and Operation implementation for the provided data.")

    
    return project_scope
    
 
  
def project_approch_data(msg_summary):
    
        
    project_approch_prompt="""
        You are the Microsoft Dynamics Finance and operation implementation expert with the role of solution architect You do pre sales work, 
        you understand the client requirement and scope and add it to the documentation, you are expert in planning and estimating the project 
        timeline based on the best practices of Microsoft Dynamics Finance and operation implementation. 
        
        Youe task is to generate the Project Approach section based on the provided data for a professional document. 
        Follow the given sample structure and include details under each phase heading.

        Data Provided:
        {context}

        Sample Structure:
        # Project Approach
        ## Approach - Sure Step 365 Methodology
        Generate content for the Project Approach - 365 Methodology section of an SOW. Include details about the phases covered 
        (e.g., Project Initiation, Analysis, Design, Development, Deployment, and Operations), any excluded phases, and a phased implementation strategy. 
        Ensure the content is adaptable to the project context and emphasizes a structured approach, such as Sure Step 365, with specific methodologies or swim lanes 
        as applicable.
        
        Note: Give this figure heading with hash in the content: Figure 1 Sure Step 365 Methodology \n #ProjectApproachImagePlaceholder# where you want to add the image of the project approach.
        
        ### Project Initiation Phase
        Write about project initiation Phase based on the provided context for Microsoft dynamics 365 
        
        ### Analysis & Design Phase
        Write about Analysis & Design Phase based on the provided context for Microsoft dynamics 365 
        
        ### Build Phase
        Write about Build Phase based on the provided context for Microsoft dynamics 365 
        
        ### Test & Deployment Phase
        Write about Build Phase based on the provided context for Microsoft dynamics 365 
        
        ### Operations Phase
        Write about Build Phase based on the provided context for Microsoft dynamics 365 
        

        Guidelines:
        1. Start with a high-level summary of the project approach under 'Approach - Sure Step 365 Methodology.'
        2. List Key 'Project Service Deliverables' in tabular format.
        3. Ensure the tone is professional and consistent with business documentation.
        4. Do not use any : in the content.
        
        Remember to strictly follow the JSON schema mentioned below to create output and include the tables where mentioned. Do not include any extra characters. create a proper JSON object with the following format:
        the output should be a JSON object with the following format:
        1) "type": "heading", "level": level of the heading i.e 1 or 2 or 3 according to the number of # , "text": <text>
        2) "type": "paragraph", "title":"", "text": <text>
        3) "type": "table", "title": <text>, "data": <2D array of string>
        4) "type": "list", "title": <text>, "data": <2D array of string>
    


        Generate the 'Project Approach' section following the JSON format given. do enclose the whole JSON object in [] brackets.
        remember: Do not add any timeline under this heading

        
    """
    


    # Run LLM
    project_approch_creation_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", project_approch_prompt),
                ("human", "{input}")
            ]
        )

    project_approch_chain = project_approch_creation_prompt | llm
    
    project_approch_timeline = invoke_llm_with_retry(project_approch_chain,msg_summary, "Create project approach with content on the bases of provided context for Microsoft Dynamics Finance and operation implementation ")
  
    return project_approch_timeline
    
    
        
def project_timeline_data(data): 
    
    project_timeline_prompt = """
        
        You are the Microsoft Dynamics Finance and operation implementation expert with the role of solution architect You do pre sales work, 
        you understand the client requirement and scope and add it to the documentation, you are expert in planning and estimating the project 
        timeline based on the best practices of Microsoft Dynamics Finance and operation implementation. 
        
        Your task is to generate the 'Project Timeline and Content' section Based on the provided data, for a professional document. 
        Include both the timeline and relevant content for each phase of the project. 

        Data Provided:
        {context}

        Sample Structure:
        Timeline Approach
        Provide a summary of timeline and phases based on the provided data.
        Guidelines:
        1. Provide precise durations for each phase. See the timeline duration from the provided data in order to provide correct timeline.
        2. For each phase, include a professional summary of its objectives, key activities, and deliverables.
        3. Ensure the structure matches the format provided above.
        4. Maintain a professional tone and align with business documentation standards.
        5. Do not use any : in the content.
        
        5. Create the table for each phase under phases. The table should contains the following columns:
        
        Table: 

        Category: category or name of the activity i.e in Analysis phase: Consultant Activities 
        Description: responsibility or role or work against that category i.e  for Consultant Activities in analysis phase category the description is like
            •	Conduct the following workshops
            •	Business Process/ User Story Requirements Workshops
            •	Customer Review Workshops of initial setup and configuration of core 365 application
            •	Data Integration and Migration Strategy Workshop
            •	Documentation of User Stories in Azure DevOps
            •	Documentation of gaps in Azure DevOps and Fit Gap sheet
            •	Develop high-level conceptual design 
            •	Data integration and migration mapping
            •	Installations of environment specified in Section 2.2.4 Required Environment  
            •	Provide functional support for requirement test writing 
            
        Add more category and description as per data. 

        Generate the 'Timeline'section based on the given data and structure.
        Remember to take the weeks or time data from the given timeline data or context. Do not add any timeline from your own. 
        DO NOT use <br> or any other HTML tag in content.
        Do not create any extra column then the columns that are mentioned in the table.
        
        the output should be a JSON object with the following format:
        1) "type": "heading", "level": level of the heading i.e 2 or 3 or 4 according to the number of # , "text": <text>
        2) "type": "paragraph", "title": "" , "text": <text>
        3) "type": "table", "title": <text>, "data": <2D array of string>
        4) "type": "list", "title": <text>, "data": <2D array of string>
       
    """  
    
    # Run LLM
    project_timeline_creation_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", project_timeline_prompt),
                ("human", "{input}")
            ]
        )

    project_timeline_chain = project_timeline_creation_prompt | llm
    
    project_timeline = invoke_llm_with_retry(project_timeline_chain, data, "Write Project Timeline and content on the bases of provided context for Microsoft Dynamics Finance and operation implementation")
 
    
    return project_timeline
    
       

def fees_table_data(msg_summary):
    fees_table_prompt="""
    
    You are the Microsoft Dynamics Finance and operation implementation expert with the role of solution architect You do pre sales work, 
    you understand the client requirement and scope and add it to the documentation, you are expert in planning and estimating the project 
    timeline based on the best practices of Microsoft Dynamics Finance and operation implementation.
    
    You are an expert in creating professional project estimation Based on the provided data, generate the Fees section and create the Tables section for the document. Ensure both sections follow the provided structures.
    
    Data Provided:
    {context}
 
 
    Fees
  
    Generate the 'Fees' section for the SOW document detailing like this: 
    The services performed under this SOW will be billed on a time and materials basis at the hourly rates shown in the table below but shall not exceed the charges in the Project Budget table below except as otherwise 
    agreed by the parties in writing pursuant to the Change Management Process described herein. You will be invoiced for the actual fees incurred performing the services plus any reasonable out of pocket travel and living 
    expenses as described below. 
    
    Generate Table by using the context data provided for the following tables.
    Table
    This table is generated by using 'rateperhour' from context that contains Rate / hour data against Roles. It contains the following 2 columns only:
    - Role: 'Role' from rateperhour in context.
    - Rate / hour: 'Rate' from rateperhour data from context.
   
    Note: Make sure to align role and their rate as per the provided data. Do not shuffle the roles and rates, Keep the provided order from context.
   
   
    Table
    This table is generated by using 'Phases' that contains estimated hours data and 'PhaseRate' that contains Estimated Cost data from projectestimation in context. It contains the following 4 columns only:
    - Phase i.e Analysis & Design, Build,
    - Hours Estimate    
    - Avg. Rate / Hour  
    - Estimated Cost
    - Total: Its contains Total of Hours Estimate and Total of Estimated Cost.
 
    Guidelines:
    1. stict to the provided data for the fees and estimation.
    2. Use a consistent, formal tone throughout.
    3. Ensure the document content is comprehensive and aligns with the provided data and structure.
    4. Ensure the structure matches the format provided above.
    5. Ensure that the roles and rate per hour data is align with each other.
    5. Make sure the resources role names and rate per hour are align from the provided data. Do not shuffle the roles and rates, Keep the provided order from context.
    
    
    Remember to strictly follow the JSON schema mentioned below to create output and include the tables where mentioned. Do not include any extra characters. create a proper JSON object with the following format:
    the output should be a JSON object with the following format:
    1) "type": "heading", "level": level of the heading i.e 1 or 2 or 3 according to the number of # , "text": <text>
    2) "type": "paragraph", "title":"", "text": <text>
    3) "type": "table", "title": <text>, "data": <2D array of string>
    4) "type": "list", "title": <text>, "data": <2D array of string>
    
    Generate the Fees section following the JSON format given. Do enclose the whole JSON object in [] brackets.

    """
    
    
    fees_table_creation_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", fees_table_prompt),
                ("human", "{input}")
            ]
        )

    fees_table_chain = fees_table_creation_prompt | llm
    
    fees_table = invoke_llm_with_retry(fees_table_chain,msg_summary, "Create fees structure with tables for the provided data")

    
    return fees_table



# --------------------------------------------------------------------------------------------
#     static data  

def area_out_of_scope_static():
    area_out_of_scope="""### 2.2.6 Areas Out of Scope
Any area that is not explicitly listed in Sections 2.2 and 2.3 as “In-Scope” is out-of-scope for this engagement. Areas that are out of scope for this engagement include, but are not limited to, the following:

##### Table 13: Areas Out of Scope
| Area                          | Description                                                                                                                                                                                                                                                                                                             |
|-------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Microsoft Product Licenses    | Microsoft product licenses and/or software subscriptions will not be provided under this SOW. Customer is responsible for acquiring all necessary product licenses and/or software subscriptions required as a result of this SOW.                                                                                     |
| Hardware                      | Hardware will not be provided under this SOW. Customer is responsible for acquiring all necessary hardware.                                                                                                                                                                                                           |
| Infrastructure Setup          | Infrastructure (required hardware and software) and/or Azure Cloud setup including any High Availability/Disaster Recovery/Load balanced environments is not in scope under this SOW.                                                                                                                                |
| Legacy Systems                | Quisitive will not be responsible for supporting Customer legacy systems.                                                                                                                                                                                                                                             |
| Integration with 3rd Party Software | Quisitive will not be responsible for integration with 3rd Party Software (not applicable to Dynamics ISVs) which are not part of the scope.                                                                                                                                                                           |
| Source code review            | Customer will not provide Quisitive with access to non-Quisitive source code or source code information.                                                                                                                                                                                                               |
| Requirements Gathering        | Quisitive will analyze the requirements documented for processes identified as in scope. Any business process outside of the scope will not be analyzed as part of this SOW.                                                                                                                                           |
| Documentation                 | Preparation of documentation about any existing process, previous standards, policies, or existing guidelines is out of scope under this SOW.                                                                                                                                                                         |
| Process Re-engineering/Process Mapping | Design of functional business components of the solution unless specifically included in scope and delivered by Quisitive Consulting staff. Process engineering, or re-engineering activities are outside of the scope of the Quisitive responsibilities and are assumed to be completed by Customer.                                 |
| Adoption & Change Management  | Design or re-design of Customer’s functional organization unless specifically included in scope and delivered by Quisitive Consulting staff.                                                                                                                                                                          |
| Updates Patches and Fixes     | Upgrades, Updates, Fixes and Patches are included in this scope of work, specifically:                                                                                                                                                                                                                                 |
|                               | - Operating Systems Versions, updated service packs, or hotfixes.                                                                                                                                                                                                                                                       |
|                               | - Dynamics for Finance & Operations Component Versions, platform updates, service packs, or hotfixes, Design Change Request (DCR).                                                                                                                                                                                     |
|                               | - Other Server or Customer Framework Component Versions.                                                                                                                                                                                                                                                               |
|                               | If a fix or patch is identified as required after the start of the Project, an assessment will be made by the Project management team from Quisitive and Customer as to the need for such a fix. If the fix is deemed needed by both parties to enable functionality required by Customer, Quisitive and Microsoft will make the change. |
|                               | If the fix or patch is due to defects in the Microsoft software, Microsoft will fix at no charge to the Customer.                                                                                                                                                                                                      |
| Global Solution Needs         | Language implementations that are not part of the core product will not be implemented under this SOW.                                                                                                                                                                                                                |
| Business Intelligence and Data Warehousing | Unless specifically detailed in this document, implementation and/or data replication to support business intelligence and data warehousing are not included in this SOW.                                                                                                                                                           |

"""

    return area_out_of_scope
    

def project_governace_static():
    project_governace = """# 4 Project Governance Approach
The governance structure and processes the team will adhere to for the project are described in the following sections:

## Project Management
The project will be managed by a full-time Quisitive Project Manager to work with the Customer Project Manager. The Quisitive Project Manager will be responsible for the overall delivery of Quisitive services as specified in the table below. The Project Manager will report to the Project Management Office or the Steering Committee, which will consist of the Customer Sponsors, Customer Program Manager, and the Quisitive Delivery Leader.

### Communication Plan
The following will be used to communicate during the project:
- **Communication plan**: This document will describe the frequency, audience, and content of communication with the team and stakeholders. It will be developed by Quisitive and the customer as part of project planning.
- **Status reports**: The Quisitive team will prepare and issue regular status reports to project stakeholders per the frequency defined in the communication plan.
- **Status meetings**: The Quisitive team will schedule weekly status meetings to review the overall project status, the acceptance of deliverables, and review open problems and risks.

### Issue/Risk Management Procedure
The following general procedure will be used to manage active project issues and risks during the project:
- **Identify** : Identify and document project issues (current problems) and risks (potential problems that could affect the project).
- **Analyze and prioritize**: Assess the potential impact and determine the highest priority risks and problems that will be actively managed.
- **Plan and schedule**: Determine the strategy for managing priority risks and issues and identify a resource who can take responsibility for mitigation and remediation.
- **Track and report**: Monitor and report the status of risks and problems.
- **Escalate**: Escalate to project sponsors the high-impact problems and risks that the team is unable to resolve.
- **Control**: Review the effectiveness of risk and issue management actions.

Active issues and risks will be regularly monitored throughout the project.

### Change Management Process
During the project, either party can request modifications to the services described in this SOW. These changes only take effect when the proposed change is agreed upon by both parties in writing. The change management process steps are:
1. **The change is documented**: All change requests will be documented by Quisitive in a Quisitive change request form and submitted to the Customer. The change request form includes:
- A description of the change.
- The estimated effect of implementing the change. 
2. **The change is submitted**: The change request form will be provided to the Customer.
3. **The change is accepted or rejected**: The Customer has three business days to confirm the following to Quisitive:
- **Acceptance**: The Customer must sign and return the change request form.
- **Rejection**: If the Customer does not want to proceed with the change or does not provide an approval within three business days, no changes will be performed.

- At Quisitive's discretion, time required to research and document customer-originated change requests will be billed at the standard rates specified in Section 7 “Fees.”

### Critical Path Decisions
Throughout the project, Quisitive will submit requests for decisions for the Customer to act on. Decisions are assigned due dates, and it is assumed that the Customer will provide the required feedback or make decisions on the due date agreed upon by the parties.

Some decisions requested will be determined by Quisitive as Critical Path Decisions (CPD) with key dependencies. These will be submitted with a written Notice of Decision (NOD) request to the Customer's Project Sponsor. If the NOD request is not acted on within 5 business days of receipt by the Customer's Project Sponsor, it may impact the project critical path and may be addressed as a Project Change Request and submitted through the Change Management process.

### 4.1.5 Project Steering Committee
The CRM Steering Committee provides overall senior management oversight and strategic direction for the project. The PROJECT Steering Committee for the project will meet per the frequency defined in the communication plan and will include the roles listed in the following table. The responsibilities for the committee include:
- Making decisions about the project's strategic direction.
- Serving as a final arbiter of project issues.
- Approving significant change requests.

##### Table: Steering Committee Members
| Role                         | Representing       |
|------------------------------|--------------------|
| Project Executive Sponsor     | Customer          |
| Customer Project Sponsor      | Customer          |
| Executive Technical Lead      | Customer          |
| Project Manager               | Customer          |
| Architect                     | Customer          |
| Executive Sponsor             | Quisitive         |
| Delivery Architect / Solution Delivery Lead | Quisitive |
| Project Manager               | Quisitive         |

### 4.1.6 Architecture Board
Technical Governance and solution oversight for the project will be provided by an Architecture Board, which will consist of the following key technical leadership and management representatives:

##### Table 21: Architecture Board Composition
| Name  | Representing | Title       | Project Role |
|-------|--------------|-------------|--------------|
| TBD   | Customer     | Architect   |              |
| TBD   | Quisitive    | Delivery Architect |       |
| TBD   | Quisitive    | Solution Architect |       |

The Architecture Board will hold weekly meetings and document key outcomes. Customer and Quisitive Managers will share joint responsibility for management and tracking of architectural changes, issues, risks, and decisions.

The Architecture board is responsible for the following:

- Conducting meetings to review and address architectural decisions including implications and impacts of decisions and resulting changes to the solution and project plan.
- Providing solution and direction oversight including managing issues and risks escalated by the solution work streams including:
    - Infrastructure decisions (design, sizing, availability, configuration)
    - Data migration, interfaces, reporting and security design
    - Technical solution architecture (components, mapping, integration)
    - Functional solution design (fits, customizations, ISVs)
    - Design and technical documentation standards and programming standards
- Facilitate cross work stream collaboration among technical stakeholders and serve as a single platform through which key technical discussions are addressed.

### 4.1.7 Escalation Process
The Quisitive project manager will work closely with the customer project manager, sponsor and other designees to manage project issues, risks, and change requests as described previously. The Customer will provide reasonable access to the sponsor or sponsors to expedite resolution. The standard escalation path for review, approval, or dispute resolution is as follows:

- Project team member (Quisitive or the Customer)
- Project manager (Quisitive and the Customer)
- Quisitive delivery manager
- Quisitive and the Customer project sponsor
- PROJECT Steering Committee

## 4.1.8 Service Deliverable Acceptance Process

During the project, Quisitive will submit certain deliverables (listed in Section 3.3 as deliverables) for the customer's review and approval.  
Within three business days of the date of receipt, the Customer is required to:

- **Accept the deliverable** by signing, dating, and returning a service deliverable acceptance form, which can be sent by email, or by using (or partially using) the deliverable  
- **Reject the deliverable** by notifying Quisitive in writing; the Customer must include the reason(s) for rejection.

### Document Deliverables

The review and approval of documents will be governed by the following:  
The Quisitive Project Manager, or his/her designee, will prepare a Deliverable Acceptance Form (DAF) and forward it with the respective document deliverable to the Customer Project Manager, or Customer designee, for review and acceptance.

- Customer project manager will be responsible for the distribution of documents, organizing internal reviews, and collating feedback into a single document. It is required that the Customer complete all reviews and comments with tracking activated in the original document.
- Customer and Quisitive will review the comments promptly and agree on those which require the document to be changed.
- Quisitive will change the document in line with the agreement and resubmit the updated documents within 2 business days for final sign-off.
- Customer will review the revised DAF and either indicate its agreement by signing and returning the DAF or communicate any further revisions required to the Consultant.

### Document Acceptance Classification and Criteria

- Major - error in understanding or design which will prevent the solution from working.
- Minor - an item or error which will not have a major impact and can be corrected in the following design documents.
- Cosmetic - incorrect spelling, grammar, or formatting.

Any document with zero Major, 2 or fewer Minor, and/or 10 or fewer Cosmetic items will be accepted.

## 4.2 Project Completion

Quisitive will provide Services defined in this SOW within a period of 18 weeks as mentioned in Section 3.2 - Timeline. If additional services are required that are out of scope as defined herein, the Change Management process as defined in Section 4.1.3 may be followed and the contract modified.

## 4.3 General Customer Responsibilities

In addition to any Customer activities identified elsewhere in this SOW, Customer will perform or provide the following:

1. Provide suitable workspaces, including meeting rooms and web/tele-conferencing equipment.
2. Provide resources and connectivity giving the Quisitive onsite team access to appropriate facilities, internet, and e-mail.
3. Provide access to all necessary Customer work sites, systems logon and passwords as well as material and resources as needed and as reasonably requested by us in advance.
4. Assume responsibility for management of all non-Quisitive managed vendors.
5. Provide access with proper licenses to all necessary tools and third-party products required for Quisitive to complete its assigned tasks.
6. Acquire Azure subscription required to support the environments as defined in the scope section of this SOW.
7. Provide personnel with specific business expertise and detailed knowledge of the current systems and business processes (SME, Business Analysts, Data analysts, etc.).

## 4.4 Project Assumptions

All estimates regarding fees, timelines, and our detailed solution are based on information provided by the Customer to date, and all the listed assumptions within this document being validated as true during this project. They are also based on the Customer and Quisitive working in partnership, as described within the approach and governance sections of the document. Anything that differs materially regarding the information provided, the approach and governance documented, or the assumptions, can result in Quisitive raising a change request to cover additional work or extended durations as a direct result. Notwithstanding anything to the contrary herein, Customer shall not be responsible for additional fees required as a result of any acts, omissions, or occurrences outside of its reasonable control including, without limitation, Quisitive's failure to timely comply with its obligations and responsibilities as set forth herein or Quisitive's failure to perform as stated herein.

The Services and delivery schedule for this project are based on the following assumptions:

1. Staffing the project with the right resources requires 4 to 6 weeks. For this reason, the project initiation phase will not start until at least 4 weeks after the SOW is signed by Quisitive and Customer.
2. The standard workday for the project is between 8:00 AM and 5:00 PM local time where the team is working, Monday through Friday, except for scheduled holidays. The project schedule will be constructed using a standard 8-hour day for both Customer and Quisitive resources.
3. In performing services under this SOW, Quisitive will rely upon any instructions, authorizations, approvals, or other information provided by Customer's Project Manager or personnel duly designated by Customer's Project Manager. All estimates regarding fees, timelines, and our detailed solution are based on information provided by Customer to date.
4. Quisitive's resources may perform services remotely or on-site from Quisitive facilities, Customer facilities, or Quisitive partner's facilities, as mutually agreed by the parties.
5. Quisitive may make changes in staffing including but not limited to number of resources, individuals, project roles etc. with a 2-week notice to Customer; provided that no such staffing changes shall extend the duration of the Project or result in additional fees.
6. If the project schedule requires Quisitive's resources to perform dedicated services at Customer's site on a weekly basis, Quisitive resources will typically be on-site for 3 nights/4 days; arriving on Monday morning and leaving on Thursday afternoons. The consultants will work off-site on Fridays.
7. Quisitive consultants will work on-site as described in Assumption #6 above, for three weeks, then one-week off-site each month.
8. Process engineering, or re-engineering activities are assumed to be completed by Customer, and/or Customer's representative.
9. Customer is responsible for all organization change management.
10. Failure to complete any required site readiness activities described herein for Quisitive to deliver its services according to the agreed upon Project schedule may result in Project delays requiring a Change Request to this SOW as well as additional Project costs.
11. Project work will be performed in the relevant “Trident Maritime Systems” Locations, including headquarters. Individuals or teams may be required to make specific site visits during the project. They will be handled on an as-needed basis and requested or approved via project governance process.
12. All Project communications will be in English, local language support and translations will be provided by Customer.
13. Customer resources will be available to perform their roles on the project team at appropriate times and for the required duration.
14. Key Customer technical resources will be available throughout the engagement to assist the Quisitive project team.
15. Project management from Customer is assumed to be a key responsibility for the assigned Trident Maritime Systems resource.
16. All work is to be contiguously scheduled; any breaks in the engagement calendar will be mutually agreed.
17. If the scope listed in this SOW changes significantly due solely to Customer's fault there will need to be effort expended on re-estimation and on adjusting the scope and/or project timelines; this will lead to additional costs and possible Change Requests.
18. Quisitive will provide all document deliverables based on Quisitive documentation standards.
19. All project document deliverables will be in English only.
20. The application will be accessible over the corporate intranet of Customer. It is Customer's responsibility to configure secure extranet infrastructure if required.
21. Customer will provide 24x7 access to its development and testing environments to onsite and offsite consultants to carry out work on the project.
22. Any bugs arising in any third-party tools are the responsibility of each vendor and will not be fixed by Quisitive.



##### 4.4.1 Infrastructure Assumptions

1. Existing systems or programs upon which the project deliverables depend are stable and will not change during the term of this project.
2. Where applicable, Customer will provide servers with the base Windows operating system, latest patches installed, and other required software, such as Antivirus protection, installed.
3. Timely availability of subscription(s), hardware, software, and physical space for the solution environments is essential. Failure to complete all site readiness activities that are required for Quisitive to deliver its services according to the agreed-upon project schedule may result in project delays requiring Change Orders to this SOW as well as additional project costs.
4. The proposed solution assumes Customer already has Microsoft Active Directory set up and active with access to the required infrastructure, including Azure Active Directory that will be used by this solution through correct security zones and firewall control.
5. Customer is responsible for or the activation of the Dynamics 365 for Finance & Operations Enterprise Edition Online subscription.
6. A Microsoft Azure data center based in the United States or Canada will be utilized for D365 instances deployed in this project. The cloud deployment approach for production deployment of the D365 solution will not be determined in this project.

##### 4.4.2 Other Assumptions

1. The fit/gap process will likely reveal some functional gaps. Handling of additional gaps will be addressed via the Change Review Board process.
2. Workflow requirements presented by Customer shall be documented in Azure DevOps and included in fit/gap disposition. However, gap requirements will not be analyzed by Quisitive. It is assumed that Customer will address workflow gaps.
3. Customer will provide Work stream Leads and SMEs needed to support each work stream and maintain Weekly Solution Modelling Schedule and Deliverables, described in section 3.1.
4. Customer work stream resource plan will support the proposed work stream workshops. These resources will be empowered to lead and manage process alignment, change management, and decision-making.
5. Azure DevOps will be utilized throughout the project to document, organize, and maintain many activities and artifacts, including requirements, action items, issues log, and risk log. To support this, Quisitive provides a standard DevOps template. DevOps administration and operation will be managed jointly by Customer and Quisitive Project Managers/Architects. If extensive, development of DevOps forms, queries/reports, and dashboards requested by Customer will be subject to the Change Management process.
6. Upon completion of the initial installation and setup of D365 instances by Quisitive Technical Infrastructure Consultant, it is assumed Customer will maintain the D365 technical environment.
7. Customer's core project team members will be trained on D365 prior to the start of implementation. This includes functional training relevant to their project work stream.
    
    
    """
    return project_governace





def fallback_data(msg_summary):

    fallback_prompt = """If you find a content related to terms and conditions in provided context. Utilize those details to create a "Terms and Conditions" Ensure the content is clear, concise and only three points. Here Quisitve is the company who is providing service to the other company mentioned in context
    
    
    context:
    {context}   
    the output should be a JSON object with the following format:
        1) "type": "heading", "level": 1 , "text": Terms and conditions
        2) "type": "list", "title": "", "data": <2D array of string>
    
    Remember just generate the terms and conditions only. Do not include bullets or numbers.
    Always enclose the whole JSON object in [] brackets and ensure the proper JSON structure output.
    
    """


    # Run LLM
    # intro = llm.invoke(prompt.format())
    fallback_creation_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", fallback_prompt),
                ("human", "{input}")
            ]
        )

    fallback_chain = fallback_creation_prompt | llm
    
    fallback = invoke_llm_with_retry(fallback_chain,msg_summary,"generate terms and conditions")

    
    return fallback

