from langchain.text_splitter import RecursiveCharacterTextSplitter
from Config.azureEmbeddings import embeddings
import uuid
from dotenv import load_dotenv
load_dotenv()

def raw_content_index(text, metadata):
    """
    Process and upload chunks of a text document by splitting the text into manageable chunks,
    embedding them, and uploading the chunks along with metadata to a search client.

    Args:
        text (str): The text content to be processed.
        metadata (dict): Metadata associated with the document.
    """
    if text:
        # Initialize a text splitter with specific chunk size and overlap
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1200, chunk_overlap=200)
        all_splits = text_splitter.split_text(text)  # Split text into chunks
        print(f"Loaded and split {len(all_splits)} document chunks.")
        
        # Embed the text chunks to obtain vector representations
        vectors = embeddings.embed_documents(all_splits)
        embedded_chunks = [
            {"content": text, "source": metadata, "content_vector": vector}
            for text, metadata, vector in zip(all_splits, metadata, vectors)
        ]
        
        if embedded_chunks:
            documents = []
            for i, chunk in enumerate(embedded_chunks):
                # Create a unique ID for each document chunk
                id = str(uuid.uuid1())
                document = {
                    "id": id,
                    "file_id": metadata.get("file_id"),
                    "chunk_id": f"{id}_pages_{i}",
                    "content": chunk["content"],
                    "title": metadata.get("title"),
                    "subject": metadata.get("subject"),
                    "filepath": metadata.get("file_path"),
                    "contentVector": chunk["content_vector"]
                }
                documents.append(document)
            return documents    
            # Upload the processed document chunks to the search client
            #search_client.upload_documents(documents=documents)
