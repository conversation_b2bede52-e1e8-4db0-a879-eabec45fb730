from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain.agents import create_tool_calling_agent

from Tools.company_combinesearch import companySearchCombineTool
from Tools.document_creation import create_document
from Tools.suggestBusinessProcess import suggest_business_processes
from Tools.suggestModules import suggest_modules_tool

from Tools.suggestMigartionWIthLLM import suggest_Data_migrations
from Tools.suggestERP import suggest_ERP
from Tools.company_data_update import comapny_data_update_tool

from Tools.suggestERP import suggest_ERP
from Tools.company_data_update import comapny_data_update_tool

from Tools.suggestIntegrationWithLLM import suggest_integrations

from Config.azureAI import llm

proposalCreation_prompt = ChatPromptTemplate.from_messages([
    ("system", "you're a helpful assistant, chat id is: {chat_id}, user ID: {user_id}"), 
    ("human", "{input}"), 
    ("placeholder", "{agent_scratchpad}"),
])

proposalCreation_tools = [
    create_document,
    suggest_business_processes,
    companySearchCombineTool,
    suggest_modules_tool,
    suggest_ERP,
    comapny_data_update_tool,
    suggest_integrations,
    suggest_Data_migrations
    ]

proposalCreation_agent = create_tool_calling_agent(
                                                    llm=llm, 
                                                    tools=proposalCreation_tools,
                                                    prompt=proposalCreation_prompt
                                                )