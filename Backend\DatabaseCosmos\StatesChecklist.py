import logging
import random,uuid
import os
from azure.cosmos import CosmosClient, Partition<PERSON>ey, exceptions
from dotenv import load_dotenv
from datetime import datetime
import traceback
from Config.cosmosdb import database

from enum import Enum


# Load environment variables
load_dotenv()
# endpoint = os.getenv("COSMOS_DB_ENDPOINT")
# key = os.getenv("COSMOS_DB_KEY")
# database_name = os.getenv("COSMOS_DB_NAME")
# container_name = os.getenv("COSMOS_DB_CONTAINER_FOR_HISTORIES", "StateChecklist")

# # Initialize Cosmos DB client
# client = CosmosClient(endpoint, key)
# database = client.create_database_if_not_exists(id=database_name)
container = database.create_container_if_not_exists(
    id="StateChecklist",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
) 
class States(Enum):
    CompanyInfo = "company_info"
    BuisnessProcess = "buisness_process"
    ErPPlatform = "erp_platform"
    Integration = "integration"
    DataMigration = "data_migration"
    ApplicationModule = "application_module"
    Timeline = "timline"
    CompanyDataUpdate="company_data_update"
    CostEstimation="cost_estimation"

def validation(user_id:str,state_enum:str):
    print("validating States")
    try:
        # Retrieve user's states document
        item = getUserState(user_id)
        print(item,state_enum.value)
        if item:
            print("Access the state using enum value")
            state_value = item.get(state_enum.value)
            return state_value
        else:
            print(f"No state found for user {user_id}.")
            return False
    except Exception as e:
        print(traceback.format_exc())
        print(f"Error while checking state: {str(e)}")
        return False

def getUserState(user_id):
    try:
        # Query to get the item based on user_id
        item = container.read_item(item=user_id, partition_key=user_id)
        return item
    except exceptions.CosmosResourceNotFoundError:
        print(f"State not found for user {user_id}")
        return None

def delete_item(user_id):
    """
    delete item by chat ID.
    """
    print('\n1.12 Deleting Item by Id\n')

    container.delete_item(item=user_id, partition_key=user_id)

    print('Deleted item\'s Id is {0}'.format(user_id))




def creatStates(user_id):
    #logging.info("creating new states for user {0}", user_id)
    print("creating new states for user")
    try:
        state = getUserState(user_id)
        if state :
            delete_item(user_id)
                    
        item = {
                'id':user_id,
                'user_id':user_id,
                'company_info': 'False',
                'buisness_process':'False',
                'erp_platform': 'False',
                'application_module':'False',
                'integration': 'False',
                'data_migration': 'False',
                'timeline':'False',
                'cost_estimation':'False',
                'document_generate': 'False',
                'ppt_generate': 'False',
            }
        print("State initiallized!")
        print(item)
        container.create_item(body=item)
        print("'State created successfully'")
    except Exception as e:
        print(traceback.format_exc())
        logging.error(f"Error while creating States {str(e)}")


def updatetimelineState(user_id,value):
    print("updating state company_info")
    try:
        print("intry")
        items  = getUserState(user_id)
        items['timeline'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())

def updateEstimationState(user_id,value):
    print("updating state cost_estimation")
    try:
        print("intry")
        items  = getUserState(user_id)
        items['cost_estimation'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())

def updatePresentationState(user_id,value):
    print("updating state ppt_generate")
    try:
        items  = getUserState(user_id)
        items['ppt_generate'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())
def updateDocumentState(user_id,value):
    print("updating state document_generate")
    try:
        items  = getUserState(user_id)
        items['document_generate'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())

def updateCompanyInfoState(user_id,value):
    #logging.info("updating state company_info for user {0}", user_id)
    print("updating state company_info")
    try:
        print("intry")
        items  = getUserState(user_id)
        items['company_info'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())

def updateBPState(user_id,value):
    print("updating state buisness_process")
    try:
        items :dict  = getUserState(user_id)
        items['buisness_process'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())
         
def updateERPState(user_id,value):
    print("updating state checklist erp_platform")
    try:
        items :dict  = getUserState(user_id)
        items['erp_platform'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())

def updateAppModState(user_id,value):
    print("updating state checklist application_module")
    try:
        items :dict  = getUserState(user_id)
        items['application_module'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())
         
def updateIntegrationState(user_id,value):
    print("updating state checklist integration")
    try:
        items :dict  = getUserState(user_id)
        items['integration'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())

def updateDMState(user_id,value):
    print("updating state checklist data_migration")
    try:
        items :dict  = getUserState(user_id)
        items['data_migration'] = value
        response = container.upsert_item(body=items)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())
         
def getUserState(user_id):
    print("Getting states for user")
    try:
        response = container.read_item(item=user_id, partition_key=user_id)
        print('Item read by Id {0}'.format(user_id))
        print(response)
        return response
    except Exception as e:
         print("Error" ,e)
         print(traceback.format_exc())
         print("noid")
         return None


        
    
            