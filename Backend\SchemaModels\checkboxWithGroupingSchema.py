# schema define. 
from pydantic import BaseModel, Field
from typing import List, Optional


class FieldModel(BaseModel):
    type: str = Field(description="Type of the field, e.g., Checkbox.")
    label: str = Field(description="Label for the field.")
    
    description: Optional[str] = Field(description="Description of the field.")
    id: str = Field(description="ID for the field.")
    
    group: Optional[str] = Field(description="Group category of the field.")
    isChecked: Optional[bool] = Field(False, description="Checkbox status indicating if it's checked.")



class CheckboxWithGrouping(BaseModel):
    """It will use for checkbox grouping"""
    id: str = Field(description="Chat ID")
    user_id: str = Field(description="User ID")
    
    viewType: str = Field(description="Type of view, e.g., checkboxWithGrouping")
    title: str = Field(description="Title of the form")
    
    type: str = Field(description="Type of form")
    description: str = Field(description="Description of the form.")
    
    fields: List[FieldModel] = Field(description="List of fields in the form.")
    footer: Optional[str] = Field(None, description="Footer note for additional information or guidance.")

    
class CheckBoxWithGroupingOutputSchema(BaseModel):
    output: CheckboxWithGrouping


