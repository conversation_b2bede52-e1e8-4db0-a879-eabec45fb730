from RAG.data_extraction import data_extraction, extract_text_and_tables
from RAG.chunk_index import raw_content
from RAG.summary_index import process_and_index_document
from RAG.chat_summary import generate_chat_summary, create_chat_sections_summary
from Config.blobService import blob_service_client
import io
from docx import Document
import json
import os
from RAG.retriever import summary_retriever , chunk_retriever

def get_document_from_blob(blob_name):
 
    blob_client = blob_service_client.get_blob_client(container=os.getenv("blob_rag_container_name"), blob=blob_name)
    blob_url = blob_client.url
    
    # Download the blob as a stream
    blob_data = blob_client.download_blob()
    doc_bytes = blob_data.readall()
    doc_stream = io.BytesIO(doc_bytes)
    document = Document(doc_stream)
    return document , blob_url

def upload_documents(document , blob_url):
    """
    Extract data from a document content, split it into chunks, and upload it to a search client.

    Args:
        doc_content (bytes): The content of the document file.
    """
    # Convert the byte content to a stream
   
    # Extract text and metadata from the document
    metadata = data_extraction(document , blob_url)
    print("DONE")
    text = extract_text_and_tables(document)
    
    # Process and upload the content in chunks
    raw_content(text, metadata)
    process_and_index_document(text, metadata)

def chat_summary(file_path):
    """
    Generate a summary of chat content from a text file.

    Args:
        file_path (str): The path to the chat history file.

    Returns:
        str: The generated chat summary.
    """
    _, extension = os.path.splitext(file_path)
    extension = extension.lower()
    
    
    # Handle JSON files
    if extension == '.json':
        with open(file_path, 'r', encoding='utf-8') as file:
            content = json.load(file)
            content = json.dumps(content, indent=4)  # Formatted JSON string
            
    # Handle TXT files
    elif extension == '.txt':
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

    sections = create_chat_sections_summary(content)  # Create sections from the content
    chat_summary_output = generate_chat_summary(sections)  # Generate the summary
    return chat_summary_output

def run_rag(blob_name):
    """
    Main function to execute the document processing and chat summary generation workflow.
    """
    # Process and upload the document to upload at indexer
    
    # Azure Blob Storage connection details
    """
    Main function to execute the document processing and chat summary generation workflow.
    """
    document , blob_url = get_document_from_blob(blob_name)
    # Upload and process the document content
    upload_documents(document , blob_url)

    #This can be a txt file or a json file there you have to place a chat history file
    upload_chat_summary = "C:\\Other files\\PromptEdge\\promptRAG\\HistoricalDataPOC\\chat_history_1.txt"         
    
    chat_summary_output = chat_summary(upload_chat_summary)  # Generate chat summary

    file_id = summary_retriever(chat_summary_output)  # Retrieve the summary based on the chat
    chunk_retriever(file_id)  # Retrieve detailed content related to the file ID


 
