from langchain_core.tools import tool

# from Tools.company_apollosearch import companySearchApolloTool
from Tools.company_websearch import companySearchWebTool

from LLMChains.Base_LLMChain_ModelClass import BaseLLMChainClass
from StructuredSchemas.company_extraction_schema import CompanyStructureModel
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from DatabaseCosmos.StatesChecklist import updateCompanyInfoState,validation,States
from DatabaseCosmos.Company_info_State import insert_Companyinfo_State,read_CompanyInfo, updateCompanyInfo
from Tools.suggestPills import getSuggestedPills


company_extraction_system_prompt = """
                You will be provided the result from web in text format and 
                also provided search from apollo tool in json text format.
                Read the given results precisely.
                Transform the given results into the structured output provided.
                """
company_extraction_chain = BaseLLMChainClass(
                                            company_extraction_system_prompt, 
                                            CompanyStructureModel
                                            )

@tool(return_direct=True)
def companySearchCombineTool(company_name:str, user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg])-> str:
    """Search company using Apollo. Give details like What is the company type, its major clients, product type, email id, phone number, Number of employees, headquarters address, company revenue, main products, buisness module, headquarters address, subsidiaries, industry name and type, SIC_Code, its linkedin account link, website link and any other information avaiable."""
    
   
    bool_state=validation(user_id=user_id,state_enum=States.CompanyInfo)
    # if(bool_state == "True"):
    #     print("State already Completed!")
    #     # response=AlreadyExistInformation(id=id,user_id=user_id)
    #     return response
        
        
    # else:           
        
    #Combine Search Starts Here
    web_search_results = companySearchWebTool.invoke(company_name)
    # apollo_search = companySearchApolloTool.invoke(company_name)
    company_data = company_extraction_chain.structure({
                                "input": f"""Web Search Results in text format: {str(web_search_results)}"""

                                            # -----------------------------------------------

                                            # Apollo Serach Results in json format: {str(apollo_search)}"""
                                })

    # company_data = dict(dict(response)["output"])
    print("--- Searched company data: ",company_data)

    response=DataNotExisted(id=id,user_id=user_id,company_data=company_data)
    return response

    

    


def AlreadyExistInformation(id,user_id):
    company_data=read_CompanyInfo(chat_id=id)
    dictionary  = company_data

    print("company infor",dictionary)
      

    company_name = dictionary["companyname"]
    company_information = dictionary["companyinformation"]
    #industry = dictionary["industry"]["name"]
    headquarters_address = dictionary["headquarters_address"]
    employees = dictionary["employees"]
    #corporate_structure = dictionary["corporate_structure"]
    #sic_code=dictionary["industry"]["SIC_Code"]
    #type_of_manufac=dictionary["industry"]["type_of_manufacturing"]
    subsidiaries_and_brands = dictionary["subsidiaries_and_brands"]

    response= json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "companyinfo",
                        "title": company_name,
                        "content": company_information,
                        "industry": dictionary["industry"],
                        "SIC_Code":dictionary["SIC_Code"],
                        "type_of_manufacturing":dictionary["type_of_manufacturing"],
                        "headquarters_address":headquarters_address,
                        "employees":employees,
                        "corporate_structure":dictionary["corporate_structure"]["ownership"],
                        "subsidiaries_and_brands": subsidiaries_and_brands
                    })
    return response


       


def DataNotExisted(id,company_data,user_id):
    
    
    # checking if any company search data existed for the given user
    existing_data=read_CompanyInfo(chat_id=id)
    old_dictionary  = existing_data
    
    
    dictionary = company_data
    
    dictionary["industry"] = {
        "name": company_data["industry_name"],
        "SIC_Code": company_data["SIC_Code"],
        "type_of_manufacturing": company_data["type_of_manufacturing"]
    }

    dictionary["corporate_structure"] = company_data["corporate_structure_ownership"]
    print(company_data["corporate_structure_ownership"])
    dictionary["employees"] = str(dictionary["employees"])

    del dictionary["industry_name"]
    del dictionary["SIC_Code"]
    del dictionary["type_of_manufacturing"]
    del dictionary["corporate_structure_subsidiary_of"]
    del dictionary["corporate_structure_ownership"]

    print("----------------------------------------- dictionary", dictionary)

    company_name = dictionary["company_name"]
    company_information = dictionary["company_information"]
    industry = dictionary["industry"]["name"]
    headquarters_address = dictionary["headquarters_address"]
    employees = dictionary["employees"]
    corporate_structure = dictionary["corporate_structure"]["ownership"]
    sic_code=dictionary["industry"]["SIC_Code"]
    type_of_manufac=dictionary["industry"]["type_of_manufacturing"]
    subsidiaries_and_brands = dictionary["subsidiaries_and_brands"]


    
    # if there is no previous company data found in DB then insert the data into DB
    if not old_dictionary :
        print("here inserting in to company----------------------------------------------------------------")
        db_response = insert_Companyinfo_State(
            chat_id=id,
            user_id=user_id,
            company_name=company_name,
            company_information=company_information,
            industry=industry,
            SIC_code=sic_code,
            type_of_manufacturing=type_of_manufac,
            headquarters_address=headquarters_address,
            employees=employees,
            corporate_structure=corporate_structure,
            subsidiaries_and_brands=subsidiaries_and_brands
        )
        print("Db write completed", db_response)

    # if company data found in the DB against the provided chat_id then update the company data into the DB    
    else:
        print("--------------- in data existed , updating the data : ", company_data)
        db_response = updateCompanyInfo(chat_id=id,
            user_id=user_id,
            company_name=company_name,
            company_information=company_information,
            industry=industry,
            SIC_code=sic_code,
            type_of_manufacturing=type_of_manufac,
            headquarters_address=headquarters_address,
            employees=employees,
            corporate_structure=corporate_structure,
            subsidiaries_and_brands=subsidiaries_and_brands)
        
        print("Db company data update completed", db_response)

    #^^^^^^^^^^^^^^^^^^^^^^^^ Database Work Here ^^^^^^^^^^^^^^^^^^^^^^^^^
    #Intitializing Company Search State in DB True
    print("updating Company info state to true in db -----------------------------------")
    updateCompanyInfoState(user_id=user_id,value="True")

    # reading current chat from db
    UserChatHistory = read_userConversation(id)
    print("This is current user chat history :-------------------,",UserChatHistory['History'])
    # #upserting assistance response into db
    # UserChatHistory['History'].append({"role": "assistant", "content": company_data})
    UserChatHistory['History'].append({"role": "tool", "content": "company_info"})
    upsert_userConversation(UserChatHistory)
    #^^^^^^^^^^^^^^^^^^^^^^^^ Database Work End Here ^^^^^^^^^^^^^^^^^^^^^^^^^
    
    # get suggested pills 
    suggested_pills= getSuggestedPills(user_id)
    
    response= json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "companyinfo",
                        "title": company_name,
                        "content": company_information,
                        "industry": dictionary["industry"]["name"],
                        "SIC_Code":dictionary["industry"]["SIC_Code"],
                        "type_of_manufacturing":dictionary["industry"]["type_of_manufacturing"],
                        "headquarters_address":headquarters_address,
                        "employees":employees,
                        "corporate_structure":dictionary["corporate_structure"]["ownership"],
                        "subsidiaries_and_brands": subsidiaries_and_brands,
                        
                        "pills": suggested_pills
                    })
    return response


