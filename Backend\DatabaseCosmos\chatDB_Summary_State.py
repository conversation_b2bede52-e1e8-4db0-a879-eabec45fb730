import logging
import random
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from utilities.CreateGuid import generate_guide
from Config.cosmosdb import database
from DatabaseCosmos.UpdatingCheckboxAndCheckboxWithGrouping import upsertUserSelectedData
# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="chatDB_Summary",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
)

def initialize_chatDB(user_id: str, id: str):
    logging.info(f"Initializing ChatDB with user_id: {user_id} and chat_id: {id}")
    print("Initializing ChatDB")
    try:
        # Check if the item already exists
        existing_item = getChatDB(id)
        if existing_item:
            print("Item already exists")
            return existing_item
       
        # guid=generate_guide()
        item = {
            # 'id': guid,
            'user_id': user_id,
            'id': id,
            'createdAt': datetime.utcnow().isoformat(),
            'updatedAt': datetime.utcnow().isoformat(),
            'ifChatSectionExists': False,
            'chatSections': [],
            'ifChatSummaryExists': False,
            'chatSummary': "",
            'ifSimilaritySearchFileIdsExists': False,
            'similarFileIds': [],
        }
        container.create_item(body=item)
        print(f'Created new item on {id}')
        return item
        
    except Exception as e:
        print("Error" ,e)
        
        print("Read Failed!")
        return None 
    
def getChatDB(id: str):
    print("Reading ChatDB")
    try:
        logging.info(f"Querying chat_id: {id} from ChatDB")
        # query = f"SELECT * FROM c WHERE c.user_id = '{user_id}' AND c.id = '{id}'"
        query = f"SELECT * FROM c WHERE c.id = '{id}'"
        
        items = container.query_items(query=query, enable_cross_partition_query=True)
        
        item_list = list(items)
        if item_list:
            print(f"Retrieved item: {item_list[0]}")
            return item_list[0]
        else:
            print("No item found")
            return None
    
    except Exception as e:
        print("Error", e)
        print("Read Failed!")
        return None
    
#update chat section
def updateChatSection(id: str,chatSections: list):
    print("Updating Chat Section")
    try:
        logging.info(f"Updating chatSections for chat_id: {id}")
        item = getChatDB(id)
        if item:
            if item['ifChatSectionExists'] == False:
                item['ifChatSectionExists'] = True
                item['chatSections'] = chatSections
                item['updatedAt'] = datetime.utcnow().isoformat()
                response = container.upsert_item(body=item)
                print(f'Upserted Item\'s Id is {response["id"]}')
                return response
            else:
                print("Chat Section already exists")
                return None
        else:
            print("Item not found")
            return None
        
    except Exception as e:
        print("Error", e)
        print("Upsert Failed!")
        return None

#update chat summary
def updateChatSummary(id: str,chatSummary: str):
    print("Updating Chat Summary")
    try:
        logging.info(f"Updating chatSummary for chat_id: {id}")
        item = getChatDB(id)
        if item:
            if item['ifChatSummaryExists'] == False:
                item['ifChatSummaryExists'] = True
                item['chatSummary'] = chatSummary
                item['updatedAt'] = datetime.utcnow().isoformat()
                response = container.upsert_item(body=item)
                print(f'Upserted Item\'s Id is {response["id"]}')
                return response
            else:
                print("Chat Summary already exists")
                return None
        else:
            print("Item not found")
            return None
        
    except Exception as e:
        print("Error", e)
        print("Upsert Failed!")
        return None
    
#update similarity search file ids
def updateSimilaritySearchFileIds(id: str,similarFileIds: list):
    print("Updating Similarity Search File Ids")
    try:
        logging.info(f"Updating similarFileIds for chat_id: {id}")
        item = getChatDB(id)
        if item:
            if item['ifSimilaritySearchFileIdsExists'] == False:
                item['ifSimilaritySearchFileIdsExists'] = True
                item['similarFileIds'] = similarFileIds
                item['updatedAt'] = datetime.utcnow().isoformat()
                response = container.upsert_item(body=item)
                print(f'Upserted Item\'s Id is {response["id"]}')
                return response
            else:
                print("Similarity Search File Ids already exists")
                return None
        else:
            print("Item not found")
            return None
        
    except Exception as e:
        print("Error", e)
        print("Upsert Failed!")

#delete chatDB
def deleteChatDB(id: str):
    print("Deleting ChatDB")
    try:
        logging.info(f"Deleting chat_id: {id} from ChatDB")
        item = getChatDB(id)
        if item:
            container.delete_item(item, partition_key=id)
            print(f'Deleted item with Id {id}')
            return item
        else:
            print("Item not found")
            return None
        
    except Exception as e:
        print("Error", e)
        print("Delete Failed!")
        return None
