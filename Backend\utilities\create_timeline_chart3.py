import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from io import BytesIO

def create_gantt_chart(data):

    # Convert Start and End into weeks (W1, W2, ...)
    phases = [d['Phase'] for d in data]
    start_weeks = [d['Start'] for d in data]
    end_weeks = [d['End'] for d in data]

    y_pos = np.arange(len(phases))

    fig, ax = plt.subplots(figsize=(10, 5))
    ax.barh(y_pos, [end - start for start, end in zip(start_weeks, end_weeks)], 
            left=start_weeks, color='skyblue', edgecolor='black')

    # Formatting x-axis labels as W1, W2, ...
    ax.set_xticks(range(1, max(end_weeks) + 1))
    ax.set_xticklabels([f'W{i}' for i in range(1, max(end_weeks) + 1)])
    ax.set_yticks(y_pos)
    ax.set_yticklabels(phases)
    ax.invert_yaxis()

    plt.xlabel("Weeks")
    plt.ylabel("Phases")
    plt.title("Timeline")
    plt.grid(axis='x', linestyle=':', alpha=0.7)

    # show image
    # plt.show()

    # Save the plot as a PNG image for docs and frontend
    img_stream = BytesIO()
    plt.savefig(img_stream, format='png') 
    img_stream.seek(0)  

    #How to retrieve image_stream as bytes
    img_bytes = img_stream.getvalue()
    
    return img_bytes


def get_ganttchart_data(df):

    # Select columns that start with 'W' followed by a number and are not empty
    df = df.filter(regex=r"^W\d+$")
    df = df.reset_index(drop=True)

    # Extract phase names (first row)
    phase_names = df.iloc[0]

    # Extract hours (excluding first row), ensuring only numeric rows are used
    hours_data = df.iloc[1:].apply(pd.to_numeric, errors='coerce').fillna(0).astype(int)

    # Initialize result list
    phases = {}

    for col in df.columns:
        phase = phase_names[col]  # Get phase name
        week_num = int(col[1:])  # Convert "W1" to 1, "W2" to 2, etc.
        total_hours = hours_data[col].sum()
        
        if phase not in phases:
            phases[phase] = {"weeks": [], "total_hours": 0}
        
        phases[phase]["weeks"].append(week_num)
        phases[phase]["total_hours"] += total_hours

    # Convert to DataFrame for plotting
    data = []
    for phase, info in phases.items():
        data.append({
            "Phase": phase,
            "Start": min(info["weeks"]),
            "End": max(info["weeks"]) + 1,  # Ensure the bar spans the full week
            "Total Hours": info["total_hours"]
        })

    return data