import os
from dotenv import load_dotenv
import pandas as pd
# from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
import json

from LLMChains.Base_LLMChain_ModelClass import <PERSON><PERSON><PERSON><PERSON>nC<PERSON>
from StructuredSchemas.integrations_extraction_schema import ISVStructureModel
from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.ERP_State import read_ERPState
 
from utilities.get_integrations import get_integrations
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
 
from Config.azureAI import llm
 
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated

from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from DatabaseCosmos.StatesChecklist import updateIntegrationState
from Tools.suggestPills import getSuggestedPills


# Load environment variables from .env file
load_dotenv()
 
# llm = AzureChatOpenAI(
#     azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
#     azure_deployment=os.getenv("OPEN_AI_MODEL_NAME"),
#     openai_api_version=os.getenv("AZURE_API_VERSION"),
# )
# print("AzureChatOpenAI Initilized...")
 
# Load the local CSV file with ID
integrations_df = pd.read_csv(r"./Data/Integrations.csv", encoding='utf-8')
print("pandas readed")
 
integrations_extraction_system_prompt = f"""
                You are provided a company's data
                And also provided a list of ISVs used for Microsoft Dynamics Business solumtions given in 'Integrations'
               
                Suggest the ISVs suitable according to the company
                Read the given company data precisely, then suggest some ISVs
 
                Remeber to return just the ISV IDs
                Transform the given suggestion into the structured output provided.
 
                Here is a list of available ISVs with their groups, sub groups and IDs:
                Integrations :  {integrations_df.to_string(index=False)}
                """
integrations_extraction_chain = BaseLLMChainClass(
                                            integrations_extraction_system_prompt,
                                            ISVStructureModel
                                            )
 
@tool(return_direct=True)
def suggest_integrations(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is used to recommend or suggest the integrations or ISVs"""

    # id = "20241106190337-be0e9567ef2d"
    print("here is the id", id)
    company_info = read_CompanyInfo(id)
    
    if company_info:

        company_input = f"""
                        Company Name: {company_info['companyname']}
                        Company Information: {company_info['companyinformation']}
                        Number of Employees: {company_info['employees']}
                        Headquarters Location: {company_info['headquarters_address']}
                        Industry: {company_info['industry']}
                    """

        # Invoke the LLM with the prompt
        response = integrations_extraction_chain.structure({"input": company_input})
        print(response)
        isv_list = get_integrations(response["isv_id"])

    else:
        
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "errorkey": "companyNameNotFound"
        })
        # Handle case where company_info is None or empty
        isv_list = get_integrations([])

    # Insert into the database
    # insert = insert_BP_State(chat_id=id, bp_list=bp_list, user_id=user_id)
    # print("insertion into db is completed")
    # print(insert)

    print("here is the ISVs list", isv_list)
    
    
    
    #Intitializing Company Search State in DB True
    print("updating BP state to true in db -----------------------------------")
    updateIntegrationState(user_id=user_id,value="True")
    
    ### chat history
    # reading current chat from db
    UserChatHistory = read_userConversation(id)
    print("This is current user chat history :-------------------,",UserChatHistory['History'])
    # #upserting assistance response into db
    # UserChatHistory['History'].append({"role": "assistant", "content": company_data})
    UserChatHistory['History'].append({"role": "tool", "content": "integration"})
    upsert_userConversation(UserChatHistory)
    
    # get suggested pills 
    suggested_pills= getSuggestedPills(user_id)
    

    return json.dumps({
        "id": id,
        "user_id": user_id,
        "viewType": "integration",
        "title": f"{company_info['companyname']} -> Integrations" if company_info else "Integrations",
        "type": "checkboxWithTextGrouping",
        "description": f"This form allows you to select the relevant ISVs for {company_info['companyname']}'s Dynamics 365 implementation." if company_info else "This form allows you to select relevant ISVs.",
        "fields": isv_list,
        
        "pills": suggested_pills
    })

 
# # # Call the function and print the result
# result = suggest_business_processes(company_info)
# print(result)