# schema define. 
from pydantic import BaseModel, Field
from typing import List, Optional


class FieldModel(BaseModel):
    type: str = Field(description="Type of the field, e.g., Checkbox.")
    label: str = Field(description="Label for the field.")
    description: Optional[str] = Field(description="Description of the field.")
    id: str = Field(description="ID for the field.")
    # isChecked: Optional[bool] = Field(False, description="Whether the checkbox is checked or not.")


class Checkbox(BaseModel):
    """It will use to for checkbox"""
    id: str = Field(description="Chat ID")
    user_id: str = Field(description="User ID")
    viewType: str = Field(description="Category of the view type.")
    title: str = Field(description="Name of the Company + state, where it is")
    
    type: str = Field(description="Type of form")
    description: str = Field(description="Description of the form.")
    
    fields: List[FieldModel] = Field(description="List of fields in the form.")

    
    
class CheckBoxOutputSchema(BaseModel):
    output: Checkbox


