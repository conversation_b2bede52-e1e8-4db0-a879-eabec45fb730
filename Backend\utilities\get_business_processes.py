import pandas as pd
df = pd.read_csv(r"./Data/BusinessProcesses.csv", encoding='utf-8')

def get_business_processes(bp_id_list: list):
    
    # Initialize the result list
    result = []
    
    # Iterate over each row in the dataframe
    for _, row in df.iterrows():
        # Check if the current ID is in the bp_id list
        is_checked = True if row['ID'] in bp_id_list else False
        print("ischeckedddd----------",is_checked)
        # Create the object with the required structure
        bp_object = {
            "type": "Checkbox",
            "label": row['Business Process'],
            "description": row['Purpose'],
            "id": row['ID'],
            "isChecked": is_checked
        }
        
        # Append to the result list
        result.append(bp_object)

    # print(result)
    
    return result

# # Usage
# bp_id_list = ['BP003', 'BP004', 'BP007', 'BP008', 'BP012']
# business_processes = get_business_processes(bp_id_list)
# print(business_processes)
