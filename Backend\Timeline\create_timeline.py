import pandas as pd
import math
import numpy as np
import io

def create_resource_timeline(build_hours, resources):
    # Calculate actual hours for each resource (percentage * 40)
    for resource in resources:
        resource['weekly_hours'] = resource['hours'] * 40

    # Initialize DataFrame with just the Phase column first
    df = pd.DataFrame(index=[r['role'] for r in resources], columns=['Role'])
    
    # Fill Phase column
    for resource in resources:
        df.at[resource['role'], 'Role'] = resource['role']
    
    # Initialize remaining hours and week counter
    remaining_hours = build_hours
    current_week = 0
    week_columns = []
    
    # Allocate hours week by week until remaining hours are depleted
    while remaining_hours > 0:
        week_col = f'Week {current_week + 1}'
        week_columns.append(week_col)
        
        # Add new week column
        df[week_col] = 0
        
        for resource in resources:
            # Calculate hours for this week
            hours_to_allocate = min(resource['weekly_hours'], remaining_hours)
            df.at[resource['role'], week_col] = hours_to_allocate
            remaining_hours -= hours_to_allocate
            
            if remaining_hours <= 0:
                break
                
        current_week += 1
    
    # Add 'Build' as the first row
    build_row = pd.Series([''] + ['Build'] * len(week_columns) + [None], index=['Role'] + week_columns + ['Total Hours'])
    df = pd.concat([pd.DataFrame([build_row]), df])
    
    # Add Total Hours column
    df['Total Hours'] = df[week_columns].sum(axis=1)
    # df = df.drop(columns=['Role'])
    
    return df, current_week

def create_df_from_phase(phase_object, resources):
    weeks = phase_object['weeks']

    # Calculate actual hours for each resource (percentage * 40)
    for resource in resources:
        resource['weekly_hours'] = resource['hours'] * 40
    # Create DataFrame
    df = pd.DataFrame(index=[r['role'] for r in resources], columns=['Role'])
    # Fill Phase column
    for resource in resources:
        df.at[resource['role'], 'Role'] = resource['role']
    df.head()

    current_week = 0
    week_columns = []

    # Allocate hours week by week until remaining hours are depleted
    for week in range(weeks):
        week_col = f'Week {week + 1}'
        week_columns.append(week_col)
        
        # Add new week column
        df[week_col] = 0
        
        for resource in resources:
            # Calculate hours for this week
            hours_to_allocate = max(resource['weekly_hours'], 0)
            df.at[resource['role'], week_col] = hours_to_allocate
                
        current_week += 1
    
    # Add 'Build' as the first row
    build_row = pd.Series([''] + [phase_object['name']] * len(week_columns) + [None], index=['Role'] + week_columns + ['Total Hours'])
    df = pd.concat([pd.DataFrame([build_row]), df])
    
    df = df.drop(columns=['Role', 'Total Hours'])
        
    return df

def create_df(build_hours, resources):
    # create build weeks and its DF
    df, build_weeks = create_resource_timeline(build_hours, resources)
    timeline_df = df

    #calulate weeks for each phase
    initialization_week = 1
    solution_modeling_week = math.ceil(0.2*build_weeks)
    solution_testing_week = math.ceil(0.2*build_weeks)
    deployment_week = 2
    support_week = 2 

    # create object for each phase
    phases = [
        {
            "name": "Initialization",
            "weeks": initialization_week,
        },
        {
            "name": "Solution Modeling",
            "weeks": solution_modeling_week,
        },
        {
            "name": "Solution Testing",
            "weeks": solution_testing_week,
        },
        {
            "name": "Deployment",
            "weeks": deployment_week,
        },
        {
            "name": "Support",
            "weeks": support_week,
        },
    ]

    # create DF for each phase
    dfs = []
    for phase in phases:
        df = create_df_from_phase(phase, resources)
        dfs.append({
            "phase": phase['name'],
            "df": df,
        })

    # add build phase to the list
    timeline_df.insert(0, 'Phase', 'Build')
    role_column = timeline_df.pop('Role')
    timeline_df = timeline_df.drop(columns=['Phase', 'Total Hours'])

    dfs.append({
        "phase": "Build",
        "df": timeline_df,
    })

    # merge all according to the flow
    merged_flow = ["Initialization", "Solution Modeling", "Build", "Solution Testing", "Deployment", "Support"]
    merged_df = pd.DataFrame()
    for phase in merged_flow:
        for df in dfs:
            if df['phase'] == phase:
                merged_df = pd.concat([merged_df, df['df']], axis=1)

    # add role_column into first column of merged column
    merged_df.insert(0, 'Role', role_column)

    # Optional: Reset column names to generic names 
    headers = ["Role"] + [f'W{i}' for i in range(1, len(merged_df.columns))]
    merged_df.columns = headers



    # Convert all columns except 'Role' to numeric, coerce errors to NaN
    merged_df.iloc[1:, 1:] = merged_df.iloc[1:, 1:].apply(pd.to_numeric, errors='coerce')

    # Add last column in merged_df named 'Total Hours' and sum all the numeric values in each row
    merged_df['Total Hours'] = merged_df.iloc[1:, 1:].sum(axis=1)


    
    # add a column named 'Rate' and fill it with the rate of each role from resources object having key 'rate'
    merged_df['Rate'] = 0
    for resource in resources:
        merged_df.loc[merged_df['Role'] == resource['role'], 'Rate'] = resource['rate']

    # add a column named 'Total Cost' and fill it with the product of 'Rate' and 'Total Hours'
    merged_df['Total Cost'] = merged_df['Rate'] * merged_df.filter(like='W').sum(axis=1)

    # Convert 'Total Cost' column to numeric
    merged_df['Total Cost'] = pd.to_numeric(merged_df['Total Cost'], errors='coerce')


    # sum the Total Hours column
    merged_df.loc['Total', 'Total Hours'] = merged_df['Total Hours'].sum()

    # sum the Total Hours column
    merged_df.loc['Cost', 'Total Cost'] = merged_df['Total Cost'].sum()



    # Move 'Total Hours' column to the 2nd position
    cols = merged_df.columns.tolist()
    cols.insert(1, cols.pop(cols.index('Total Hours')))
    merged_df = merged_df[cols]

    # move 'Rate' column to the 3rd position
    cols = merged_df.columns.tolist()
    cols.insert(2, cols.pop(cols.index('Rate')))
    merged_df = merged_df[cols]

    # move 'Total Cost' column to the 4th position
    cols = merged_df.columns.tolist()
    cols.insert(3, cols.pop(cols.index('Total Cost')))
    merged_df = merged_df[cols]

    return merged_df
