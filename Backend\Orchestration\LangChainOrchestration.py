from json import tool
from Agents.proposalCreation_Agent2 import proposalCreation_llm_with_tools, proposalCreation_tools
from Config.azureAI import llm
from copy import deepcopy
from langchain_core.runnables import chain
import json 
from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from utilities.utils import convert_to_langchain_format

def executeAgent(query:str, user_id:str, id:str, chat_history: dict):

    response = {
        "input" : query,
        "id": id,
        "user_id": user_id,
        "response": []
    }

    UserChatHistory = read_userConversation(id)
    if UserChatHistory is None:
        UserChatHistory = {
            "id": id,
            "user_id": user_id,
            "History": []
        }

    print("original",UserChatHistory['History'])
    last_message = []
    if UserChatHistory['History'] and len(UserChatHistory['History']) > 0:
        last_message = UserChatHistory['History'][-5:] if len(UserChatHistory['History']) >= 5 else UserChatHistory['History'] 
        print("This is current user chat history :-------------------,",last_message)
         

    messages = convert_to_langchain_format(last_message + [{"role": "user", "content": query}])
    print("This is after user chat history :-------------------,",messages)
    
    @chain
    def inject_user_id(ai_msg):
        tool_calls = []
        for tool_call in ai_msg.tool_calls:
            tool_call_copy = deepcopy(tool_call)
            tool_call_copy["args"]["user_id"] = user_id
            tool_call_copy["args"]["id"] = id
            tool_calls.append(tool_call_copy)
        return tool_calls

    tool_map = {tool.name: tool for tool in proposalCreation_tools}


    @chain
    def tool_router(tool_call):
        return tool_map[tool_call["name"]]


    proposalCreationToolChain = proposalCreation_llm_with_tools | inject_user_id | tool_router.map()

    tool_msg = proposalCreationToolChain.invoke(messages)

    print(f"message from agent: {tool_msg}.")

    if len(tool_msg)<=0:
        print("normal conversation")

        system_prompt = """
Instructions:
- You are an AI Agent who helps user in answering general questions and any questions related to ERP system they want to implement.
- User might ask you to add information, this means that they want to add information in the word document which will be created not by but by another agent.
- If user asks you to add any information, you can help them out.
- If user's question/input is ambigous then ask for clarity before answering.
"""
        messages = convert_to_langchain_format([{"role": "system", "content": system_prompt}] + chat_history + [{"role": "user", "content": query}])
        ai_msg = llm.invoke(messages)
        response['response'].append(
                                {
                                    "viewType": "simplechat",
                                    "content": str(ai_msg.content),
                                    }
                            )          
        
        UserChatHistory = read_userConversation(id)
        print("This is current user chat history :-------------------,",UserChatHistory['History'])
        UserChatHistory['History'].append({"role": "assistant", "content": str(ai_msg.content)})
        upsert_userConversation(UserChatHistory) 
        
    else:
        print("tool call coversation")
        print("Tool" , tool)
        for tool_response in tool_msg:
            tool_response = json.loads(tool_response.content)
            print("tool call content"  , tool_response)

            response['response'].append(tool_response)
        
        UserChatHistory = read_userConversation(id)
        if UserChatHistory:
            print("Tool View Type",tool_response['viewType'])
            if tool_response['viewType'] == "simplechat":
                UserChatHistory['History'].append({"role": "assistant", "content":tool_response['content'],"response": tool_response['content']})
            else:
                UserChatHistory['History'].append({"role": "Tool", "content":tool_response["tool_name"],"response": tool_response})
            upsert_userConversation(UserChatHistory)
    
    return json.dumps(response)
