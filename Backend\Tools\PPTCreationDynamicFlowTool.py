from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import io

from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.Template import read_Template
from DatabaseCosmos.UserCurrentChat import read_userConversation, upsert_userConversation
from utilities.upload_file import upload_file_to_blob
from utilities.fetchChat import fetchChatSummary
from utilities.default_template import ppt_template
from utilities.ppt_generation_chains import all_chains, paragraph_chain, table_chain

# from langchain.memory import ConversationBufferMemory
from Config.azureAI import llm
from langchain_core.prompts import ChatPromptTemplate
import re
from pptx import Presentation
from pptx.util import Pt, Inches
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.text import MSO_ANCHOR
import datetime

import os

file_path1 = "./src/presentation/QuisitiveTemplate/Slide1.JPG"
file_path2 = "./src/presentation/QuisitiveTemplate/Slide2.JPG"
file_path3 = "./src/presentation/QuisitiveTemplate/Slide3.JPG"
file_path4 = "./src/presentation/QuisitiveTemplate/Slide4.JPG"

# blob_name = "PPT3_Template.pptx"

document_creation_template = """
Task:
You are tasked with creating a document based on a provided Template Guide and Context. Follow these instructions carefully to generate a well-structured and coherent document.

Key Points to Remember:
1) Template-Based Structure:
- Use the headings, format, and flow outlined in the Template Guide.
- Ensure tables are used wherever the Template Guide specifies "table" or "tabular form."
2) Lists:
- Avoid numbering items in lists. Use bullet points instead for clarity.
3) Additional Content:
- Include any relevant details or instructions mentioned in the Chat History (as provided in the Context) under a dedicated section labeled Out of Scope or as specified in the Template Guide.
4) Image URLs:
- Do not include or reference any image URLs in the document.

Details Provided:

Date: {date}
Template Guide:
'''{template_guide}'''
"""

document_creation_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", document_creation_template),
                ("human", "Context: '''{context}'''")
            ]
        )

document_creation_chain = document_creation_prompt | llm

@tool(return_direct=True)
def createDynamicFlowPPT_Tool(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is use to generate Presentation and wil return you the url to dowload the Presentation, Call this tool when User asked to create Presentation or ppt"""

    ppt_url = ""
    docx_url = ""

    UserChatHistory = read_userConversation(id)
    UserChatHistory['History'].append({"role": "tool", "content": "createDynamicFlowPPT_Tool"})
    upsert_userConversation(UserChatHistory)

    document_type = "company_name"
    company_info = read_CompanyInfo(id)
    if company_info:
        document_type = company_info['companyname']
    else:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "errorkey": "companyNameNotFound"
        })
        
    msg_summary = fetchChatSummary(id=id)
    if msg_summary == "":
        msg_summary = "Want to create a SOW for the company: "

    prs = Presentation("./DocumentGeneration/Presentation/Quisitive/slideLayouts/paragraph.pptx")
        
    # print("Conversational History...", msg_summary)
    
    template_guide = ppt_template
    # getTemplate = read_Template(user_id)
    # if getTemplate:
    #     template_guide = getTemplate['template']
    # print("Templated provided...",template_guide)

    print("Generating Document....")
    response = document_creation_chain.invoke({
                    "context": msg_summary, 
                    "date": str(datetime.datetime.now()),
                    "template_guide": template_guide,
                    })
    # response = str(response.content).encode('utf-8')
    # response = response.content
    # print("Genration Response...",response)

    # all_responses = ""
    # for chain in all_chains:
    #     response = chain.structure({
    #                 "context": msg_summary, 
    #                 })
    #     all_responses += response.content

    ############################################################################# structured tooling
    paragraph_response = paragraph_chain.structure({
                    "input": response.content, 
                    })
    prs_paragraph = create_paragraph(paragraph_response['slideObjects'], prs)

    response_table = table_chain.structure({
                    "input": response.content, 
                    })
    prs_table = create_table(response_table['slideObjects'], prs_paragraph)
    prs = prs_table
    #############################################################################
    
    # response = all_responses
    # Initialize presentation
    # prs = Presentation()

    # # Parse the template
    # sections = parse_template(response)

    # # Generate slides based on sections
    # for section, content in sections:
    #     # print(section)
    #     if section == 'Title':
    #         add_title_slide(prs, content)
    #     if section == 'Table':
    #         headers = [header.strip() for header in content['headers']]
    #         rows = [[cell.strip() for cell in row] for row in content['rows']]
    #         add_table_slide(prs, 'Table', headers, rows)
    #     else:
    #         add_content_slide(prs, section, content)

    # # Save the presentation
    # slide_layout = prs.slide_layouts[-1]
    # slide = prs.slides.add_slide(slide_layout)
    # set_slide_background_color(slide, file_path4,prs)

    # Save the document
    file_path = f"{user_id}/{id}/{document_type}_presentation.pptx"

    # Save the rendered document to an in-memory stream
    output_stream = io.BytesIO()
    prs.save(output_stream)
    output_stream.seek(0)  # Reset stream position for uploading

    #generating sas url
    print("generating PPT URL....")
    ppt_url = upload_file_to_blob(
                                blob_name=file_path,
                                output_stream=output_stream
                                )

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "documentcreation",
                        "url": {
                            "ppt_url" : ppt_url,
                            "docx_url": docx_url,
                        }
                    }
                )

# Function to add title slide with background color
def add_title_slide(prs, title):
    
    slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(slide_layout)
    set_slide_background_color(slide, file_path1,prs)  # Light lavender background
    slide.shapes.title.text = title
    

def add_content_slide(prs, title, content):
    # Define the maximum length for each slide's content
    max_length = 800  # Adjust as needed based on your content and font size
   
    # Split content into chunks if it exceeds max_length
    content_chunks = []
    while len(content) > max_length:
        split_index = content.rfind('\n', 0, max_length)  # Split at last newline within max_length
        if split_index == -1:
            split_index = max_length  # If no newline found, split at max_length
        content_chunks.append(content[:split_index].strip())
        content = content[split_index:].strip()
    content_chunks.append(content)  # Add remaining content
 
    # Loop over chunks to create multiple slides if needed
    for i, chunk in enumerate(content_chunks):
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)
        set_slide_background_color(slide, file_path3, prs)  # Light gray background
       
        # Update title with numbering if there are multiple slides
        if len(content_chunks) > 1:
            slide_title = f"{title} ({i + 1})"
        else:
            slide_title = title
        slide.shapes.title.text = slide_title
 
        # Add content to the placeholder and format
        text_placeholder = slide.placeholders[1]
        text_placeholder.text = clean_markdown(chunk)
       
       
        for paragraph in text_placeholder.text_frame.paragraphs:
            if ':' in paragraph.text:
                heading, text = paragraph.text.split(':', 1)
                paragraph.clear()  # Clear the existing text
                run = paragraph.add_run()
                run.text = heading.strip() + ': '
                run.font.bold = True
                run = paragraph.add_run()
                run.text = text.strip()
            paragraph.font.size = Pt(18)
            paragraph.alignment = PP_ALIGN.LEFT

# Updated function to add table slide with support for multi-slide splitting
def add_table_slide(prs, title, headers, rows):
    slide_layout = prs.slide_layouts[5]
    max_rows_per_slide = 8  # Adjust this based on your slide layout
    
    for i in range(0, len(rows), max_rows_per_slide):
        # Create a new slide
        slide = prs.slides.add_slide(slide_layout)
        set_slide_background_color(slide, file_path3, prs)  # Light gray background
        
        # Update title with part number
        part_number = i // max_rows_per_slide + 1
        slide.shapes.title.text = f"{title} (Part {part_number})"
        
        # Define table dimensions
        table = slide.shapes.add_table(
            min(len(rows) - i, max_rows_per_slide) + 1,  # Rows on this slide + header
            len(headers), 
            Inches(1.2), Inches(1.5), Inches(8), Inches(1.5)
        ).table
        
        # Add header row
        for col, header in enumerate(headers):
            cell = table.cell(0, col)
            cell.text = header.strip()
            cell.text_frame.paragraphs[0].font.bold = True
            cell.text_frame.paragraphs[0].font.size = Pt(12)
            cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
            cell.fill.solid()
            cell.fill.fore_color.rgb = RGBColor(200, 200, 200)
        
        # Add data rows for this slide
        chunk_rows = rows[i:i + max_rows_per_slide]
        for row_idx, row_data in enumerate(chunk_rows, 1):
            for col_idx, cell_data in enumerate(row_data):
                cell = table.cell(row_idx, col_idx)
                cell.text = clean_markdown(cell_data.strip())
                cell.text_frame.paragraphs[0].font.size = Pt(12)
            

# Function to clean markdown syntax
def clean_markdown(text):
    # text = text.encode('utf-8')
    text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # Remove bold asterisks
    text = re.sub(r'\*(.*?)\*', r'\1', text)  # Remove italic asterisks
    text = re.sub(r'\-', '', text)  # Remove hyphens
    # print(text)
    return text


# Function to set slide background color
def set_slide_background_color(slide, image_path,prs):
    # Get slide dimensions from the presentation (in inches)
    slide_width = prs.slide_width
    slide_height = prs.slide_height

    # Add image covering the entire slide
    image_shape = slide.shapes.add_picture(image_path, Inches(0), Inches(0), width=slide_width, height=slide_height)
    image_shape._element.getparent().remove(image_shape._element)  # Remove image from default order
    slide.shapes._spTree.insert(2, image_shape._element)  # Insert image back to the first position (behind)


# Parse template into structured sections
def parse_template(text):
    sections = []
    table_data = None
    lines = text.splitlines()
    current_section = None
    content = []
 
    for line in lines:
        line = line.strip()
        if line.startswith('# '):  # Top-level title
            if current_section:
                # Add the section and its content
                sections.append((current_section, "\n".join(content)))
                # If there is a table, add it after the section content
                if table_data:
                    headers, *rows = [row.strip('|').split('|') for row in table_data]
                    sections.append(('Table', {'headers': headers, 'rows': rows}))
                    table_data = None  # Reset table data after adding it
            current_section = line[2:].strip()
            content = []
        elif line.startswith('## '):  # Subtitle
            if current_section:
                sections.append((current_section, "\n".join(content)))
                if table_data:
                    headers, *rows = [row.strip('|').split('|') for row in table_data]
                    sections.append(('Table', {'headers': headers, 'rows': rows}))
                    table_data = None  # Reset table data after adding it
            current_section = line[3:].strip()
            content = []
        elif line.startswith('### '):  # Subheading
            if current_section:
                sections.append((current_section, "\n".join(content)))
                if table_data:
                    headers, *rows = [row.strip('|').split('|') for row in table_data]
                    sections.append(('Table', {'headers': headers, 'rows': rows}))
                    table_data = None  # Reset table data after adding it
            current_section = line[4:].strip()
            content = []
        elif line.startswith('#### '):  # Minheading
            if current_section:
                sections.append((current_section, "\n".join(content)))
                if table_data:
                    headers, *rows = [row.strip('|').split('|') for row in table_data]
                    sections.append(('Table', {'headers': headers, 'rows': rows}))
                    table_data = None  # Reset table data after adding it
            current_section = line[5:].strip()
            content = []
        elif line.startswith('|'):  # Table line
            if table_data is None:
                table_data = []  # Initialize table_data when a table starts
            table_data.append(line)
        elif line:  # Regular content
            content.append(line)  # Add regular content to the section
 
    # Add any remaining content and table at the end of the file
    if current_section:
        sections.append((current_section, "\n".join(content)))
        if table_data:
            headers, *rows = [row.strip('|').split('|') for row in table_data]
            sections.append(('Table', {'headers': headers, 'rows': rows}))
 
    return sections

def create_paragraph(object_list, prs):
    prs2 = Presentation("./DocumentGeneration/Presentation/Quisitive/slideLayouts/paragraph.pptx")
    file_path3 = "./src/presentation/QuisitiveTemplate/Slide3.JPG"
   
    # Step 2: Loop through `response` and create slides
    for item in object_list:
        item = dict(item)
        # Add a new slide (using the layout of the first slide as a base)
        slide_layout = prs2.slide_layouts[0]  # Adjust layout index if needed
        slide = prs.slides.add_slide(slide_layout)

        # Set the background image for the slide
        slide_width = prs.slide_width
        slide_height = prs.slide_height
        image_shape = slide.shapes.add_picture(file_path3, Inches(0), Inches(0), width=slide_width, height=slide_height)
        image_shape._element.getparent().remove(image_shape._element)  # Remove image from default order
        slide.shapes._spTree.insert(2, image_shape._element)  # Insert image behind text elements

        # Step 3: Loop through the shapes to find placeholders
        for shape in slide.shapes:
            # print(f"===========Shape name ==> {shape.name}")
            # print(f"===========Shape name ==> {shape.name}")
            if not shape.is_placeholder:
                continue  # Skip non-placeholder shapes

            # Check for Title Placeholder
            if shape.placeholder_format.type == 1:  # 1 = TITLE
                shape.text = item['title']  # Set the title text
                # print(f"Updated Title Placeholder: {shape.text}")

            # Check for Body Placeholder
            elif shape.placeholder_format.type == 2:  # 2 = BODY
                text_frame = shape.text_frame
                text_frame.clear()  # Clear any existing text
                text_frame.text = item['paragraph']  # Set the body text
                # Optionally adjust paragraph formatting
                for paragraph in text_frame.paragraphs:
                    paragraph.space_after = Inches(0.1)
                    paragraph.line_spacing = 1.0  # Optional: Adjust line spacing
                # print(f"Updated Body Placeholder: {shape.text}")
    return prs

def create_table(object_list, prs):
    prs2 = Presentation("./DocumentGeneration/Presentation/Quisitive/slideLayouts/table.pptx")
    file_path3 = "./DocumentGeneration/Presentation/Quisitive/backgrounds/slide.jpg"
    # Step 2: Loop through `response` and create slides
    for item in object_list:
        item = dict(item)
        
        # Add a new slide (using the layout of the first slide as a base)
        slide_layout = prs2.slide_layouts[0]  # Adjust layout index if needed
        slide = prs.slides.add_slide(slide_layout)

        # Set the background image for the slide
        slide_width = prs.slide_width
        slide_height = prs.slide_height
        image_shape = slide.shapes.add_picture(file_path3, Inches(0), Inches(0), width=slide_width, height=slide_height)
        image_shape._element.getparent().remove(image_shape._element)  # Remove image from default order
        slide.shapes._spTree.insert(2, image_shape._element)  # Insert image behind text elements

        # Step 3: Loop through the shapes to find placeholders
        for shape in slide.shapes:
            if not shape.is_placeholder:
                continue  # Skip non-placeholder shapes

            # Check for Title Placeholder
            # print(f"===========Shape type ==> {shape.placeholder_format.type}")
            # print(f"===========Shape name ==> {shape.name}")
            if shape.placeholder_format.type == 1:  # 1 = TITLE
                shape.text = item["title"]  # Set the title text
                # print(f"Updated Title Placeholder: {shape.text}")

            # Check for Body Placeholder
            elif shape.placeholder_format.type == 2:  # 2 = BODY
                text_frame = shape.text_frame
                text_frame.clear()  # Clear any existing text
                text_frame.text = item["paragraph"]  # Set the body text
                # Optionally adjust paragraph formatting
                for paragraph in text_frame.paragraphs:
                    paragraph.space_after = Inches(0.1)
                    paragraph.line_spacing = 1.0  # Optional: Adjust line spacing
                # print(f"Updated Body Placeholder: {shape.text}")

            # Check for Table Placeholder
            elif shape.placeholder_format.type == 12:  # 12 = table
                rows, cols = len(item["table"]), len(item["table"][0])
                table = shape.insert_table(rows, cols).table

                # Populate the table
                for row_idx, row_data in enumerate(item["table"]):
                    for col_idx, cell_value in enumerate(row_data):
                        cell = table.cell(row_idx, col_idx)
                        cell.text = cell_value

                # print(f"Updated Table Placeholder with {rows} rows and {cols} columns")
    return prs