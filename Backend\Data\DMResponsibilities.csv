Task, Consultant, Customer, Required By
Data Migration Strategy, X, , End of Analysis
Extraction from legacy system, , X, End of Analysis
Data cleansing, , X, Begin Design
Design of tools and templates, X, , End of Design
Data mapping (legacy to new), X, , End of Design
Develop templates and the format data should take, X, , End of Design
Develop automated migration scripts (if any), X, , Mid Development
"Place data in template format, staging table or form"  , , X, No later than the start of Process Testing
Loading data into new system, X, , No later than the end of Process Testing
Testing and validating (reconciliation) migrated data, , X, No later than the end of Process Testing
Manual data migrations (if any), , X, No later than the end of Process Testing