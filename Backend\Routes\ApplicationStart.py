import azure.functions as func
import logging,traceback,json
from DatabaseCosmos.StatesChecklist import creatStates,updateAppModState
from DatabaseCosmos.UserCurrentChat import read_userConversation,create_userConversation,upsert_userConversation
# from Orchestration.LangChainOrchestration import proposalCreation_agent_executor
from Orchestration.LangChainOrchestration import executeAgent
from DatabaseCosmos.Buisness_process_State import callUpdateBp,read_BPState
from DatabaseCosmos.ERP_State import callUpdateErp, read_ERPState
from DatabaseCosmos.App_module_State import callUpdateAP
from DatabaseCosmos.Data_Migration_State import callUpdateDm
from DatabaseCosmos.Integration_State import callUpdateInt
from DatabaseCosmos.Company_info_State import getAllByUserID
from DatabaseCosmos.ProjectEstimation import read_ProjectEstimation_State
from DatabaseCosmos.Company_info_State import read_CompanyInfo
from utilities.getSasURL import GenerateSAS_URL , generate_benchmark_file_sas_url
from Config.cosmosdb import database
from Config.blobService import blob_service_client
from utilities.delete_doc import delete_file_from_blob
from utilities.delete_record import delete_record
from azure.storage.blob import BlobServiceClient
import os
from DatabaseCosmos.Theme_template_upload import insert_Template_FileName
from docx import Document
from RAG.upload_on_indexes import upload_documents_in_raw_and_summary_indexes
from DatabaseCosmos.Timeline import GetTimeline
from Timeline.dynamic_resource_assignement import create_timeline
from DatabaseCosmos.Theme_template_state import insert_Template_state


application_start = func.Blueprint()

@application_start.route(route="conversationStart")
def conversationStart(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python conversationStart trigger function processed a request.')
    try:
        print("here")
        req_body = req.get_json()
        user_id = req.headers.get('user_id')
        user_input = req_body.get('user_input')
        id = req_body.get('id')

        if not user_id:
            return func.HttpResponse(
            f"Please provide User_id in headers",
            status_code=500
        )
        
        if id:
            print("current session user")
            try:
                UserChatHistory = read_userConversation(id)
                print("userHistory found",UserChatHistory['History'])
                UserChatHistory['History'].append({"role": "user", "content": user_input})
                upsert_userConversation(UserChatHistory)
               
            except Exception as e:
                return func.HttpResponse(
            f"Please provide correct chat_id in json body! no User History Found, error: {e}",
            status_code=500
        )
        else:
            print("new user incoming!!")
            creatStates(user_id)
            UserChatHistory = create_userConversation(user_id,[])
            UserChatHistory['History'].append({"role": "user", "content": user_input})
            upsert_userConversation(UserChatHistory)
            id = UserChatHistory['id']
            user_id = UserChatHistory['user_id']
            
        print(f"UserChatHistory in conversationStart: {UserChatHistory}")
        response = executeAgent(query=user_input, id=id, user_id=user_id, chat_history=UserChatHistory['History'])
        print(f"response from agent: {response}")
        response = json.loads(response)
        return json.dumps(response)

    except Exception as e:
        print(traceback.format_exc())
        logging.error(f"Error while handling chat {str(e)}", exc_info=True)
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500
        )

 
@application_start.route(route="ActiveProspects")
def ActiveProspects(req: func.HttpRequest) -> func.HttpResponse:
        
    try:
           
        user_id = req.headers.get('user_id')

        if user_id:
            companyinfo = getAllByUserID(user_id=user_id)
            if companyinfo:
                for company in companyinfo:
                    erp = read_ERPState(company['chat_id'])

                    if erp :  
                        erp_name = [item['label'] for item in erp[0]['items'] if item['isChecked']]  
                        company['erp'] = erp_name if erp_name else []
                    else:
                        erp_name = []  
                        company['erp'] = erp_name
                    
                    estimation = read_ProjectEstimation_State(company["chat_id"])
                    if estimation:
                        estimationObject = {
                                                "months": estimation['months'],
                                                "resources": len(estimation['Resources'].values()),
                                                "budget": estimation['Budget'],
                                                "estimatedHours": estimation['Estimated Hours'],
                                            }
                        company['estimations'] = estimationObject

                    else:
                        company['estimations'] = {}
                    
                       
            return func.HttpResponse(json.dumps({                        
                        "user_id": user_id,
                        "viewType": "activeProspect",
                        "projectdata":companyinfo
                        }), status_code=200)
    except ValueError:
        return func.HttpResponse("Invalid JSON", status_code=400)

@application_start.route(route="GetUrl")
def GetUrl(req: func.HttpRequest) -> func.HttpResponse:

    try:
        req_body = req.get_json()
        id = req_body.get('id')
        user_id = req.headers.get('user_id')
        type = req_body.get('type')

        company_info = read_CompanyInfo(id)
        response = GenerateSAS_URL(id=id,user_id=user_id,company_name=company_info['companyname'],type=type)
        return func.HttpResponse(
            response,
            status_code=200
        )

    except Exception as e:
        print(traceback.format_exc())
        logging.error(f"Error while generating SAS URL! Please try again {str(e)}", exc_info=True)
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500
        )
    

@application_start.route(route="insertionCall")
def insertionCall(req: func.HttpRequest) -> func.HttpResponse:
        
    try:
        req_body = req.get_json()
    except ValueError:
        return func.HttpResponse("Invalid JSON", status_code=400)

    user_id = req.headers.get('user_id')
    id = req_body.get('id')
    state = req_body.get('viewtype')
    selected_list = req_body.get("list", [])
    ids = [item["id"] for item in selected_list]

    if state == "bp":
        logging.info("BP Insertion Call")
        print("BP Insertion Call")
        print(f"IDs: {ids}")
        value :bool = callUpdateBp(chat_id=id,selected_list=selected_list)
        if value == True:           
            return func.HttpResponse("Insertion call processed successfully.", status_code=200)
        else:
            return func.HttpResponse(
            f"Error while handling Insertion call! please try again",
            status_code=500
        )
    elif state == "ap":
        logging.info("AP Insertion Call")
        value :bool = callUpdateAP(chat_id=id,selected_list=selected_list)
        if value == True:
            return func.HttpResponse("Insertion call processed successfully.", status_code=200)
        else:
            return func.HttpResponse(
            f"Error while handling Insertion call! please try again",
            status_code=500
        )
    elif state == "erp":
        logging.info("ERP Insertion Call")

        erp = read_ERPState(id)
        records = erp[0]['items']
        selected_ids = {item['id'] for item in selected_list}
        is_changed = False

        for record in records:
            print(f"Processing record: {record}")  # Debugging
            if (record['id'] in selected_ids and not record['isChecked']):
                print(f"Newly checked detected for ID: {record['id']}")
                is_changed = True
                break
            if (record['id'] not in selected_ids and record['isChecked']):
                print(f"Newly unchecked detected for ID: {record['id']}")
                is_changed = True
                break
        
        if is_changed:
            delete_record(id,user_id,"App_module_State")
            
        value :bool = callUpdateErp(chat_id=id,selected_list=selected_list)
        if value == True:
            return func.HttpResponse("Insertion call processed successfully.", status_code=200)
        else:
            return func.HttpResponse(
            f"Error while handling Insertion call! please try again",
            status_code=500
        )
    elif state == "int":
        logging.info("Integration Insertion Call")
        value :bool = callUpdateInt(chat_id=id,selected_list=selected_list)
        if value == True:
            return func.HttpResponse("Insertion call processed successfully.", status_code=200)
        else:
            return func.HttpResponse(
            f"Error while handling Insertion call! please try again",
            status_code=500
        )
    elif state == "dm":
        logging.info("DM Insertion Call")
        value :bool = callUpdateDm(chat_id=id,selected_list=selected_list)
        if value == True:
            return func.HttpResponse("Insertion call processed successfully.", status_code=200)
        else:
            return func.HttpResponse(
            f"Error while handling Insertion call! please try again",
            status_code=500
        )
    
    # Return a success response
    return func.HttpResponse("Insertion call processed successfully.", status_code=200)

@application_start.route(route="DeleteChat")
def deleteChat(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Trigger to delete all instances of that particular chat.')
    
    try:

        req_body = req.get_json()
        user_id = req.headers.get('user_id')
        id = req_body.get('id')

        #Deleting documents from blob wrt the chat_id
        delete_file_from_blob(user_id, id)
        
        # List all containers in the database
        containers = list(database.list_containers())
        
        for container_info in containers:
            container_name = container_info['id']
            container = database.get_container_client(container_name)

            if container.id == "CurrentChat":
                query = f"SELECT c.id FROM c WHERE c.id = @chat_id"
            else:
                query = f"SELECT c.id FROM c WHERE c.chat_id = @chat_id"
            
            parameters = [{"name": "@chat_id", "value": id}]
            items = container.query_items(query=query, parameters=parameters, enable_cross_partition_query=True)
            
            # Delete each item
            for item in items:
                container.delete_item(item=item['id'], partition_key=user_id)
                print(f"Deleted item {item['id']} from container {container_name}")       
        
        return func.HttpResponse(
            status_code=200
        )
  
    except Exception as e:
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500
        )
    
@application_start.route(route="getResponse")
def getResponse(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Trigger to get last response of the chat.')
    
    try:

        req_body = req.get_json()
        id = req_body.get('id')

        response = read_userConversation(id)
        recentResponse = response['History'][-2:]
        
        return func.HttpResponse(
            json.dumps({
                "response": recentResponse
            }),
            status_code=200
        )
  
    except Exception as e:
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500
        )
    
@application_start.route(route="uploadFile")
def uploadFile(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Trigger to upload excel file.')
    
    try:
        file = req.files.get('file')
        container_name = os.getenv("Blob_output_container_name")
        container_client = blob_service_client.get_container_client(container_name)

        if not file:
            return func.HttpResponse("Missing file", status_code=400)
        
        folder_path = f"Resource_costing/"

        # Check for existing blobs in the folder
        blob_list = container_client.list_blobs(name_starts_with=folder_path)
        for blob in blob_list:
            # Delete all existing blobs in the folder
            container_client.delete_blob(blob.name)
    
        # Upload the file to the correct folder
        file_name = file.filename
        blob_client = container_client.get_blob_client(f"Resource_costing/{file_name}")
        blob_client.upload_blob(file, overwrite=True)
        return func.HttpResponse(f"File {file_name} uploaded successfully", status_code=200)
    
    except Exception as e:
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500
        )
        
        
# Route for uploading document theme file 
@application_start.route(route="docThemeUpload")
def docThemeFileUpload(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Trigger to upload excel file.')
    
    try:
        file = req.files.get('file')
        # user_id= req.headers.get('user_id')
        
        container_name = os.getenv("Blob_output_container_name")
        container_client = blob_service_client.get_container_client(container_name)

        if not file:
            return func.HttpResponse("Missing file", status_code=400)
        
        folder_path = f"ThemeTemplate/docFile/"

        # Check for existing blobs in the folder
        blob_list = container_client.list_blobs(name_starts_with=folder_path)
        for blob in blob_list:
            # Delete all existing blobs in the folder
            container_client.delete_blob(blob.name)
    
        # Upload the file to the correct folder
        file_name = file.filename
        blob_client = container_client.get_blob_client(f"{folder_path}{file_name}")
        blob_client.upload_blob(file, overwrite=True)
        items = insert_Template_FileName(file_name, "document")
        
        return func.HttpResponse(f"File {file_name} uploaded successfully", status_code=200)
    
    except Exception as e:
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500
        )
        
        
        
# Route for uploading presentation theme file 
@application_start.route(route="pptThemeUpload")
def pptThemeFileUpload(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Trigger to upload excel file.')
    
    try:
        file = req.files.get('file')
        # user_id= req.headers.get('user_id')
        
        container_name = os.getenv("Blob_output_container_name")
        container_client = blob_service_client.get_container_client(container_name)

        if not file:
            return func.HttpResponse("Missing file", status_code=400)
        
        folder_path = f"ThemeTemplate/pptFile/"

        # Check for existing blobs in the folder
        blob_list = container_client.list_blobs(name_starts_with=folder_path)
        for blob in blob_list:
            # Delete all existing blobs in the folder
            container_client.delete_blob(blob.name)
    
        # Upload the file to the correct folder
        file_name = file.filename
        blob_client = container_client.get_blob_client(f"{folder_path}{file_name}")
        blob_client.upload_blob(file, overwrite=True)
        
        items = insert_Template_FileName(file_name, "ppt")
        
        return func.HttpResponse(f"File {file_name} uploaded successfully", status_code=200)
    
    except Exception as e:
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500
        )
    
# Route for uploading presentation theme file 
@application_start.route(route="uploadDocKB")
def upload_document_for_knowledgebase(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Trigger to upload document file for knowledgebase and summarization.')
    
    try:
        file = req.files.get('file')
        user_id= req.headers.get('user_id')
        
        container_name = os.getenv("blob_rag_container_name")
        container_client = blob_service_client.get_container_client(container_name)

        if not file:
            return func.HttpResponse("Missing file", status_code=400)
        
        folder_path = f"DocumentKnowledgeBase/{user_id}"
    
        # Upload the file to the correct folder
        file_name = file.filename
        blob_client = container_client.get_blob_client(f"{folder_path}/{file_name}")
        blob_client.upload_blob(file, overwrite=True)
        
        blob_url  = blob_client.url
        document = Document(file)
        
        upload_documents_in_raw_and_summary_indexes(document , blob_url)
        
        return func.HttpResponse(f"File {file_name} uploaded and created Indexex, successfully", status_code=200)
    
    except Exception as e:
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500,
        )
    

@application_start.route(route="updateTimeline")
def get_dynamic_resource(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Trigger to get dynamic resource.')
    
    try:

        #db get biuld hours
        # calculate timeline again acording to given resources
        req_body = req.get_json()
        user_id = req.headers.get('user_id')
        id = req_body.get('id')
        updated_resources = req_body.get('resources')
        
        #build_hours = get_total_hours(estimates)
        item = GetTimeline(id)
        build_hours = item['build_hours']
        print(f"Build hours: {build_hours}")
        
        tool_response = create_timeline(build_hours, updated_resources , user_id , id) #updated timeline
        tool_response = json.loads(tool_response)

        # add into chat history
        UserChatHistory = read_userConversation(id)
        UserChatHistory['History'].append({"role": "Tool", "content":tool_response["tool_name"], "response": tool_response})
        upsert_userConversation(UserChatHistory)

        return func.HttpResponse(
            json.dumps(tool_response),
            status_code=200
        )
    
    except Exception as e:
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500
        )
    
@application_start.route(route="selectDocTheme")
def select_doc_theme(req: func.HttpRequest) -> func.HttpResponse:
    try:
        # Get the theme selection from request body
        req_body = req.get_json()
        theme_choice = req_body.get("theme_choice","default")
        
        items = insert_Template_state(theme_choice, "document")
        
        return func.HttpResponse(f"Document theme preference set to {theme_choice}", status_code=200)
    
    except Exception as e:
        return func.HttpResponse(
            f"Error setting Document theme preference: {str(e)}",
            status_code=500
        )
    
@application_start.route(route="selectPresentationTheme")
def select_ppt_theme(req: func.HttpRequest) -> func.HttpResponse:
    try:
        # Get the theme selection from request body
        req_body = req.get_json()
        theme_choice = req_body.get("theme_choice","default")
        
        items = insert_Template_state(theme_choice, "ppt")
        
        return func.HttpResponse(f"Presentation theme preference set to {theme_choice}", status_code=200)
    
    except Exception as e:
        return func.HttpResponse(
            f"Error setting Presentation theme preference: {str(e)}",
            status_code=500
        )
    



# Route for uploading document theme file 
@application_start.route(route="benchmarkFileUpload")
def benchmarkFileUpload(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Trigger to upload excel file.')
    
    try:
        file = req.files.get('file')
        
        container_name = os.getenv("Blob_input_container_name")
        container_client = blob_service_client.get_container_client(container_name)

        if not file:
            return func.HttpResponse("Missing file", status_code=400)
        
        folder_path = f"Hours Benchmarks/admin/"

        # Check for existing blobs in the folder
        blob_list = container_client.list_blobs(name_starts_with=folder_path)
        for blob in blob_list:
            # Delete all existing blobs in the folder
            container_client.delete_blob(blob.name)
    
        # Upload the file to the correct folder
        file_name = file.filename
        blob_client = container_client.get_blob_client(f"{folder_path}{file_name}")
        blob_client.upload_blob(file, overwrite=True)
        
        return func.HttpResponse(f"File {file_name} uploaded successfully", status_code=200)
    
    except Exception as e:
        return func.HttpResponse(
            f"Error while handling chat: {str(e)}",
            status_code=500
        )
    


@application_start.route(route="getBenchmarkSheetUrl")
def get_benchmark_sheet_sas_url(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Trigger to get SAS URL for benchmark sheet.')
    
    try:
        response = generate_benchmark_file_sas_url()
        return func.HttpResponse(
            json.dumps({"sas_url": response}),
            status_code=200
        )
    
    except Exception as e:
        return func.HttpResponse(
            f"Error generating SAS URL: {str(e)}",
            status_code=500
        )
