from pydantic import BaseModel, Field
from typing import List, Optional

class CompanyStructureModel(BaseModel):
    """It will use to search for the company"""

    company_name: str = Field(description="Name of the Company, if its typo mistake then make it correct") 
    company_information: str = Field(description="detailed information about the company")

    headquarters_address: str = Field(description="headquarter address of the company")
    employees: int = Field(description="Number of employees with in the company")

    subsidiaries_and_brands: List[str] = Field(description="subsidiaries of the company")

    industry_name: str = Field(description="Name of the Industry the company belongs to")
    type_of_manufacturing: str = Field(description="type of manufacturing of the company")
    SIC_Code: str = Field(description="SIC_Code, id available else return empty string")

    corporate_structure_subsidiary_of: str = Field(description="Name of the parent company")
    corporate_structure_ownership: str = Field(description="ownership of the company")