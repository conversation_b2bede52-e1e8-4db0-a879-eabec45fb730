from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import io

from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.Buisness_process_State import read_BPState
from DatabaseCosmos.App_module_State import read_AppModule_State
from DatabaseCosmos.ERP_State import read_ERPState
from DatabaseCosmos.UserCurrentChat import read_userConversation, upsert_userConversation
from utilities.upload_file import upload_file_to_blob

# from langchain.memory import ConversationBufferMemory
from Config.azureAI import llm
from langchain_core.prompts import ChatPromptTemplate
import re
from pptx import Presentation
from pptx.util import Pt, Inches
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.text import MSO_ANCHOR
import datetime

import os

file_path1 = "./src/presentation/QuisitiveTemplate/Slide1.JPG"
file_path2 = "./src/presentation/QuisitiveTemplate/Slide2.JPG"
file_path3 = "./src/presentation/QuisitiveTemplate/Slide3.JPG"
file_path4 = "./src/presentation/QuisitiveTemplate/Slide4.JPG"

# Set up a template to guide the language model
template = """
Todays Date: {date}
Document is generating by Quisitive
Generate a SOW document for '{document_type}' based on the minimal App suggesting Microsoft 365 suggestions for the company..
Don't mention numbers while generating list.
following are the details:

{content}
"""

# # Set up a template to guide the language model
# template = """
# Todays Date: {date}
# Document is generating by Quisitive
# Generate a SOW document in HTML for {document_type} based on the App suggesting Microsoft 365 suggestions for the company.
# Remember to create the document in HTML format, start from <body> tag and add ul, headings, subheadings, tables, and etc for easy parsing.
# following are the details:

# {content}
# """

prompt = ChatPromptTemplate.from_messages(
            [
                ("system", template),
                ("human", "{input}")
            ]
        )

# Create a LangChain chain to use the template
document_chain = prompt | llm

@tool(return_direct=True)
def createPptTool( input_query:str, user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is use to generate the document and wil return you the url to dowload the document"""

    
    document_type = f"company_name"
    msg_summary = f"Want to create a SOW for the company: "
    ppt_url = ""
    docx_url = ""

    company_info = read_CompanyInfo(id)
    if company_info:
        company_input = f"""
                        Compnay Information:

                        Company Name: {company_info['companyname']}
                        Company Information: {company_info['companyinformation']}
                        Number of Employees: {company_info['employees']}
                        Headquarters Location: {company_info['headquarters_address']}
                        Industry: {company_info['industry']}
                    """
        document_type = company_info['companyname']
        msg_summary += company_input
    
    bp_state = read_BPState(id)
    if bp_state:
        # print("bp state", bp_state)
        bp_state_info = f"""
                        Business Processes the company choosed:
                        """
        for each_object in bp_state[0]['items']:
            if each_object['isChecked'] == True:
                bp_str = f"""

                        Label = {each_object['label']}

                         """
                bp_state_info += bp_str

        msg_summary += bp_state_info
    
    module_state = read_AppModule_State(id)
    if module_state:
        # print("module state", module_state)
        module_state_info = f"""
                        App modules the company choosed:
                        """
        for each_object in module_state[0]['items']:
            if each_object['isChecked'] == True:
                module_str = f"""

                        Label = {each_object['label']}

                         """
                module_state_info += module_str

        msg_summary += module_state_info

    erp_state = read_ERPState(id)
    if erp_state:
        # print("ERP state", erp_state)
        erp_state_info = f"""
                        ERP modules the company choosed:
                        """
        for each_object in erp_state[0]['items']:
            if each_object['isChecked'] == True:
                erp_str = f"""

                        Label = {each_object['label']}

                         """
                erp_state_info += erp_str

        msg_summary += erp_state_info
    
    UserChatHistory = read_userConversation(id)
    UserChatHistory['History'].append({"role": "tool", "content": f"document created for query: '{input_query}'"})
    upsert_userConversation(UserChatHistory)

    UserChatHistory = read_userConversation(id)
    print("History...",UserChatHistory['History'])
    chat_history = f"""
                    Here is the Chat History,
                    Also generate the document the user have asked for

                    Here is the chat history: {str(UserChatHistory['History'])}
                    """
    msg_summary += chat_history
    # print("msg Summary:" , msg_summary)
    


    # Get response from the model
    response = document_chain.invoke({
                    "document_type": document_type, 
                    "content": msg_summary, 
                    "date": str(datetime.datetime.now()),
                    "input": "create document"
                    })
    print("Response...", response)
    response = response.content

    # Initialize presentation
    prs = Presentation()
    # prs.slide_width = Inches(17.778)
    # prs.slide_height = Inches(10)

    # Parse the template
    sections = parse_template(response)

    # Generate slides based on sections
    for section, content in sections:
        print(section)
        if section == 'Title':
            add_title_slide(prs, content)
        elif section == 'Table':
            headers = [header.strip() for header in content['headers']]
            rows = [[cell.strip() for cell in row] for row in content['rows']]
            add_table_slide(prs, 'Estimated Project Cost', headers, rows)
        else:
            add_content_slide(prs, section, content)

    # Save the presentation
    slide_layout = prs.slide_layouts[-1]
    slide = prs.slides.add_slide(slide_layout)
    set_slide_background_color(slide, file_path4,prs)
    # prs.save("Statement_of_Work_Presentation.pptx")

    # Save the document
    file_path = f"{document_type}_presentation.pptx"

    # Save the rendered document to an in-memory stream
    output_stream = io.BytesIO()
    prs.save(output_stream)
    output_stream.seek(0)  # Reset stream position for uploading

    #generating sas url
    # print("generating SAS URL....")
    sas_url = upload_file_to_blob(
                                blob_name=file_path,
                                output_stream=output_stream
                                )

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "documentcreation",
                        "url": {
                            "ppt_url" : ppt_url,
                            "docx_url": docx_url,
                        }
                    }
                )


# Function to add title slide with background color
def add_title_slide(prs, title):
    
    slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(slide_layout)
    set_slide_background_color(slide, file_path1,prs)  # Light lavender background
    slide.shapes.title.text = title
    

# Function to add content slide with background color
def add_content_slide(prs, title, content):
    
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    set_slide_background_color(slide, file_path3,prs)  # Light gray background
    slide.shapes.title.text = title

    
    
    text_placeholder = slide.placeholders[1]
    text_placeholder.text = clean_markdown(content)
    for paragraph in text_placeholder.text_frame.paragraphs:
        if ':' in paragraph.text:
            heading, text = paragraph.text.split(':', 1)
            paragraph.clear()  # Clear the existing text
            run = paragraph.add_run()
            run.text = heading.strip() + ': '
            run.font.bold = True
            run = paragraph.add_run()
            run.text = text.strip()
        paragraph.font.size = Pt(18)
        paragraph.alignment = PP_ALIGN.LEFT




# Function to add table slide with background color
def add_table_slide(prs, title, headers, rows):
    
    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    set_slide_background_color(slide, file_path3,prs)  # Light gray background
    slide.shapes.title.text = title
    

    # Define table dimensions
    table = slide.shapes.add_table(len(rows) + 1, len(headers), Inches(1.2), Inches(1.5), Inches(8), Inches(1.5)).table
    

    # Add header row
    for col, header in enumerate(headers):
        cell = table.cell(0, col)
        cell.text = header.strip()
        cell.text_frame.paragraphs[0].font.bold = True
        cell.text_frame.paragraphs[0].font.size = Pt(12)
        cell.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        cell.fill.solid()
        cell.fill.fore_color.rgb = RGBColor(200, 200, 200)

    # Add data rows
    for row_idx, row_data in enumerate(rows, 1):
        for col_idx, cell_data in enumerate(row_data):
            cell = table.cell(row_idx, col_idx)
            cell.text = clean_markdown(cell_data.strip())
            cell.text_frame.paragraphs[0].font.size = Pt(12)
            

# Function to clean markdown syntax
def clean_markdown(text):
    text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # Remove bold asterisks
    text = re.sub(r'\*(.*?)\*', r'\1', text)  # Remove italic asterisks
    text = re.sub(r'\-', '', text)  # Remove hyphens
    print(text)
    return text


# Function to set slide background color
def set_slide_background_color(slide, image_path,prs):
    # Get slide dimensions from the presentation (in inches)
    slide_width = prs.slide_width
    slide_height = prs.slide_height

    # Add image covering the entire slide
    image_shape = slide.shapes.add_picture(image_path, Inches(0), Inches(0), width=slide_width, height=slide_height)
    image_shape._element.getparent().remove(image_shape._element)  # Remove image from default order
    slide.shapes._spTree.insert(2, image_shape._element)  # Insert image back to the first position (behind)


# Parse template into structured sections
def parse_template(text):
    sections = []
    table_data = None
    lines = text.splitlines()
    current_section = None
    content = []

    for line in lines:
        line = line.strip()
        if line.startswith('# '):  # Top-level title
            sections.append(('Title', line[2:].strip()))
        elif line.startswith('## '):  # Subtitle
            if current_section:
                sections.append((current_section, "\n".join(content)))
            current_section = line[3:].strip()
            content = []
        elif line.startswith('### '):  # Subheading
            if current_section:
                sections.append((current_section, "\n".join(content)))
            current_section = line[4:].strip()
            content = []
        elif line.startswith('|'):  # Table line
            if table_data is None:
                table_data = []
            table_data.append(line)
        elif line:
            content.append(line)

    # Append any remaining content
    if current_section:
        sections.append((current_section, "\n".join(content)))
    if table_data:
        headers, *rows = [row.strip('|').split('|') for row in table_data]
        sections.append(('Table', {'headers': headers, 'rows': rows}))

    return sections
    
   