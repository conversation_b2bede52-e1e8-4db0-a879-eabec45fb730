import pandas as pd
from DatabaseCosmos.ERP_State import read_ERPState
df = pd.read_csv(r"./Data/Modules.csv", encoding='utf-8')

def get_modules(module_list: list,id):

    erp = read_ERPState(id)

    
    if erp:
        items = erp[0]['items']
        selected_erp = [item for item in items if item["isChecked"]]
        #df['Module Group'] = df['Module Group'].str.capitalize()
        if len(selected_erp) == 1 and "finance" in selected_erp[0]["label"].lower():
            modules_df = df[df['Module Group'].isin(['Finance', 'Supply Chain Management'])]
        elif len(selected_erp) == 1 and "business central" in selected_erp[0]["label"].lower():
            modules_df = df[df['Module Group'].isin(['Business Central'])] 
        else:
            modules_df = df
    else:
        modules_df = df
    
    # Initialize the result list
    result = []
    
    # Iterate over each row in the dataframe
    for _, row in modules_df.iterrows():
        # Check if the current ID is in the bp_id list
        is_checked = True if row['Module ID'] in module_list else False
        
        # Create the object with the required structure
        module_obj = {
            "type": "checkboxWithGrouping",
            "label": row['Module Name'],
            "description": row['Description'],
            "id": row['Module ID'],
            "group": row['Module Group'],
            "isChecked": is_checked
        }
        
        # Append to the result list
        result.append(module_obj)

    # print(result)
    
    return result

# # Usage
# module_list = ['BP003', 'BP004', 'BP007', 'BP008', 'BP012']
# business_processes = get_business_processes(module_list)
# print(business_processes)
