import markdown
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import re

from docx.shared import P<PERSON>, <PERSON><PERSON>, RGBColor, Cm

from LLMChains.document_static_data_prompts import project_governace_static

import zipfile
from lxml import etree
import os
from datetime import date
import shutil


# from docx import Document
# from datetime import date
from LLMChains.document_static_data_prompts import project_governace_static

import zipfile
from lxml import etree
import os
from datetime import date
import shutil
import html  # This will help escape special characters in XML

from docx import Document
from docx.shared import Pt, Inches, RGBColor, Cm
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.oxml import OxmlElement



# Input and output file paths
doc_path = "./Data/SOW_Microsoft_Dynamics_front_page.docx"
updated_doc_path = "./Data/SOW_Microsoft_Dynamics_document.docx"



# Function to replace placeholders in XML content
def replace_placeholders_in_xml(xml_content, placeholder_data):
    for placeholder, replacement in placeholder_data.items():
        xml_content = xml_content.replace(placeholder, replacement)
    return xml_content

# Function to replace text in shapes (text boxes)
def replace_placeholders_in_shapes(xml_content, placeholder_data):
    # Remove XML declaration if it exists (the first line)
    if xml_content.startswith("<?xml"):
        xml_content = xml_content.split("\n", 1)[-1]  # Remove the first line (XML declaration)
    
    # # Parse the XML content
    # tree = etree.fromstring(xml_content)  # Use fromstring instead of XML
    # Parse the XML content
    try:
        tree = etree.fromstring(xml_content)  # Use fromstring instead of XML
    except etree.XMLSyntaxError as e:
        print(f"Error parsing XML: {e}")
        return xml_content  # Return the original content if parsing fails
    
    
    # Find all <w:drawing> elements (shapes)
    drawing_elements = tree.xpath('//w:drawing', namespaces={'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
    
    # Debug: Print the structure of the first <w:drawing> element to inspect its contents
    if drawing_elements:
        # print("Found drawing element:")
        print(etree.tostring(drawing_elements[0], pretty_print=True).decode())
    else:
        print("No <w:drawing> elements found.")
    
    for drawing in drawing_elements:
        # Debug: Print out the drawing element to inspect
        # print(f"Inspecting drawing: {etree.tostring(drawing, pretty_print=True).decode()}")

        # Inside each <w:drawing>, there might be <w:t> (text) elements to modify
        text_elements = drawing.xpath('.//w:t', namespaces={'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'})
        
        if text_elements:
            for text_element in text_elements:
                # Replace placeholders in the text of the shape
                for placeholder, replacement in placeholder_data.items():
                    if placeholder in text_element.text:
                        print(f"Replacing '{placeholder}' with '{replacement}' in text: {text_element.text}")
                        text_element.text = text_element.text.replace(placeholder, replacement)
        else:
            print("No <w:t> elements found inside the shape.")

    # Return the modified XML as a string
    return etree.tostring(tree, encoding='utf-8').decode('utf-8')



# Function to process and update XML files in the Word document
def process_docx_placeholders(doc_path, updated_doc_path, placeholder_data):
    temp_dir = "./temp_docx"
    
    # Remove the directory if it already exists
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    
    # Step 1: Unzip the .docx file
    with zipfile.ZipFile(doc_path, 'r') as docx_zip:
        if os.path.exists(temp_dir):
            os.system(f"rm -r {temp_dir}")
        os.makedirs(temp_dir)
        docx_zip.extractall(temp_dir)
    
    # Step 2: Locate XML files to process
    xml_files_to_process = [
        "word/document.xml",       # Main document body
        "word/footer1.xml",        # Footer content
        "word/header1.xml"         # Header content
    ]
    
    # Dynamically find all footer and header files
    word_folder = os.path.join(temp_dir, "word")
    for file in os.listdir(word_folder):
        if "footer" in file and file.endswith(".xml"):
            xml_files_to_process.append(f"word/{file}")
        if "header" in file and file.endswith(".xml"):
            xml_files_to_process.append(f"word/{file}")
    
    # Step 3: Replace placeholders in the XML files
    for xml_file in xml_files_to_process:
        file_path = os.path.join(temp_dir, xml_file)
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as file:
                xml_content = file.read()
            
            # First replace in regular text content
            updated_content = replace_placeholders_in_xml(xml_content, placeholder_data)
            
            # Replace placeholders inside shapes/text boxes if any
            updated_content = replace_placeholders_in_shapes(updated_content, placeholder_data)
            
            # Write back the updated content
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
    
    # Step 4: Repackage the updated files into a new .docx
    with zipfile.ZipFile(updated_doc_path, 'w') as updated_docx:
        for root, _, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                archive_name = os.path.relpath(file_path, temp_dir)
                updated_docx.write(file_path, archive_name)
    
    # Clean up temporary files
    shutil.rmtree(temp_dir)


def title_page(doc_path, document_type, username):
    
        # Placeholder data to replace
    placeholder_data = {
        "#companyname#": document_type,
        "#username#": username,
        "#date#": str(date.today()),
    }
    # Execute the function
    process_docx_placeholders(doc_path, updated_doc_path, placeholder_data)
    print(f"Updated document saved as: {updated_doc_path}")
    
    return updated_doc_path

# doc_path = "./Data/SOW_Microsoft_Dynamics_front_page.docx"
# document_type = "Microsoft Dynamics"
# username = "John Doe"
# add_title(doc_path, document_type, username)
