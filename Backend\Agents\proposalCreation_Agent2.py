from langchain_core.prompts import Chat<PERSON><PERSON><PERSON><PERSON><PERSON>plate
from Tools.PPTCreationDynamicFlowTool2 import createDynamicFlowPPT_Tool
from Tools.suggestBusinessProcess import suggest_business_processes
from Tools.router_tools import route_to_company_agent,route_to_proposal_agent
from Tools.suggestModules import suggest_modules_tool
from Tools.timelineTool4 import createTimeLineTool
from Tools.costEstimationTool2 import createCostEstimationTool
from Tools.suggestMigartionWIthLLM import suggest_Data_migrations
from Tools.suggestERP import suggest_ERP_platform
from Tools.company_data_update import comapny_data_update_tool
from Tools.suggestIntegrationWithLLM import suggest_integrations
from Tools.fallback import fallback_tool
from Tools.DocumentCreationStaticTool3 import createDynamicFlowDocument_Tool
from Tools.company_extraction_tool import comapny_info_extration
from Config.azureAI import llm

SYSTEM_PROMPT = """
You are a Router Agent working for a company that provides implementation services for Microsoft Dynamics 365 (D365).

Your responsibility is to decide which **tool** should handle the user's query. The available tools are:

- **company_agent_tool**  
  Use this tool when the query:
  - Asks to list companies
  - Mentions a specific company name
  - Asks about a company’s D365 implementation plan, status, or proposal for a particular company
  - Mentions "company" explicitly in context of D365 implementation

- **proposal_agent_tool**  
  Use this tool when the query:
  - Asks about ERP modules (like Finance, SCM, Sales, etc.)
  - Asks about data migration plans or details
  - Requests business process explanations or improvements
  - Discusses D365 implementation proposals without mentioning a specific company

- **fallback_tool**  
  Use this tool when:
  - The query does not fall into either of the above categories
  - The query is unrelated, ambiguous, or incomplete
  - The user asks for something outside of company information or proposal discussions

**If the query mentions 'implementation' but does not specify a company name**, ask the user:
*"Could you please confirm which company you want to implement D365 for?"*
Decide the appropriate tool to handle the query, and respond accordingly.
Use this space for reasoning and tool routing notes:  

"""

proposalCreation_prompt = ChatPromptTemplate.from_messages([
    ("system", SYSTEM_PROMPT),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

proposalCreation_tools = [
    route_to_company_agent,
    route_to_proposal_agent
    # fallback_tool,
    # suggest_business_processes,
    # suggest_modules_tool,
    # suggest_ERP_platform,
    # suggest_integrations,
    # suggest_Data_migrations,
    # comapny_data_update_tool,
    # createDynamicFlowPPT_Tool,
    # createDynamicFlowDocument_Tool,
    # comapny_info_extration,
    # createTimeLineTool,
    # createCostEstimationTool
    ]


proposalCreation_llm_with_tools =  proposalCreation_prompt | llm.bind_tools(
                                                                    proposalCreation_tools,
                                                                    parallel_tool_calls=False
                                                                    )
