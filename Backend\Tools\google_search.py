import os

from langchain_core.tools import Tool
from langchain_google_community import GoogleSearchAPIWrapper

from langchain_core.tools import tool
from dotenv import load_dotenv

load_dotenv() 

os.environ["GOOGLE_CSE_ID"] = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_SEARCH_API_KEY")


@tool
def googleSearchTool(query:str)-> str:
    """Search company on web ."""
    search = GoogleSearchAPIWrapper()

    google_search_tool = Tool(
        name="google_search",
        description="Search Google for recent results.",
        func=search.run,
    )
    
    # tool.run(query)

    return google_search_tool.run(query)