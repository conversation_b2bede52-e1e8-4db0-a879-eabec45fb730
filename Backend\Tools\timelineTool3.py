from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import io
from utilities.upload_file import upload_file_to_blob


import pandas as pd
from DatabaseCosmos.StatesChecklist import updatetimelineState
from DatabaseCosmos.Timeline import updateTimeline , insert_Timeline, GetTimeline
from DatabaseCosmos.ProjectEstimation import read_ProjectEstimation_State, insert_ProjectEstimation_State, update_ProjectEstimation
from DatabaseCosmos.UserCurrentChat import read_userConversation,upsert_userConversation
from utilities.fetchChat import fetchChatSummary
from Tools.suggestPills import getSuggestedPills
from Timeline.extract_data_from_chat import extract_data
from Timeline.hours_estimate_from_llm import get_all_estimates
from Timeline.get_total_hours import get_total_hours
from Timeline.create_timeline import create_df
from Timeline.color_excel import color_excel
from DatabaseCosmos.resource_state import getResources, initialize_resource, updateResources
import json
from openai import AzureOpenAI
from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.Data_Migration_State import read_DMState
from DatabaseCosmos.Integration_State import read_INTState
from DatabaseCosmos.App_module_State import read_AppModule_State


@tool(return_direct=True)
def createTimeLineTool(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is use to generate Project Timeline and will return a proper structured output with divided tasks"""

    company_info = read_CompanyInfo(id)
    module_info = read_AppModule_State(id)
    integration_info = read_INTState(id)
    dm_info = read_DMState(id)
    
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "timeline",
            "errorkey": "companyNameNotFound"
        })
    
    if not module_info and not integration_info and not dm_info:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "timeline",
            "errorkey": "modules, integrations and Data Migration not selected  by user"
        })

    # get resources
    get_resources = getResources(id)
    if not get_resources:
        initialize_resource(user_id, id)

        #read json file named as "resources.json"
        with open('./Data/resource.json') as f:
            resources = json.load(f)
            f.close()
        updateResources(id, resources)
    else:
        resources = get_resources['resources']
    print(resources)

    resources = [
    {
        "role": "Project Manager",
        "hours": 0.5,
        "weeks": 1,
        "order": 1,
        "phases": "Build",
        "rate": 100
    },
    {
        "role": "Solution Architect",
        "hours": 0.5,
        "weeks": 1,
        "order": 1,
        "phases": "Build",
        "rate": 50
    },]

    # Extract data from chat
    data_dict = extract_data(id)
    estimates = get_all_estimates(data_dict)

    # Get total hours for the build phase
    build_hours = get_total_hours(estimates)
    
    # Create the timeline dataframe
    df = create_df(build_hours, resources)
    createEstimationObject(df , user_id , id)
    # create json for frontend
    json_df = df.to_json(orient='table')
    tabular_data = json.loads(json_df)

    items = GetTimeline(id)
    if items:
        updateTimeline(id, tabular_data)
    else:  
        insert_Timeline(tabular_data, user_id,id)

    # convert the dataframe into colored excel
    excel_file = color_excel(df)

    # Save the document
    file_path = f"{user_id}/{id}/timeline.xlsx"

    # Save the rendered document to an in-memory stream
    output_stream = io.BytesIO()
    excel_file.save(output_stream)
    output_stream.seek(0)  # Reset stream position for uploading

    #generating sas url
    print("generating Excel URL....")
    excel_url = upload_file_to_blob(
                                file_name=file_path,
                                output_stream=output_stream,
                                user_id=user_id,
                                id=id
                                )
    

    suggested_pills= getSuggestedPills(user_id)

    return json.dumps({
                        "id": id,
                        "user_id": user_id,
                        "viewType": "timeline",
                        "tool_name": "timeline",
                        "pills":suggested_pills,
                        "table": tabular_data,
                        "url": {
                            "excel_url" : excel_url,
                        },
                    }
                )



def createEstimationObject(df , user_id , id):
    """
    Analyze project data and return dictionaries for roles, hours, rates, 
    total costs, and additional project metrics, handling NaN and empty values.
    
    Parameters:
    df (pandas.DataFrame): DataFrame containing project role information
    
    Returns:
    dict: A dictionary containing project analysis details
    """
    # Remove rows with NaN or empty values in 'Role' column
    df_cleaned = df.dropna(subset=['Role'])
    df_cleaned = df_cleaned[df_cleaned['Role'].str.strip() != '']

    # Find week columns
    week_columns = [col for col in df.columns if col.startswith('W')]
    weeks = len(week_columns)
    months = weeks / 4

    # Calculate average rate
    average_rate = df_cleaned['Rate'].mean()

    # Extract phase mapping (skip first 4 columns)
    phase_mapping = df.iloc[0, 4:]  # Adjust index based on actual data

    # Extract hours data (excluding the first row)
    df_hours = df.iloc[1:, 4:].astype(float)  # Skip first 4 columns

    # Remove invalid or empty rows (e.g., NaN, zeros)
    df_hours = df_hours[df_hours.sum(axis=1) > 0]  # Removes rows with total hours of 0

    # Assign week columns to their respective phases
    df_hours.columns = phase_mapping.values

    # Sum hours per phase for each week
    phase_weekly_hours = df_hours.groupby(axis=1, by=phase_mapping.values).sum()

    # Sum up total hours for each phase
    total_phase_hours = phase_weekly_hours.sum()

    # Remove "Total Phase Hours" from the dictionary
    if "Total Phase Hours" in total_phase_hours:
        total_phase_hours = total_phase_hours.drop("Total Phase Hours")

    # Calculate phase-wise average rate (default to overall average rate if phase is missing)
    phase_avg_rates = {phase: df_cleaned[phase].mean() if phase in df_cleaned.columns else average_rate 
                       for phase in total_phase_hours.index}

    # Compute total phase cost (rounded to 2 decimal places)
    phase_rates = {phase: round(total_phase_hours[phase] * phase_avg_rates.get(phase, average_rate), 2) 
                   for phase in total_phase_hours.index}

    # Store all results in variables before returning
    resources = dict(zip(df_cleaned['Role'], df_cleaned['Total Hours']))
    role_rates = dict(zip(df_cleaned['Role'], df_cleaned['Rate']))
    role_total_costs = dict(zip(df_cleaned['Role'], df_cleaned['Total Cost']))
    budget = df_cleaned['Total Cost'].sum()
    estimated_hours = df_cleaned['Total Hours'].sum()
    phase_hours = total_phase_hours.to_dict()
    
    item = read_ProjectEstimation_State(id)
    if item:
        update_ProjectEstimation({
            "id": item['id'],
            "chat_id": item['chat_id'],
            "user_id": user_id,
            "Resources": resources,
            "Phases": phase_hours,  
            "Estimated Hours": f"{estimated_hours:,}",
            "months":months,
            "PhaseRate":phase_rates,
            "Budget":f"${budget:,}", 
            "AverageRate":average_rate
        })
    else:
        insert_ProjectEstimation_State(resources,phase_hours,f"{estimated_hours:,}",months,phase_rates,f"${budget:,}",average_rate, user_id,id)



    # Return final dictionary
    # all_estimations =  {
    #     'Resources': resources,
    #     'Budget': budget,
    #     'Estimated Hours': estimated_hours,
    #     'Weeks': weeks,
    #     'months': months,
    #     'AverageRate': round(average_rate,2),
    #     'Phases': phase_hours,
    #     'PhaseRate': phase_rates
    # }
    # print("ESTIMATIONS" , all_estimations)
    # return all_estimations
