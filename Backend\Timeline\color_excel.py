from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Font, Border, Side, Alignment
from openpyxl import load_workbook
import pandas as pd
import io

phase_colors = {
    "Initialization": "C65911",  # Dark Orange
    "Solution Modeling": "BF8F00",  # Gold
    "Build": "00B0F0",  # Bright Blue
    "Solution Testing": "A9D08E",  # Light Green
    "Deployment": "548235",  # Dark Green
    "Support": "8EA9DB",  # Light Blue
}


def color_excel(dataframe):

    # Create an in memory binary file object, and write the dataframe to it.
    in_memory_excel = io.BytesIO()
    dataframe.to_excel(in_memory_excel, index=False)

    # Write the file out to disk to demonstrate that it worked.
    in_memory_excel.seek(0,0)

    #load the workbook
    wb = load_workbook(in_memory_excel)
    ws = wb.active

    # Define a border style
    thin_border = Border(
        left=Side(style="thin"),
        right=Side(style="thin"),
        top=Side(style="thin"),
        bottom=Side(style="thin"),
    )

    # Merge cells for repeating phase names and apply styles
    previous_phase = None
    start_col = None

    for col in ws.iter_cols(min_row=2, max_row=2, min_col=2, max_col=ws.max_column):
        for cell in col:
            phase = str(cell.value).strip()
            if phase in phase_colors:  # Only process phases with defined colors
                if phase != previous_phase:
                    if previous_phase and start_col:
                        # Merge the previous phase columns
                        ws.merge_cells(
                            start_row=2,
                            start_column=start_col,
                            end_row=2,
                            end_column=cell.column - 1,
                        )
                        merged_cell = ws.cell(row=2, column=start_col)
                        merged_cell.value = previous_phase
                        merged_cell.alignment = Alignment(horizontal="center", vertical="center")
                        fill = PatternFill(
                            start_color=phase_colors[previous_phase],
                            end_color=phase_colors[previous_phase],
                            fill_type="solid",
                        )
                        merged_cell.fill = fill
                        merged_cell.font = Font(color="FFFFFF")  # White font color

                        # Apply the phase color to all cells under the merged phase
                        for row in ws.iter_rows(
                            min_row=3, max_row=ws.max_row, min_col=start_col, max_col=cell.column - 1
                        ):
                            for data_cell in row:
                                data_cell.fill = fill
                                data_cell.border = thin_border
                                data_cell.font = Font(color="FFFFFF")  # White font color for data cells

                    # Update for the current phase
                    previous_phase = phase
                    start_col = cell.column

    # Merge the last phase columns
    if previous_phase and start_col:
        ws.merge_cells(
            start_row=2, start_column=start_col, end_row=2, end_column=ws.max_column
        )
        merged_cell = ws.cell(row=2, column=start_col)
        merged_cell.value = previous_phase
        merged_cell.alignment = Alignment(horizontal="center", vertical="center")
        fill = PatternFill(
            start_color=phase_colors[previous_phase],
            end_color=phase_colors[previous_phase],
            fill_type="solid",
        )
        merged_cell.fill = fill
        merged_cell.font = Font(color="FFFFFF")  # White font color

        # Apply the phase color to all cells under the last merged phase
        for row in ws.iter_rows(
            min_row=3, max_row=ws.max_row, min_col=start_col, max_col=ws.max_column
        ):
            for data_cell in row:
                data_cell.fill = fill
                data_cell.border = thin_border
                data_cell.font = Font(color="FFFFFF")  # White font color for data cells

    # Apply borders and handle numbers stored as text
    for row in ws.iter_rows(min_row=3, max_row=ws.max_row, min_col=2, max_col=ws.max_column):
        for cell in row:
            # Attempt to convert values stored as text to numbers
            try:
                cell.value = float(cell.value)
            except (ValueError, TypeError):
                pass  # Keep text values unchanged

            # Ensure cells already colored have borders
            if not cell.fill.fgColor.rgb:  # If no color is applied yeta
                cell.border = thin_border

    # Save the styled Excel file
    # wb.save("./Data/resource_allocation_colored2.xlsx")

    # retrun workbook
    return wb
