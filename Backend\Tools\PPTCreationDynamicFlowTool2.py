from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import io
import pandas as pd
from utilities.create_timeline_chart3 import create_gantt_chart, get_ganttchart_data

from DatabaseCosmos.Timeline import GetTimeline
from utilities.create_timeline_chart2 import create_chart

from DatabaseCosmos.Company_info_State import read_CompanyInfo
from DatabaseCosmos.UserCurrentChat import read_userConversation, upsert_userConversation
from utilities.upload_file import upload_file_to_blob
from utilities.fetchChat import fetchChatSummary
from utilities.ppt_generation_chains import implementation_methodology_chain, introduction_chain, project_scope_chain
from langchain_core.runnables import RunnableParallel

from utilities.convert_csv_to_array import csv_to_array
from utilities.convert_obj_to_array import object_to_2d_array
from utilities.convert_integration_obj_to_array import convert_integration_obj_to_array
from DocumentGeneration.Presentation import PresentationGenerator
from utilities.parse_to_json import parse_to_json_function
from Tools.suggestPills import getSuggestedPills
from DatabaseCosmos.ProjectEstimation import read_ProjectEstimation_State
from DatabaseCosmos.Theme_template_state import read_Template_State
from Tools.PPTDynamicTemplateThemeTool import createDynamicFlowPPT_Tool2
theme_path = "./DocumentGeneration/Presentation/Themes/Quisitive/theme.json"
static_csv_paths = {
    "generalProjectScope": "./Data/GeneralProjectScope.csv",
    "trainingInScope": "./Data/TrainingInScope.csv",
    "DMResponsibilities": "./Data/DMResponsibilities.csv",
    "outOfScope": "./Data/OutOfScope.csv",
}

@tool(return_direct=True)
def createDynamicFlowPPT_Tool(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is use to generate Presentation and wil return you the url to dowload the Presentation, Call this tool when User asked to create Presentation or ppt"""
    
    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "integration",
            "errorkey": "companyNameNotFound"
        })
    
    ppt_state = read_Template_State("ppt")
    state = ppt_state['state']

    if state == "default":
        ppt_url = ""
        docx_url = ""

        document_type = "company_name"
        company_info = read_CompanyInfo(id)
        if company_info:
            document_type = company_info['companyname']
        else:
            return json.dumps({
                "id": id,
                "user_id": user_id,
                "viewType": "error",
                "tool_name":"createDynamicFlowPPT_Tool",
                "errorkey": "companyNameNotFound"
            })
            
        msg_summary = fetchChatSummary(id=id)
        if msg_summary == "":
            msg_summary = "Want to create a SOW for the company: "

        chat_history = fetchChatSummary(
            id=id,
            getChatHistory=False,
            returnRaw=True
        )
        
        response_list = [
            {
                "type": "firstPage",
                "documentTitle": "Microsoft Dynamics 365 - Finance & Operations, Budgetary Price Estimate",
                "companyName": document_type,
            },
        ]

        #parallel execution chain
        parallel_chain = RunnableParallel(
            introduction=introduction_chain, 
            # project_scope=project_scope_chain,
            # implementation_methodology=implementation_methodology_chain
            )
        
        response = parallel_chain.invoke({"context": msg_summary})

        for chain_type, chain_content in response.items():
            # print(chain_type)
            content = chain_content.content
            response_list.extend(parse_to_json_function(content))

        # static content starting from here
        response_list.append(
            {
                "type":"table",
                "title": "General Project Scope",
                "table": csv_to_array(static_csv_paths["generalProjectScope"])
            },
        )

        try:
            response_list.append(
                {
                    "type":"table",
                    "title": "Process In Scope",
                    "table": object_to_2d_array(chat_history["businessprocesses"])
                },
            )
            
        except Exception as e:
            pass

        try:
            response_list.append(
                {
                "type": "table",
                "title": "Application Modules",
                "table": object_to_2d_array(chat_history["modules"])
                },
            )
        except Exception as e:
            pass

        response_list.append(
            {
                "type":"table",
                "title": "Training In Scope",
                "table": csv_to_array(static_csv_paths["trainingInScope"])
            },
        )

        try:
            response_list.extend([
                {
                "type": "table",
                "title": "Data Migration In Scope",
                "table": object_to_2d_array(chat_history["datamigration"])
                },
                {
                    "type":"table",
                    "title": "Data Migration Responsibilities",
                    "table": csv_to_array(static_csv_paths["DMResponsibilities"])
                },  
            ])
        except Exception as e:
            pass

        try:
            response_list.append(
                {
                "type":"table",
                "title": "Integrations",
                "table": convert_integration_obj_to_array(chat_history["integration"])
                },    
            )
        except Exception as e:
            pass

        response_list.extend([
            {
            "type":"table",
            "title": "Out of Scope",
            "table": csv_to_array(static_csv_paths["outOfScope"])
            },
            {
                "type": "title",
                "title": "Implementation Approach",
                "subtitle": "",
            }
        ])

        try:
            # get image
            timelinedata = GetTimeline(id=id)
            # image = create_chart(timelinedata['timeline'])
            
            timeline_dict = timelinedata['timeline']
            timeline_df = pd.DataFrame.from_dict(timeline_dict['data'])

            ganttchart_data = get_ganttchart_data(timeline_df)
            image = create_gantt_chart(ganttchart_data)

            image_stream = io.BytesIO(image)
            projectestimation_data = read_ProjectEstimation_State(id)

            response_list.extend([
                    {
                        "type": "title",
                        "title": "Proposal",
                        "subtitle": "Timeline & Cost Estimates"
                        },
                    {
                        "type": "image",
                        "title": "Proposal | Project Timeline",
                        "imagePath": image_stream,
                        },
                    {
                        "type": "costing",
                        "title": "Proposal | Professional Services",
                        "duration": str(projectestimation_data['months']),
                        "budget": f"{projectestimation_data['Budget']}" if not projectestimation_data['Budget']=="" else "N/A",
                        "estimatedHours": str(projectestimation_data['Estimated Hours']),  
                        "totalTeamMembers": str(len(projectestimation_data['Resources'])),
                        },
                ])
        except Exception as e:
            pass

        response_list.append(
            {
                "type": "title",
                "title": "Questions",
                "subtitle": "",
            }
        )

        presentation_generator = PresentationGenerator(theme_path)
        presentation_generator.generate_presentation(response_list)
        # presentation_generator.prs.save("generated_presentation.pptx")

        
        # Save the document
        file_path = f"{user_id}/{id}/{document_type}_presentation.pptx"

        # Save the rendered document to an in-memory stream
        output_stream = io.BytesIO()
        presentation_generator.prs.save(output_stream)
        output_stream.seek(0)  # Reset stream position for uploading

        #generating sas url
        print("generating PPT URL....")
        ppt_url = upload_file_to_blob(
                                    file_name=file_path,
                                    output_stream=output_stream,
                                    user_id=user_id,
                                    id=id
                                    )
        # get suggested pills 
        suggested_pills = getSuggestedPills(user_id)

        return json.dumps({
                            "id": id,
                            "user_id": user_id,
                            "viewType": "documentcreation",
                            "tool_name":"createDynamicFlowPPT_Tool",
                            "url": {
                                "ppt_url" : ppt_url,
                                "docx_url": docx_url,
                            },
                            "pills": suggested_pills
                        }
                    )
    else:
        print("Generate Dynamic Presentation")

        output = createDynamicFlowPPT_Tool2(user_id=user_id, id=id)
        return output