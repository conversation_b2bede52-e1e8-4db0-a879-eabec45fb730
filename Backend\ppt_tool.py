      
from pptx import Presentation

from langchain_core.tools import tool
import json
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
import io

from DatabaseCosmos.Timeline import GetTimeline
from utilities.create_timeline_chart import create_chart

from DatabaseCosmos.Company_info_State import read_CompanyInfo
from utilities.fetchChat import fetchChatSummary
from utilities.ppt_generation_chains import introduction_chain
from langchain_core.runnables import RunnableParallel

from utilities.convert_csv_to_array import csv_to_array
from utilities.convert_obj_to_array import object_to_2d_array


from utilities.parse_to_json import parse_to_json_function
from Tools.suggestPills import getSuggestedPills
from DatabaseCosmos.ProjectEstimation import read_ProjectEstimation_State

from pptx.util import Pt

from pptx import Presentation
from pptx.util import Inches
import io
from pptx.dml.color import RGBColor


theme_path = "./DocumentGeneration/Presentation/Themes/Quisitive/theme.json"
static_csv_paths = {
    "generalProjectScope": "./Data/GeneralProjectScope.csv",
    "trainingInScope": "./Data/TrainingInScope.csv",
    "DMResponsibilities": "./Data/DMResponsibilities.csv",
    "outOfScope": "./Data/OutOfScope.csv",
}



def content():
    
    ppt_url = ""
    docx_url = ""
    
    id = "20250113213633-0a570ecc3035"
    user_id = "test12"

    document_type = "company_name"
    company_info = read_CompanyInfo(id)
    if company_info:
        document_type = company_info['companyname']
    else:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name":"createDynamicFlowPPT_Tool",
            "errorkey": "companyNameNotFound"
        })
        
    msg_summary = fetchChatSummary(id=id)
    if msg_summary == "":
        msg_summary = "Want to create a SOW for the company: "

    chat_history = fetchChatSummary(
        id=id,
        getChatHistory=False,
        returnRaw=True
    )
    
    response_list = [
        {
            "type": "firstPage",
            "documentTitle": "Microsoft Dynamics 365 - Finance & Operations, Budgetary Price Estimate",
            "companyName": document_type
        }
    ]
    
    #parallel execution chain
    parallel_chain = RunnableParallel(
        introduction=introduction_chain
        )
    
    response = parallel_chain.invoke({"context": msg_summary})

    for chain_type, chain_content in response.items():
        # print(chain_type)
        content = chain_content.content
        response_list.extend(parse_to_json_function(content))

    # static content starting from here
    response_list.append(
        {
            "type":"table",
            "title": "General Project Scope",
            "table": csv_to_array(static_csv_paths["generalProjectScope"])
        },
    )

    try:
        response_list.append(
            {
                "type":"table",
                "title": "Process In Scope",
                "table": object_to_2d_array(chat_history["businessprocesses"])
            },
        )
        
    except Exception as e:
        pass

    try:
        response_list.append(
            {
            "type": "table",
            "title": "Application Modules",
            "table": object_to_2d_array(chat_history["modules"])
            },
        )
    except Exception as e:
        pass

    response_list.append(
        {
            "type":"table",
            "title": "Training In Scope",
            "table": csv_to_array(static_csv_paths["trainingInScope"])
        },
    )

    try:
        response_list.extend([
            {
            "type": "table",
            "title": "Data Migration In Scope",
            "table": object_to_2d_array(chat_history["datamigration"])
            },
            {
                "type":"table",
                "title": "Data Migration Responsibilities",
                "table": csv_to_array(static_csv_paths["DMResponsibilities"])
            },  
        ])
    except Exception as e:
        pass

    try:
        response_list.append(
            {
            "type":"table",
            "title": "Integrations",
            "table": object_to_2d_array(chat_history["integration"])
            },    
        )
    except Exception as e:
        pass

    response_list.extend([
        {
        "type":"table",
        "title": "Out of Scope",
        "table": csv_to_array(static_csv_paths["outOfScope"])
        },
        {
            "type": "title",
            "title": "Implementation Approach",
            "subtitle": "",
        }
    ])

    try:
        # get image
        timelinedata = GetTimeline(id=id)
        # print(timelinedata)
        image = create_chart(timelinedata['timeline'])
        image_stream = io.BytesIO(image)

        projectestimation_data = read_ProjectEstimation_State(id)

        response_list.extend([
                {
                    "type": "title",
                    "title": "Proposal",
                    "subtitle": "Timeline & Cost Estimates"
                    },
                {
                    "type": "image",
                    "title": "Proposal | Project Timeline",
                    "imagePath": image_stream,
                    },
                {
                    "type": "costing",
                    "title": "Proposal | Professional Services",
                    "duration": str(projectestimation_data['months']),
                    "budget": f"{projectestimation_data['Budget']}" if not projectestimation_data['Budget']=="" else "N/A",
                    "estimatedHours": str(projectestimation_data['Estimated Hours']),  
                    "totalTeamMembers": str(len(projectestimation_data['Resources'])),
                    },
            ])
    except Exception as e:
        pass

    response_list.append(
        {
            "type": "title",
            "title": "Questions",
            "subtitle": "",
        }
    )
    
    print("\n\n\n\n ------------ response_list ------------", response_list)
    return response_list


def add_table_to_slides(presentation, data, title_text, max_rows_per_slide=5):
    """
    Adds a table to the presentation, splitting it across slides if necessary.
    Removes slide background and unnecessary placeholders.
    """
    # Define table position and dimensions
    left = Inches(1)
    top = Inches(2)
    width = Inches(11)
    height = Inches(2.5)
    font_size = 16

    # Determine the total number of rows and columns
    rows, cols = len(data), len(data[0])
    current_row = 1  # Start from the first row of data (header row is separate)

    while current_row < rows:
        # Add a new slide for the table
        slide = presentation.slides.add_slide(presentation.slide_layouts[1])  # Title and Content layout

        # Remove the slide background
        slide.background.fill.solid()
        slide.background.fill.fore_color.rgb = RGBColor(255, 255, 255)  # White background

        # Remove other placeholders except the title
        for shape in slide.shapes:
            if shape.is_placeholder and shape != slide.shapes.title:
                sp = shape._element
                sp.getparent().remove(sp)

        # Add title to the slide
        title = slide.shapes.title
        title.text = title_text

        # Determine rows to add to this slide
        start_row = current_row
        end_row = min(current_row + max_rows_per_slide, rows)

        # Add table
        table = slide.shapes.add_table(end_row - start_row + 1, cols, left, top, width, height).table

        # Add the header row (always the first row of the data)
        for col_idx, header in enumerate(data[0]):
            cell = table.cell(0, col_idx)
            cell.text = str(header)
            # Adjust font size
            for paragraph in cell.text_frame.paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(font_size)

        # Add the data rows
        for table_row, data_row in enumerate(data[start_row:end_row], start=1):
            for col_idx, cell_value in enumerate(data_row):
                cell = table.cell(table_row, col_idx)
                cell.text = str(cell_value)
                # Adjust font size
                for paragraph in cell.text_frame.paragraphs:
                    for run in paragraph.runs:
                        run.font.size = Pt(font_size)

        # Update current_row for the next iteration
        current_row = end_row

def add_image_to_slide(slide, image_stream, title_text):
    """Adds an image to the slide and removes unnecessary placeholders."""
    # Set the slide title
    title = slide.shapes.title
    if title:
        title.text = title_text

    # Remove other placeholders (e.g., subtitle or content placeholders)
    for shape in slide.shapes:
        if shape.is_placeholder and shape != title:  # Keep the title, remove others
            sp = shape._element
            sp.getparent().remove(sp)

    # Define image position and size
    left = Inches(1.5)
    top = Inches(2)
    height = Inches(5)
    
    # Add image
    slide.shapes.add_picture(image_stream, left, top, height=height)


def add_paragraph_to_slide(slide, title_text, paragraph_text):
    """Adds a paragraph to the slide."""
    title = slide.shapes.title
    title.text = title_text

    # Add the paragraph to the content placeholder
    content = slide.placeholders[1]
    content.text = paragraph_text



def ppt_gen(response_list):
    # Load the existing PowerPoint presentation
    # ppt_path = "./Data/SampledesignforthemePPT.pptx"
    ppt_path = "./Data/ppt_theme2.pptx"
    presentation = Presentation(ppt_path)

    for item in response_list:
        # Handle title slides
        if item["type"] == "title":
            slide = presentation.slides.add_slide(presentation.slide_layouts[0])  # Title Slide layout
            title = slide.shapes.title
            subtitle = slide.placeholders[1]
            title.text = item["title"]
            subtitle.text = item.get("subtitle", "")
        
          # Handle paragraph slides
        elif item["type"] == "paragraph":
            slide = presentation.slides.add_slide(presentation.slide_layouts[1])  # Title and Content layout
            add_paragraph_to_slide(slide, item["title"], item["paragraph"])


        elif item["type"] == "table":
            
            add_table_to_slides(presentation, item["table"], item["title"], max_rows_per_slide=5)

        # Handle image slides
        elif item["type"] == "image":
            slide = presentation.slides.add_slide(presentation.slide_layouts[1])  # Title and Content layout
            add_image_to_slide(slide, item["imagePath"], item["title"])

        # Handle costing slides
        elif item["type"] == "costing":
            slide = presentation.slides.add_slide(presentation.slide_layouts[1])  # Title and Content layout
            title = slide.shapes.title
            content = slide.placeholders[1]
            title.text = item["title"]
            costing_details = (
                f"Duration: {item['duration']} months\n"
                f"Budget: {item['budget']}\n"
                f"Estimated Hours: {item['estimatedHours']}\n"
                f"Total Team Members: {item['totalTeamMembers']}"
            )
            content.text = costing_details

    # Save the modified presentation
    updated_ppt_path = "./Data/ppt6.pptx"
    presentation.save(updated_ppt_path)
    print(f"Presentation saved to {updated_ppt_path}")

# Example call


resp_list = content()
print("\n\n\n resp_list: ", resp_list)
ppt_gen(resp_list)





