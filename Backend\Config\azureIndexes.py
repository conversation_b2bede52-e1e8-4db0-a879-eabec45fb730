from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
import os
from dotenv import load_dotenv


load_dotenv()

search_client = SearchClient(
    endpoint= os.getenv("INDEXES_ENDPOINT"),
    index_name=os.getenv("INDEX_NAME1"),
    credential=AzureKeyCredential(os.getenv("INDEXES_KEY"))
)

search_client2 = SearchClient(
    endpoint=os.getenv("INDEXES_ENDPOINT"),
    index_name=os.getenv("INDEX_NAME2"),
    credential=AzureKeyCredential(os.getenv("INDEXES_KEY"))
)
