import os
from Config.blobService import blob_service_client
import io
from dotenv import load_dotenv
load_dotenv()

def download_template_from_blob(blob_name:str):
    blob_client = blob_service_client.get_blob_client(
                                                    container = os.getenv("Blob_output_container_name"), 
                                                    blob = blob_name
                                                    )
    
    download_stream = blob_client.download_blob()
    template_bytes = io.BytesIO(download_stream.readall())
    template_bytes.seek(0)

    return template_bytes
