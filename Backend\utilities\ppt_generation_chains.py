from LLMChains.Base_LLMChain_ModelClass import BaseLLMChainClass
from langchain_core.prompts import Chat<PERSON>romptTemplate
from Config.azureAI import llm

introduction_chain_prompt = """
        You are an expert in writing Presentation Slides. Use the information provided in the 'Context' to craft a precise and engaging "Executive Summary" and "Introduction" for the company. Highlight key details such as the company's name, industry, core offerings, goals, and unique value propositions. Ensure the tone is professional and suitable for inclusion in business presentations.
        the Presentation is a Statement of Work, created by 'Quisitive'. Offering Integrations of Microsoft Dynamics 365.

        1. Write the purpose and objectives of the document.
        2. Highlight the approach or methodology mentioned in the document (e.g., Agile, phased, etc.).
        3. Summarize the key deliverables, scope, and outcomes expected from the project.

        Remeber to write it precise and to the point for the presentation slide.

        Write the 3 slides:
        1) Title Page: 'Our Understanding'
        2) Executive Summary
        3) Introduction
        4) Business Needs and Drivers (list form with little details for e.g: Operational Inefficiencies: <defintion>, Limited Visibility: <definition>, etc.)

        the output should be a json object with the following format:
        1) "type": "title", "title": <text>, "subtitle": ""
        2) "type": "paragraph", "title": <text>, "paragraph": <paragraph>
    """
# introduction_chain = BaseLLMChainClass(system_prompt=introduction_chain_prompt,)
into_chat_Propmpt_template = ChatPromptTemplate.from_messages(
                [
                    ("system", introduction_chain_prompt),
                    ("human", "Context: '''{context}'''")
                ]
            )

introduction_chain = into_chat_Propmpt_template | llm

project_scope_prompt = """
        You are an expert in creating project documentation Slides. Based on the provided 'Context', generate the "Project Objectives and Scope" section.

        Must Include Slides:
        - Business Processes (tabular form)
        - Application Modules/Components In-Scope (tabular form)
        - Processes In-Scope (tabular form) 
        - Integrations (tabular form)
        - Data Migration (tabular form)
        - Data Migration Responsbilities (tabular form), add headings (Task, Consultant, Customer, Required by), assign the task to the 'Consultant' or 'Customer' by add 'X' to any one of them. also 'Required by' should be phases of the project.
        - Out of Scope (tabular form)    

        Remeber to write it precise and to the point for the presentation slide.

        the output should be a json object with the following format:
        1) "type": "table", "title": <text>, "table": <2D array of string>
        2) "type": "paragraph", "title": <text>, "paragraph": <paragraph>
    """
# project_scope_chain = BaseLLMChainClass(system_prompt=project_scope_prompt)
project_scope_chat_Propmpt_template = ChatPromptTemplate.from_messages(
                [
                    ("system", project_scope_prompt),
                    ("human", "Context: '''{context}'''")
                ]
            )

project_scope_chain = project_scope_chat_Propmpt_template | llm

implementation_methodology_prompt = """
        You are an expert in creating project documentation Slides. Based on the provided 'Context', generate the "Implementation Methodology" section.
        the Presentation is a Statement of Work, created by 'Quisitive'. Offering Integrations of Microsoft Dynamics 365.
        
        Include Slides:
        - Title Slide: 'Implementation Methodology'
        - Sure Step Methodology
        - Implementation Approach (tabular form) add headings (Build, Implement, Replicate)

        Remeber to write it precise and to the point for the presentation slide.

        the output should be a json object with the following format:
        1) "type": "title", "title": <text>, "subtitle": ""
        2) "type": "table", "title": <text>, "table": <2D array of string>
        3) "type": "paragraph", "title": <text>, "paragraph": <paragraph>
    """
# implementation_methodology_chain = BaseLLMChainClass(system_prompt=implementation_methodology_prompt)
implementation_methodology_chat_Propmpt_template = ChatPromptTemplate.from_messages(
                [
                    ("system", implementation_methodology_prompt),
                    ("human", "Context: '''{context}'''")
                ]
            )

implementation_methodology_chain = implementation_methodology_chat_Propmpt_template | llm


project_approach_and_timeline_prompt = ""
project_approach_and_timeline_chain = BaseLLMChainClass(
                                system_prompt=project_approach_and_timeline_prompt
                                )

fees_and_costing_prompt = ""
fees_and_costing_chain = BaseLLMChainClass(
                                system_prompt=fees_and_costing_prompt
                                )
