import os
from dotenv import load_dotenv
import pandas as pd
# from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.tools import tool
import json
from Tools.WebscrappingAndCleaning import scrapping
import re,requests
import markdown2
from docx import Document
from bs4 import BeautifulSoup
from utilities.upload_file import upload_file_to_blob, generate_file
from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation
from DatabaseCosmos.StatesChecklist import updateERPState,validation,States,updateAppModState
from DatabaseCosmos.Company_info_State import insert_Companyinfo_State,read_CompanyInfo
from utilities.get_erp import get_erp
from DatabaseCosmos.ERP_State import insert_ERP_State, read_ERPState
from LLMChains.Base_LLMChain_ModelClass import BaseLLMChainClass
from StructuredSchemas.erp_extraction_schema import ERPStructureModel
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated
from DatabaseCosmos.Webscrapping import read_webscrappingD<PERSON>,insert_webscrapping
from Config.azureAI import llm
 
from DatabaseCosmos.UserCurrentChat import upsert_userConversation,read_userConversation

from Tools.suggestPills import getSuggestedPills
import os
from openai import AzureOpenAI
from utilities.openai_services import OpenAIService
 
from langchain_core.tools import Tool
from langchain_google_community import GoogleSearchAPIWrapper
import io
from langchain_core.tools import tool
from dotenv import load_dotenv
from DocumentGeneration.Document import DocumentGenerator_base_template
from utilities.parse_to_json import parse_to_json_function_doc
from DatabaseCosmos.Buisness_process_State import read_BPState
load_dotenv()
 
os.environ["GOOGLE_CSE_ID"] = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_SEARCH_API_KEY")
 
 
load_dotenv()
conversation_histories = {}
 
# Initialize OpenAI client
endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
 
 
 
client = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    azure_endpoint=endpoint,
    api_version="2024-08-01-preview"
)
 
erp_extraction_system_prompt = f"""
                You are provided a company's data
Suggest the ERP Platform of Microsoft according to the company with company info object
Read the given company data precisely, then suggest up to 5 ERP dynamics 365.
Remember to return just the business process IDs
Transform the given suggestion into the structured output provided.
"""
 
erp_extraction_chain = BaseLLMChainClass(
                                            erp_extraction_system_prompt,
                                            ERPStructureModel
                                            )
 
@tool(return_direct=True)
def suggest_ERP_platform(user_id: Annotated[str, InjectedToolArg], id: Annotated[str, InjectedToolArg]):
    """This tool is used to select / add / recommend / suggest / scope of the ERP Platform"""
    
    company_info = read_CompanyInfo(id)
    if not company_info:       
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "erp_module",
            "errorkey": "companyNameNotFound"
        })
    
    bp_info = read_BPState(id)
    if not bp_info:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "error",
            "tool_name": "erp_module",
            "errorkey": "busniessProcessNotFound"
        })
    

    updateAppModState(user_id=user_id,value="False")
    # get suggested pills
    suggested_pills= getSuggestedPills(user_id)
 
    erp_testdata = read_ERPState(id)
    
    #get Costing
    licensing_cost = read_webscrappingData(user_id=user_id)
    
    if licensing_cost is None:
        licensing_cost=scrapping()
        insert_webscrapping(user_id=user_id,costing=licensing_cost)

    # get document. 
    docx_file_path = f"Comparision_document.docx"

    erp_file_url = generate_file(user_id, id, docx_file_path)

    if erp_testdata:
    # if False:
        return json.dumps({
            "id": id,
            "user_id": user_id,
            "viewType": "erp",
            "tool_name": "erp_module",
            "title": "ERP Platform",
            "type": "Checkbox",
            "description": "Microsoft offers multiple ERP platform manufacturing organizations.",
            "document":erp_file_url,
            "fields": erp_testdata[0]["items"],
            "pills": suggested_pills
        })  
    else:
        print(company_info)
    


        # introduction_prompt= [
        #     {
        #         "role": "system",
        #         "content": """
        #         'Microsoft offers multiple ERP platform manufacturing organizations. Based on the size of the organization, 
        #         complexity of food processing, distributed manufacturing locations, and FDA regulatory and compliance requirements,
        #          it is recommended to implement Dynamics 365 Finance and Supply Chain as an ERP platform'. \n      
        #         Write a similar introductory paragraph for a comparison document recommending Microsoft Dynamics 365 for a company. 
        #         based on above given paragraph with using company information given in user input. paragraph should start from Microsoft offers multiple ERP platform...."""},
        #     {
        #         "role": "user",
        #         "content": f"Company information to create comparision document max 50 words:{company_info}"
        #     }
        #     ]
        
        # intro_response = openAIsearch(client=client,chathistory=introduction_prompt)
        intro_response = f"""Microsoft offers multiple ERP platforms for manufacturing organizations."""
        erp_list= [{
                "type": "Checkbox",
                "label": "Dynamics 365 Finance and Supply Chain",
                "isChecked": False,
                "id": "1",
                "group": "Dynamics 365 Finance and Supply Chain",
                "description": "Manage your finances and supply chain operations with Dynamics 365 Finance and Supply Chain."
            },
                {
                "type": "Checkbox",
                "label": "Dynamics 365 Business Central",
                "isChecked": False,
                "id": "2",
                "group": "Dynamics 365 Business Central",
                "description": "Streamline your small to medium-sized business operations with Dynamics 365 Business Central."
            },
        ]

        company_name = company_info['companyname']
        
        comaprision_system_msg = [
            {
                "role": "system",
                "content": """You are an expert assistant capable of providing detailed and structured comparisons 
for enterprise software solutions. Based on the provided company information and licensing details 
as dynamic variables, you will create a comprehensive comparison table between Dynamics 365 Finance 
and Supply Chain Management and Dynamics 365 Business Central. Also do add a bit of information before the tablea and after the table but has to be relevant and unique

The table must cover some of the following aspects, related to the context:

1. **Target Audience**
2. **Licensing Cost and Structure**
3. **Core Functionalities**
4. **Advanced Functionalities**
5. **Industry Fit / Best-Suited Company Types**
6. **Scalability and Growth Potential**
7. **Implementation Time and Complexity**
8. **Deployment Options (Cloud, On-Premises, Hybrid)**
9. **User Interface and Usability**
10. **Customization Capabilities**
11. **Third-party Integration Support**
12. **Microsoft Ecosystem Integration (Power Platform, Azure, etc.)**
13. **Automation and AI Features**
14. **Compliance and Regulatory Support**
15. **Security Features and Certifications**
16. **Localization and Multi-Currency Support**
17. **Supply Chain Management Capabilities**
18. **Financial Management Depth (Multi-entity, Consolidation, etc.)**
19. **Inventory and Warehouse Management**
20. **Project Management Capabilities**
21. **Manufacturing Capabilities**
22. **CRM and Sales Features**
23. **Reporting and Business Intelligence (BI) Tools**
24. **Mobile Access and Remote Work Support**
25. **User Training and Learning Resources**
26. **Partner Ecosystem and Community Support**
27. **Upgrade and Maintenance Effort**
28. **Total Cost of Ownership (TCO)**
29. **Return on Investment (ROI)**
30. **Future-proofing and Innovation Roadmap**

Ensure the information is accurate, clearly structured, and suitable for decision-makers comparing ERP solutions in various industries and scales. Tailor explanations to match the company’s size, goals, and regulatory needs where applicable.

Remember to strictly follow the JSON schema mentioned below to create output. Do not include any extra characters or encode any text in quotes ".
create a proper JSON object with the following format:
1) "type": "heading", "level": level of the heading in numbers (integer) i.e 1,2 or 3 according to the numbers of # in heading text, "text": <text>. Note one heading of level:1 is mandatory with the text "ERP Comparison Document for <company_name>"
2) "type": "paragraph", "title":"", "text": <text>
3) "type": "table", "title": <text>, "data": <2D array of string>
4) "type": "list", "title": <text>, "data": <2D array of string>
    
Always output JSON adhering to the mentioned schema and always enclose the whole JSON object in [] brackets and ensure the proper JSON structure output.


"""},
            {
                "role": "user",
                "content": f"""Please generate a detailed comparison table between Dynamics 365 Finance and Supply Chain Management
                and Dynamics 365 Business Central based on the provided details.
                The comparison should focus on features, pricing (with all costs in one row and should not contain <br> tag), 
                and how each product suits the needs of a large-scale company like Dell, 
                considering different licensing tiers.
                        Company information:{company_info} ,
                        lisencing cost:{licensing_cost},
                        company_name: {company_name}
                """
                }
            ]
        
        response = openAIsearch(client=client,chathistory=comaprision_system_msg)
        print("response2" , response)
        content = []
        try:
            # Handle case where response might be wrapped in markdown code blocks
            if "```json" in response:
                json_content = response.split("```json")[1].split("```")[0].strip()
                response_json = json.loads(json_content)
            else:
                response_json = json.loads(response)
            
            # Now response_json is a proper JSON object
            print(response_json)
        except json.JSONDecodeError:
            print("Failed to parse response as JSON")
                
        content = response_json
        
        theme_path = "./DocumentGeneration/Document/Themes/Quisitive/theme.json"
        file_path = "./base/base_templates_modified/ERP_Comparison.docx"

        
        content_values = {}
        

        document_generator = DocumentGenerator_base_template(theme_path, content_values)
        doc = document_generator.generate_document_from_base_template(content, file_path)
        
        output_stream = io.BytesIO()
        doc.save(output_stream)
        output_stream.seek(0)  # Reset stream position for uploading

        #Generate SAS URL by uploading the in-memory file
        docx_url = upload_file_to_blob(file_name=docx_file_path, output_stream=output_stream, user_id=user_id, id=id)
        #print(docx_url)
        # docx_url=generateSasURL()
        #erp_list = response["erp_list"],

        insert=insert_ERP_State(chat_id=id,user_id=user_id,erp_list=erp_list)
        print("database insertion on Data Migration state completed")
        #print(insert)

        #Intitializing Company Search State in DB True
        #print("updating BP state to true in db -----------------------------------")
        updateERPState(user_id=user_id,value="True")
        
    
        return json.dumps({
                    "id": id,
                    "user_id": user_id,
                    "viewType": "erp",
                    "tool_name": "erp_module",
                    "title": f"{company_info['companyname']} -> ERP Platform",
                    "type": "Checkbox",
                    "description": f"{intro_response}",
                    "fields": erp_list,
                    "document":docx_url,
                    "pills": suggested_pills
                })

def openAIsearch(client,chathistory):

    try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=chathistory,
                #max_tokens=1000,
                temperature=0.7,
                # top_p=0.95,
                # frequency_penalty=0,
                # presence_penalty=0
            )
            #print("response")
            return response.choices[0].message.content
    except Exception as e:
            raise Exception(f"OpenAI API request failed: {str(e)}")
    
def webScrapping(url,id):
    headers = {
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:115.0) Gecko/20100101 Firefox/115.0"
    }
    
    res = requests.get(url, headers=headers)
    soup = BeautifulSoup(res.text, 'html.parser')
    
    button_tags = soup.find_all('a')
    for button in button_tags:
        button.decompose()
    
    #print(soup.find("div", {"id": id}).get_text(strip=True, separator='\n'))
    return soup.find("div", {"id": id}).get_text(strip=True, separator='\n')
