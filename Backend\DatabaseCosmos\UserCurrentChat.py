import logging
import random,uuid
import os
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from dotenv import load_dotenv
from datetime import datetime
from Config.cosmosdb import database

# Load environment variables
load_dotenv()

container = database.create_container_if_not_exists(
    id="CurrentChat",
    partition_key=PartitionKey(path='/user_id', kind='Hash')
) 


# History": [
#         {
#             "role": "user",
#             "content": "tell Independance day of pakistan",
#             "state":1

#         },
#         {
#             "role": "assistant",
#             "content": "Independence Day of Pakistan is celebrated annually on August 14th. This day marks the anniversary of Pakistan's independence from British rule in 1947. It was on this day that the country was declared a sovereign state, following the end of British Raj and the partition of India. The day is a national holiday in Pakistan and is celebrated with various events, including flag hoisting ceremonies, parades, cultural performances, and fireworks. The founder of Pakistan, <PERSON>, played a pivotal role in the creation of the country, and his efforts are commemorated on this day."
#             "state":1       
#  }
#     ]
def create_userConversation(user_id,history):
                 
    now = datetime.now()
    guid = uuid.uuid4()
    id = f"{now.strftime('%Y%m%d%H%M%S')}-{guid.hex[:12]}"
    logging.info("File ID created successfully.")

            
    item = {
                            'id': id,
                            'user_id':user_id,
                            'createdAt':datetime.utcnow().isoformat(),
                            'History':history                           
                            }
            
    print("new Chat created")
           
            
    container.create_item(body=item)
    print(f'Created new item with Id {id}')
    return item

def read_userConversation(id):
    logging.info(f"reading user conversation with user Current session id :{id}")
    """
    Get Specific Chat history.
    """
    print('\n1.4 Querying for an  Item by Id\n')
    try:
        logging.info('Applying query.') 
        query = f"SELECT * FROM c WHERE c.id = '{id}'"
        results :dict= []
        for item in container.query_items(query=query, enable_cross_partition_query=True):
                    results.append({
                        'id': item['id'],
                        'user_id': item['user_id'],
                        'createdAt':item.get('createdAt'),
                        'History':item.get('History')
                    })
        logging.info(f"Retrieved (query) .")
        return results[0]
    except Exception as e:
         print("Error" ,e)
         print("Read Failed!")
         return None

def upsert_userConversation(conversation):
    """
    For upserting call read_userConversation , update item and return in parameter with user id.
    """
    logging.info("updating current user session ")
    print("updating user history on session id")
    try:
        #responsedata : dict=  read_userConversation(user_id)
        response = container.upsert_item(body=conversation)
        print('Upserted Item\'s Id is {0}'.format(response['id']))
    except Exception as e:
         print("Error" ,e)
         print("Upsert Failed!")
         return None
       



            